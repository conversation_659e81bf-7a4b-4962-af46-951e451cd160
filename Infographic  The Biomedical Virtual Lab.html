<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infographic: The Biomedical Virtual Lab</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap');
        .gradient-bg {
            background: linear-gradient(135deg, #1E2E5A, #3B77BC);
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }
        .flowchart-step {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 4px solid #FBB03B;
            color: #1E2E5A;
            font-weight: 700;
            background-color: white;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }
        .flowchart-arrow {
            flex-grow: 1;
            height: 4px;
            background-color: #FBB03B;
            margin: 0 -2px;
        }
        .module-card {
            background-color: #ffffff;
            border-left: 5px solid #00A1E4;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .chart-container {
            position: relative; 
            width: 100%; 
            max-width: 900px; 
            margin-left: auto; 
            margin-right: auto; 
            height: 450px;
            max-height: 50vh;
        }
         .mini-chart-container {
            position: relative; 
            width: 100%; 
            height: 200px;
        }
    </style>
</head>
<body class="bg-gray-50 text-slate-700">

    <!-- PALETTE: Brilliant Blues (#00A1E4, #3B77BC, #263C8A, #1E2E5A) with Yellow Accent (#FBB03B) -->
    <!-- CONFIRMATION: NEITHER Mermaid JS NOR SVG were used in this output. All charts are rendered using Chart.js on Canvas. All diagrams are built with HTML/CSS. -->

    <header class="gradient-bg text-white text-center py-20 px-4">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl md:text-6xl font-black mb-4">The Interactive Biomedical Virtual Lab</h1>
            <p class="text-xl md:text-2xl text-blue-200">A comprehensive hands-on learning environment for future biomedical engineers.</p>
        </div>
    </header>

    <main class="max-w-7xl mx-auto p-4 md:p-8">

        <section class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center -mt-16 mb-20">
            <div class="stat-card p-8 rounded-2xl text-white">
                <p class="text-6xl font-black text-amber-400">12</p>
                <p class="mt-2 text-xl font-bold">Core Learning Modules</p>
            </div>
            <div class="stat-card p-8 rounded-2xl text-white">
                <p class="text-6xl font-black text-amber-400">75+</p>
                <p class="mt-2 text-xl font-bold">Hands-On Experiments</p>
            </div>
            <div class="stat-card p-8 rounded-2xl text-white">
                <p class="text-6xl font-black text-amber-400">10+</p>
                <p class="mt-2 text-xl font-bold">Virtual Instruments</p>
            </div>
        </section>

        <section class="bg-white p-6 md:p-10 rounded-2xl shadow-lg mb-20">
            <h2 class="text-3xl font-bold text-center text-slate-800 mb-2">Mastering Core Concepts</h2>
            <p class="text-lg text-center text-slate-600 mb-10 max-w-3xl mx-auto">The curriculum emphasizes foundational skills by revisiting key experiments across different biomedical applications. This chart shows the frequency of core experiment types throughout all 12 modules.</p>
            <div class="chart-container">
                <canvas id="coreConceptsChart"></canvas>
            </div>
        </section>

        <section class="py-12 mb-20">
            <h2 class="text-3xl font-bold text-center text-slate-800 mb-2">A Typical Experiment Flow</h2>
            <p class="text-lg text-center text-slate-600 mb-12 max-w-3xl mx-auto">Every experiment follows a structured, hands-on process designed to build practical skills from circuit theory to data analysis.</p>
            <div class="flex flex-col md:flex-row items-center justify-center w-full">
                <div class="flowchart-step">1. Build<br>Circuit</div>
                <div class="flowchart-arrow hidden md:block"></div>
                <div class="w-4 h-12 md:hidden bg-amber-400 my-2"></div>
                <div class="flowchart-step">2. Connect<br>Instruments</div>
                <div class="flowchart-arrow hidden md:block"></div>
                <div class="w-4 h-12 md:hidden bg-amber-400 my-2"></div>
                <div class="flowchart-step">3. Test &<br>Measure</div>
                <div class="flowchart-arrow hidden md:block"></div>
                <div class="w-4 h-12 md:hidden bg-amber-400 my-2"></div>
                <div class="flowchart-step">4. Analyze<br>Results</div>
            </div>
        </section>

        <section class="bg-white p-6 md:p-10 rounded-2xl shadow-lg mb-20">
            <h2 class="text-3xl font-bold text-center text-slate-800 mb-2">From Signal to Insight</h2>
            <p class="text-lg text-center text-slate-600 mb-10 max-w-3xl mx-auto">Visualize the power of signal processing. The EMG module demonstrates how a raw muscle signal is transformed into a meaningful measure of activity.</p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                    <h3 class="text-xl font-bold text-center mb-4">1. Raw EMG Signal</h3>
                    <div class="mini-chart-container">
                        <canvas id="rawEmgChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-center mb-4">2. Rectified Signal</h3>
                     <div class="mini-chart-container">
                        <canvas id="rectifiedEmgChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-center mb-4">3. Integrated Envelope</h3>
                     <div class="mini-chart-container">
                        <canvas id="integratedEmgChart"></canvas>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="py-12">
            <h2 class="text-3xl font-bold text-center text-slate-800 mb-10">Comprehensive Curriculum</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="module-card"><b>SUSTBME01:</b> Electrocardiogram (ECG)</div>
                <div class="module-card"><b>SUSTBME02:</b> Electromyogram (EMG)</div>
                <div class="module-card"><b>SUSTBME03:</b> Electrooculogram (EOG)</div>
                <div class="module-card"><b>SUSTBME04:</b> Electroencephalogram (EEG)</div>
                <div class="module-card"><b>SUSTBME05:</b> Blood Pressure</div>
                <div class="module-card"><b>SUSTBME06:</b> Photoplethysmogram (PPG)</div>
                <div class="module-card"><b>SUSTBME07:</b> Respiratory Ventilation</div>
                <div class="module-card"><b>SUSTBME08:</b> Pulse Meter</div>
                <div class="module-card"><b>SUSTBME09:</b> Impedance</div>
                <div class="module-card"><b>SUSTBME10:</b> Doppler Ultrasound</div>
                <div class="module-card"><b>SUSTBME11:</b> TENS</div>
                <div class="module-card"><b>SUSTBME12:</b> Vital Capacity Meter</div>
            </div>
        </section>
        
    </main>

    <footer class="gradient-bg text-white text-center py-10 px-4 mt-20">
        <h2 class="text-3xl font-bold">Enter the Virtual Lab</h2>
        <p class="mt-2 text-blue-200">Start building, testing, and mastering biomedical measurement systems today.</p>
    </footer>

    <script>
        const brilliantBluesPalette = {
            blue: '#00A1E4',
            midBlue: '#3B77BC',
            darkBlue: '#263C8A',
            deepBlue: '#1E2E5A',
            accent: '#FBB03B',
            gridColor: 'rgba(30, 46, 90, 0.1)',
            textColor: '#1E2E5A',
        };

        function wrapLabel(str, maxWidth) {
            if (str.length <= maxWidth) {
                return str;
            }
            const words = str.split(' ');
            const lines = [];
            let currentLine = '';
            for (const word of words) {
                if ((currentLine + word).length > maxWidth && currentLine.length > 0) {
                    lines.push(currentLine);
                    currentLine = word;
                } else {
                    currentLine += (currentLine.length === 0 ? '' : ' ') + word;
                }
            }
            if (currentLine.length > 0) {
                lines.push(currentLine);
            }
            return lines;
        }
        
        const sharedTooltipConfig = {
            plugins: {
                tooltip: {
                    callbacks: {
                        title: function(tooltipItems) {
                            const item = tooltipItems[0];
                            let label = item.chart.data.labels[item.dataIndex];
                            if (Array.isArray(label)) {
                              return label.join(' ');
                            }
                            return label;
                        }
                    }
                }
            }
        };

        const coreConceptsData = {
            labels: [
                'Amplifier / Gain', 'LPF', 'HPF', 'BRF', 'Comparator',
                'Monostable Multivibrator', 'Demodulator', 'Oscillator / Timer', 'Rectifier'
            ].map(label => wrapLabel(label, 16)),
            datasets: [{
                label: 'Number of Modules Featuring this Experiment',
                data: [10, 7, 7, 6, 3, 4, 2, 4, 2],
                backgroundColor: brilliantBluesPalette.midBlue,
                borderColor: brilliantBluesPalette.darkBlue,
                borderWidth: 2,
                borderRadius: 5
            }]
        };

        new Chart(document.getElementById('coreConceptsChart'), {
            type: 'bar',
            data: coreConceptsData,
            options: {
                ...sharedTooltipConfig,
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: { color: brilliantBluesPalette.gridColor },
                        ticks: { color: brilliantBluesPalette.textColor, font: { weight: '600' } }
                    },
                    y: {
                        grid: { display: false },
                        ticks: { color: brilliantBluesPalette.textColor, font: { weight: '600' } }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                         ...sharedTooltipConfig.plugins.tooltip,
                        backgroundColor: brilliantBluesPalette.darkBlue,
                        titleFont: { size: 14, weight: 'bold' },
                        bodyFont: { size: 12 },
                        displayColors: false
                    }
                }
            }
        });

        const emgSharedOptions = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: { display: false },
                    grid: { display: false }
                },
                x: {
                    ticks: { display: false },
                    grid: { display: false }
                }
            },
            plugins: {
                legend: { display: false },
            },
            elements: {
                point: { radius: 0 }
            },
            animation: {
                duration: 1500
            }
        };

        const generateSignalData = (points, generatorFn) => {
            const data = [];
            for (let i = 0; i < points; i++) {
                data.push(generatorFn(i));
            }
            return data;
        };

        const rawEmgData = {
            labels: Array.from({length: 100}, (_, i) => i),
            datasets: [{
                data: generateSignalData(100, i => (Math.random() - 0.5) * (Math.sin(i * 0.5) > 0.5 ? 2 : 0.5)),
                borderColor: brilliantBluesPalette.blue,
                borderWidth: 1.5,
                tension: 0.4
            }]
        };
        new Chart(document.getElementById('rawEmgChart'), { type: 'line', data: rawEmgData, options: emgSharedOptions });
        
        const rectifiedEmgData = {
            labels: Array.from({length: 100}, (_, i) => i),
            datasets: [{
                data: rawEmgData.datasets[0].data.map(y => Math.abs(y)),
                borderColor: brilliantBluesPalette.midBlue,
                borderWidth: 1.5,
                tension: 0.4
            }]
        };
        new Chart(document.getElementById('rectifiedEmgChart'), { type: 'line', data: rectifiedEmgData, options: emgSharedOptions });

        const integratedEmgData = {
            labels: Array.from({length: 100}, (_, i) => i),
            datasets: [{
                data: (() => {
                    const rectified = rectifiedEmgData.datasets[0].data;
                    let integrated = [];
                    let sum = 0;
                    for(let i=0; i < rectified.length; i++) {
                        sum += rectified[i];
                        if (i % 5 === 0) {
                           integrated.push(sum / 5);
                           sum = 0;
                        }
                    }
                    const smoothed = [];
                    for(let i=0; i < integrated.length; i++){
                       for(let j=0; j<5; j++) smoothed.push(integrated[i]);
                    }
                    return smoothed;
                })(),
                borderColor: brilliantBluesPalette.accent,
                backgroundColor: 'rgba(251, 176, 59, 0.2)',
                fill: true,
                borderWidth: 2,
                tension: 0.4
            }]
        };
        new Chart(document.getElementById('integratedEmgChart'), { type: 'line', data: integratedEmgData, options: emgSharedOptions });

    </script>
</body>
</html>
