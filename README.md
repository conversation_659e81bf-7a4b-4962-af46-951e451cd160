# ECG Signal Processing Platform

A comprehensive Python platform for ECG signal processing, R-peak detection, and Heart Rate Variability (HRV) analysis with advanced algorithms and robust error handling.

## Features

### 🔍 Advanced R-Peak Detection
- **Bandpass filtering** (5-15 Hz) for noise reduction
- **Signal differentiation** to emphasize QRS complexes
- **Adaptive thresholding** with real-time threshold adjustment
- **Moving window integration** for signal smoothing
- **Validation and artifact removal** with physiological constraints

### 📊 Comprehensive HRV Analysis
- **Time domain features**: SDNN, RMSSD, pNN50, triangular index, CV
- **Frequency domain features**: VLF, LF, HF power, LF/HF ratio
- **Power spectral density** analysis using <PERSON>'s method
- **Quality metrics** for signal assessment
- **Robust error handling** for insufficient data

### 🛠️ Additional Capabilities
- **Multi-format data loading** (WFDB, CSV, TXT, HDF5, MAT)
- **PhysioNet database support** (MIT-BIH, NSR, QT, etc.)
- **Signal preprocessing** with baseline removal and normalization
- **Comprehensive visualization** of all processing steps
- **Performance optimization** for real-time processing

## Installation

1. Clone or download the repository
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

### Required Dependencies
- numpy >= 1.21.0
- scipy >= 1.7.0
- matplotlib >= 3.4.0
- scikit-learn >= 1.0.0
- pandas >= 1.3.0
- h5py >= 3.0.0
- biosppy >= 0.8.0
- wfdb >= 3.4.0

## Quick Start

### Basic Usage

```python
from ecg_processor import ECGProcessor, HRVAnalyzer
import numpy as np

# Initialize processor
processor = ECGProcessor(sampling_rate=360.0)
hrv_analyzer = HRVAnalyzer(sampling_rate=360.0)

# Load your ECG signal (example with synthetic data)
from example_usage import generate_synthetic_ecg
ecg_signal = generate_synthetic_ecg(duration=60.0, sampling_rate=360.0)

# Detect R-peaks
r_peaks, processing_info = processor.detect_r_peaks(ecg_signal, plot_steps=True)

# Validate R-peaks
validated_peaks, validation_info = processor.validate_r_peaks(r_peaks, len(ecg_signal))

# Perform HRV analysis
analysis_results = hrv_analyzer.analyze_hrv(validated_peaks, len(ecg_signal))

# Plot results
hrv_analyzer.plot_hrv_analysis(analysis_results, validated_peaks, ecg_signal)
```

### Loading Real ECG Data

```python
from ecg_datasets import ECGDatasetLoader

# Initialize loader
loader = ECGDatasetLoader()

# Load MIT-BIH record
ecg_signal, sampling_rate, annotations = loader.load_mit_bih_record('100')

# Load CSV file
ecg_signal, sampling_rate = loader.load_csv_file('ecg_data.csv', column=0, sampling_rate=360.0)

# Auto-detect format
ecg_signal, sampling_rate, annotations = loader.load_ecg_data('data.txt', file_format='auto')
```

## Examples

### Example 1: Complete ECG Analysis

```python
# Run the complete example
python example_usage.py
```

This will demonstrate:
- R-peak detection with visualization
- HRV analysis with all features
- Error handling scenarios
- Comparison of different physiological conditions

### Example 2: Real-time Processing

```python
from ecg_processor import ECGProcessor

processor = ECGProcessor(sampling_rate=360.0)

# Process ECG in chunks for real-time applications
chunk_size = 1800  # 5 seconds at 360 Hz
overlap = 180      # 0.5 seconds overlap

for chunk in ecg_chunks:
    r_peaks, _ = processor.detect_r_peaks(chunk)
    # Process r_peaks...
```

### Example 3: Batch Processing

```python
from ecg_datasets import ECGDatasetLoader
from ecg_processor import ECGProcessor, HRVAnalyzer

loader = ECGDatasetLoader()
processor = ECGProcessor(sampling_rate=360.0)
hrv_analyzer = HRVAnalyzer(sampling_rate=360.0)

# Process multiple MIT-BIH records
records = ['100', '101', '102', '103', '104']
results = []

for record in records:
    try:
        ecg_signal, fs, _ = loader.load_mit_bih_record(record)
        r_peaks, _ = processor.detect_r_peaks(ecg_signal)
        validated_peaks, _ = processor.validate_r_peaks(r_peaks, len(ecg_signal))
        analysis = hrv_analyzer.analyze_hrv(validated_peaks, len(ecg_signal))
        
        results.append({
            'record': record,
            'analysis': analysis
        })
    except Exception as e:
        print(f"Error processing record {record}: {e}")
```

## Algorithm Details

### R-Peak Detection (Pan-Tompkins Algorithm)

1. **Bandpass Filtering**: 5-15 Hz Butterworth filter to remove baseline drift and high-frequency noise
2. **Differentiation**: Five-point derivative to emphasize QRS slopes
3. **Squaring**: Square the signal to emphasize QRS complexes
4. **Moving Window Integration**: 150ms window for signal smoothing
5. **Adaptive Thresholding**: Dynamic threshold adjustment based on signal and noise peaks
6. **Back-search**: Find actual R-peaks in original signal around detected candidates

### HRV Analysis Features

#### Time Domain
- **SDNN**: Standard deviation of NN intervals
- **RMSSD**: Root mean square of successive differences
- **pNN50**: Percentage of successive RR intervals differing by >50ms
- **pNN20**: Percentage of successive RR intervals differing by >20ms
- **Triangular Index**: Total RR intervals / histogram height
- **CV**: Coefficient of variation

#### Frequency Domain
- **VLF Power**: Very low frequency (0.0033-0.04 Hz)
- **LF Power**: Low frequency (0.04-0.15 Hz)
- **HF Power**: High frequency (0.15-0.4 Hz)
- **LF/HF Ratio**: Sympathovagal balance indicator
- **Peak Frequencies**: Dominant frequencies in LF and HF bands

## Testing

Run comprehensive tests:

```bash
python test_platform.py
```

This includes:
- Unit tests for all functions
- Performance benchmarks
- Accuracy tests with known ground truth
- Noise robustness evaluation

## Supported Datasets

The platform supports various ECG databases:

- **MIT-BIH Arrhythmia Database**: Standard arrhythmia analysis
- **MIT-BIH Normal Sinus Rhythm Database**: Normal rhythm recordings
- **European ST-T Database**: ST-T change analysis
- **QT Database**: QT interval analysis
- **Long-term AF Database**: Atrial fibrillation recordings

## Error Handling

The platform includes robust error handling for:
- Empty or invalid signals
- Insufficient R-peaks for analysis
- Extreme noise levels
- Invalid parameter values
- File loading errors

## Performance

Typical performance on a modern CPU:
- **R-peak detection**: ~100x real-time
- **HRV analysis**: ~500x real-time
- **Memory usage**: <100MB for 1-hour recording

## Clinical Applications

This platform is suitable for:
- **Cardiac monitoring**: Real-time R-peak detection
- **HRV assessment**: Autonomic nervous system evaluation
- **Research**: Algorithm development and validation
- **Telemedicine**: Remote cardiac monitoring
- **Sports medicine**: Athletic performance monitoring

## Visualization

The platform provides comprehensive visualization:
- ECG signal with detected R-peaks
- R-peak detection processing steps
- RR interval tachogram
- RR interval distribution
- Poincaré plot
- Power spectral density
- HRV feature summary

## Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## References

1. Pan, J., & Tompkins, W. J. (1985). A real-time QRS detection algorithm. IEEE transactions on biomedical engineering, 32(3), 230-236.
2. Task Force of the European Society of Cardiology. (1996). Heart rate variability: standards of measurement, physiological interpretation and clinical use.
3. Malik, M., et al. (1996). Heart rate variability: standards of measurement, physiological interpretation, and clinical use.

## Citation

If you use this platform in your research, please cite:

```
ECG Signal Processing Platform for R-peak Detection and HRV Analysis
https://github.com/your-repo/ecg-platform
```

## Support

For questions, issues, or feature requests:
- Create an issue on GitHub
- Contact: [<EMAIL>]

---

**Note**: This platform is for research and educational purposes. For clinical applications, please ensure proper validation and regulatory compliance.