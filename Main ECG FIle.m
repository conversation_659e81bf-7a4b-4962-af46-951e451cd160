Generated matlab
% =========================================================================
% الملف الرئيسي: main_ecg_analysis.m
% =========================================================================
% هذا هو السكريبت الرئيسي الذي يقوم بتشغيل عملية تحليل إشارة ECG بأكملها.
% 1. تحميل وإعداد البيانات.
% 2. استدعاء وظيفة المعالجة الرئيسية لاستخلاص السمات (بما في ذلك HRV).
% 3. عرض النتائج وتصويرها.
% -------------------------------------------------------------------------

clear; clc; close all;

% --- الخطوة 1: تحميل وإعداد البيانات ---
% بيانات وهمية للتشغيل الفوري (إشارة ECG مع بعض الضوضاء)
Fs = 1000; % تردد العينة
t = 0:1/Fs:20-1/Fs; % 20 ثانية من البيانات لتحليل HRV أفضل
clean_ecg = ecg(length(t))';
noise = 0.1 * randn(1, length(t));
val = repmat(clean_ecg + noise, 12, 1); % تكرار الإشارة لإنشاء 12 قناة
disp('تم إنشاء بيانات ECG وهمية للتجربة (20 ثانية).');


% --- الخطوة 2: استدعاء وظيفة المعالجة الرئيسية ---
% هذه الوظيفة تقوم بكل العمليات: التنقية، استخلاص السمات الشكلية، الإحصائية، و HRV
try
    [features, denoised_ecg] = process_ecg_signals(val, Fs);
    disp('تم استخلاص السمات بنجاح.');
    
    % --- عرض بعض السمات المستخلصة للقناة الأولى ---
    disp('--- السمات الشكلية (القناة الأولى) ---');
    fprintf('معدل ضربات القلب: %d نبضة/دقيقة\n', features.morphological(1).BPM);
    fprintf('متوسط مدة QRS: %.3f مللي ثانية\n', features.morphological(1).QRS_duration * 1000);
    
    disp('--- سمات HRV - النطاق الزمني (القناة الأولى) ---');
    fprintf('SDNN: %.2f مللي ثانية\n', features.hrv_time(1).SDNN);
    fprintf('RMSSD: %.2f مللي ثانية\n', features.hrv_time(1).RMSSD);
    fprintf('pNN50: %.2f %%\n', features.hrv_time(1).pNN50);

    disp('--- سمات HRV - النطاق الترددي (القناة الأولى) ---');
    fprintf('LF Power: %.2f ms^2\n', features.hrv_freq(1).LF_Power);
    fprintf('HF Power: %.2f ms^2\n', features.hrv_freq(1).HF_Power);
    fprintf('LF/HF Ratio: %.2f\n', features.hrv_freq(1).LF_HF_Ratio);

    % --- الخطوة 3: تصوير النتائج ---
    % تصور الإشارة الأصلية، المنقاة، والنقاط المكتشفة للقناة الأولى
    plot_ecg_analysis(val(1,:), denoised_ecg(1,:), features.morphological(1).detected_points, Fs);
    
    % تصور نتائج تحليل HRV للقناة الأولى
    plot_hrv_analysis(features.morphological(1).detected_points.R_locs, Fs);

catch ME
    fprintf('حدث خطأ أثناء المعالجة: %s\n', ME.message);
    fprintf('في الملف: %s, في السطر: %d\n', ME.stack(1).name, ME.stack(1).line);
end

الملف: process_ecg_signals.m (المنسق الرئيسي)
Generated matlab
function [features, ecg_clean] = process_ecg_signals(ecg_raw, Fs)
    % --- 1. المعالجة الأولية: إزالة التشويش ---
    dec_bw = mdwtdec('r', ecg_raw, 10, 'db6');
    a10 = mdwtrec(dec_bw, 'a', 10);
    ecg_bw_removed = ecg_raw - a10;
    ecg_clean = denoise_swt(ecg_bw_removed);

    % --- 2. استخلاص السمات ---
    num_leads = size(ecg_clean, 1);
    
    % تهيئة هياكل المخرجات
    morph_features = repmat(struct(), num_leads, 1);
    hrv_time_features = repmat(struct(), num_leads, 1);
    hrv_freq_features = repmat(struct(), num_leads, 1);

    for j = 1:num_leads
        [morph_features(j), r_locs] = extract_morphological_features(ecg_clean(j, :), Fs);
        
        % حساب سمات HRV فقط إذا تم العثور على قمم R
        if ~isempty(r_locs)
            hrv_time_features(j) = calculate_hrv_time_domain(r_locs, Fs);
            hrv_freq_features(j) = calculate_hrv_frequency_domain(r_locs, Fs);
        end
    end
    
    % استخلاص السمات الإحصائية من المويجات
    dec_haar = mdwtdec('r', ecg_clean, 9, 'haar');
    stats_features = extract_wavelet_stats(dec_haar);
    
    % --- 3. تجميع كل السمات في هيكل واحد ---
    features.morphological = morph_features;
    features.hrv_time = hrv_time_features;
    features.hrv_freq = hrv_freq_features;
    features.statistical = stats_features;
end


الملف: extract_morphological_features.m (محسّن)
Generated matlab
function [features, r_locs] = extract_morphological_features(ecg_lead, Fs)
    % تهيئة المخرجات بقيم NaN لضمان التعامل السليم مع الأخطاء
    features.BPM = NaN;
    features.QRS_duration = NaN;
    features.T_amplitude = NaN;
    features.detected_points = struct('R_locs', [], 'Q_locs', [], 'S_locs', [], 'T_locs', []);

    % --- الخطوة 1: كشف R-peaks باستخدام Pan-Tompkins (أكثر قوة) ---
    r_locs = detect_r_peaks_pan_tompkins(ecg_lead, Fs);
    
    if numel(r_locs) < 2
        warning('لم يتم العثور على عدد كافٍ من قمم R في هذه القناة.');
        return; % الخروج من الوظيفة لهذه القناة
    end
    features.detected_points.R_locs = r_locs;

    % --- كشف Q و S peaks ---
    q_locs = []; s_locs = [];
    for i = 1:length(r_locs)
        q_win = r_locs(i)-round(0.1*Fs) : r_locs(i)-1;
        q_win = q_win(q_win > 0);
        if ~isempty(q_win), [~, min_idx] = min(ecg_lead(q_win)); q_locs = [q_locs, q_win(min_idx)]; end
        
        s_win = r_locs(i)+1 : r_locs(i)+round(0.1*Fs);
        s_win = s_win(s_win <= length(ecg_lead));
        if ~isempty(s_win), [~, min_idx] = min(ecg_lead(s_win)); s_locs = [s_locs, s_win(min_idx)]; end
    end
    features.detected_points.Q_locs = q_locs;
    features.detected_points.S_locs = s_locs;

    % --- كشف T-wave ---
    t_locs = [];
    for i = 1:length(r_locs)
        t_win = r_locs(i)+round(0.1*Fs) : r_locs(i)+round(0.4*Fs);
        t_win = t_win(t_win <= length(ecg_lead));
        if ~isempty(t_win), [~, max_idx] = max(ecg_lead(t_win)); t_locs = [t_locs, t_win(max_idx)]; end
    end
    features.detected_points.T_locs = t_locs;

    % --- حساب السمات الشكلية ---
    features.BPM = round(60 / (mean(diff(r_locs)) / Fs));
    if length(q_locs) == length(s_locs) && ~isempty(q_locs)
        features.QRS_duration = mean(s_locs - q_locs) / Fs;
    end
    if ~isempty(t_locs)
        features.T_amplitude = mean(ecg_lead(t_locs));
    end
end


الملفات الجديدة والمساعدة

detect_r_peaks_pan_tompkins.m (جديد)

Generated matlab
function [r_peaks_indices] = detect_r_peaks_pan_tompkins(ecg_signal, fs)
    % تطبيق خوارزمية Pan-Tompkins للكشف عن قمم R
    [b, a] = butter(1, [5 15]/(fs/2), 'bandpass');
    filtered_ecg = filtfilt(b, a, ecg_signal);
    diff_ecg = diff(filtered_ecg);
    squared_ecg = diff_ecg.^2;
    integrated_ecg = conv(squared_ecg, ones(1, round(0.150 * fs))) / round(0.150 * fs);
    
    [pks, locs] = findpeaks(integrated_ecg, 'MinPeakDistance', round(0.2*fs));
    
    signal_peak = 0; noise_peak = 0; threshold = 0;
    r_peaks_indices = [];
    
    for i = 1:length(pks)
        if i == 1
            signal_peak = max(integrated_ecg(1:locs(i)));
            noise_peak = mean(integrated_ecg(1:locs(i)));
            threshold = noise_peak + 0.25 * (signal_peak - noise_peak);
        end
        
        if pks(i) > threshold
            r_peaks_indices = [r_peaks_indices, locs(i)];
            signal_peak = 0.125 * pks(i) + 0.875 * signal_peak;
        else
            noise_peak = 0.125 * pks(i) + 0.875 * noise_peak;
        end
        threshold = noise_peak + 0.25 * (signal_peak - noise_peak);
    end

    % البحث العكسي للحصول على الموقع الدقيق
    for i = 1:length(r_peaks_indices)
        win_start = max(1, r_peaks_indices(i) - round(0.1*fs));
        win_end = min(length(filtered_ecg), r_peaks_indices(i));
        [~, max_idx] = max(filtered_ecg(win_start:win_end));
        r_peaks_indices(i) = win_start + max_idx - 1;
    end
end



calculate_hrv_time_domain.m (جديد)

Generated matlab
function [hrv_td] = calculate_hrv_time_domain(r_peaks_indices, fs)
    % حساب سمات HRV في النطاق الزمني
    hrv_td.SDNN = NaN; hrv_td.RMSSD = NaN; hrv_td.pNN50 = NaN;
    if numel(r_peaks_indices) < 5, return; end

    rr_intervals_sec = diff(r_peaks_indices) / fs;
    hrv_td.SDNN = std(rr_intervals_sec) * 1000;
    diff_rr = diff(rr_intervals_sec);
    hrv_td.RMSSD = sqrt(mean(diff_rr.^2)) * 1000;
    hrv_td.pNN50 = (sum(abs(diff_rr) > 0.050) / numel(diff_rr)) * 100;
end



calculate_hrv_frequency_domain.m (جديد)

Generated matlab
function [hrv_fd] = calculate_hrv_frequency_domain(r_peaks_indices, fs)
    % حساب سمات HRV في النطاق الترددي
    hrv_fd.LF_Power = NaN; hrv_fd.HF_Power = NaN; hrv_fd.LF_HF_Ratio = NaN;
    if numel(r_peaks_indices) < 30, return; end

    rr_intervals = diff(r_peaks_indices) / fs;
    rr_times = r_peaks_indices(2:end) / fs;
    
    resample_fs = 4;
    time_vector = rr_times(1):1/resample_fs:rr_times(end);
    rr_interpolated = interp1(rr_times, rr_intervals, time_vector, 'spline');

    [pxx, f] = pwelch(rr_interpolated - mean(rr_interpolated), [], [], [], resample_fs);
    
    lf_band = (f >= 0.04 & f < 0.15);
    hf_band = (f >= 0.15 & f < 0.4);

    hrv_fd.LF_Power = trapz(f(lf_band), pxx(lf_band));
    hrv_fd.HF_Power = trapz(f(hf_band), pxx(hf_band));
    
    if hrv_fd.HF_Power > 0
        hrv_fd.LF_HF_Ratio = hrv_fd.LF_Power / hrv_fd.HF_Power;
    end
end



plot_hrv_analysis.m (جديد)

Generated matlab
function plot_hrv_analysis(r_locs, fs)
    if numel(r_locs) < 10
        disp('لا يمكن رسم تحليل HRV لعدد قليل من القمم.');
        return;
    end
    
    figure('Name', 'نتائج تحليل HRV', 'Position', [150, 150, 1000, 800]);
    
    % --- الرسمة الأولى: مخطط فترات RR (Tachogram) ---
    subplot(2, 1, 1);
    rr_intervals_ms = diff(r_locs) / fs * 1000;
    rr_times_sec = r_locs(2:end) / fs;
    plot(rr_times_sec, rr_intervals_ms, '-o', 'MarkerSize', 4);
    title('مخطط فترات RR (Tachogram)');
    xlabel('الزمن (ثانية)');
    ylabel('مدة فترة RR (مللي ثانية)');
    grid on;

    % --- الرسمة الثانية: كثافة القدرة الطيفية (PSD) ---
    subplot(2, 1, 2);
    rr_intervals = diff(r_locs) / fs;
    rr_times = r_locs(2:end) / fs;
    resample_fs = 4;
    time_vector = rr_times(1):1/resample_fs:rr_times(end);
    rr_interpolated = interp1(rr_times, rr_intervals, time_vector, 'spline');
    [pxx, f] = pwelch(rr_interpolated - mean(rr_interpolated), [], [], [], resample_fs);
    
    plot(f, pxx, 'k', 'LineWidth', 1.5);
    hold on;
    
    lf_band = (f >= 0.04 & f < 0.15);
    hf_band = (f >= 0.15 & f < 0.4);
    
    area(f(lf_band), pxx(lf_band), 'FaceColor', 'b', 'FaceAlpha', 0.3, 'DisplayName', 'LF (0.04-0.15 Hz)');
    area(f(hf_band), pxx(hf_band), 'FaceColor', 'g', 'FaceAlpha', 0.3, 'DisplayName', 'HF (0.15-0.4 Hz)');
    
    title('كثافة القدرة الطيفية (PSD) لـ HRV');
    xlabel('التردد (Hz)');
    ylabel('القدرة (ms^2/Hz)');
    legend show;
    grid on;
    xlim([0 0.5]);
end


