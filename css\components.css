/* ===================================
   Components CSS - Biosignal Virtual Lab

   Author: Dr. <PERSON>il
   Institution: SUST - BME
   Year: 2025
   Contact: <EMAIL>

   Copyright © 2025 Dr. <PERSON>ub Esmail
   =================================== */

/* Enhanced Navigation Bar */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid var(--border-accent);
  box-shadow: var(--shadow-sm);
  z-index: var(--z-fixed);
  transition: all var(--transition-normal);
}

.nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  max-width: 1280px;
  margin: 0 auto;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all var(--transition-normal);
}

.nav-brand:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.nav-brand i {
  font-size: var(--font-size-2xl);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: pulse 2s ease-in-out infinite;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-link {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  transition: all var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
  background-color: var(--bg-secondary);
  text-decoration: none;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
}

.nav-toggle span {
  width: 24px;
  height: 2px;
  background-color: var(--text-primary);
  transition: all var(--transition-fast);
}

/* Theme Toggle Button */
.theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-full);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-normal);
  margin-left: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.theme-toggle:hover {
  background: var(--primary-gradient);
  color: white;
  transform: scale(1.1) rotate(15deg);
  box-shadow: var(--shadow-colored);
}

.theme-toggle i {
  font-size: var(--font-size-lg);
  transition: all var(--transition-normal);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-colored);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: var(--primary-gradient);
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px) scale(1.02);
  text-decoration: none;
  color: white;
}

.btn-secondary {
  background: var(--bg-gradient-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-accent);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(10px);
}

.btn-secondary:hover {
  background: var(--bg-gradient-primary);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
  color: var(--text-primary);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  position: relative;
  overflow: hidden;
}

.btn-outline::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--primary-gradient);
  transition: left var(--transition-normal);
  z-index: -1;
}

.btn-outline:hover::before {
  left: 0;
}

.btn-outline:hover {
  color: white;
  border-color: transparent;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: var(--shadow-colored);
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
}

.btn-small {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
}

/* Enhanced Cards */
.card {
  background: var(--bg-gradient-primary);
  border: 1px solid var(--border-accent);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
}

.card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px) scale(1.02);
  border-color: var(--primary-color);
}

.card-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-sm);
}

.card-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Enhanced Feature Cards */
.feature-card {
  background: var(--bg-gradient-primary);
  border: 1px solid var(--border-accent);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2xl);
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  transition: left var(--transition-slow);
}

.feature-card:hover::before {
  left: 100%;
}

.feature-card:hover {
  transform: translateY(-6px) scale(1.03);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-color);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-lg);
  background: var(--primary-gradient);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-3xl);
  box-shadow: var(--shadow-colored);
  transition: all var(--transition-normal);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-glow);
}

.feature-card h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.feature-card p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.feature-list {
  list-style: none;
  text-align: left;
  margin-bottom: var(--spacing-lg);
}

.feature-list li {
  padding: var(--spacing-xs) 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.feature-list li::before {
  content: "✓";
  color: var(--success-color);
  font-weight: var(--font-weight-bold);
  margin-right: var(--spacing-sm);
}

/* Module Cards */
.module-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.module-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.module-card:hover::before {
  left: 100%;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.module-card.primary {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color)10, var(--bg-primary));
}

.module-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.module-header i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
}

.module-header h3 {
  font-size: var(--font-size-lg);
  margin: 0;
}

.module-status {
  margin-top: var(--spacing-md);
}

.status-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.ready {
  background-color: var(--success-color);
  color: white;
}

.status-badge.development {
  background-color: var(--warning-color);
  color: white;
}

.status-badge.coming-soon {
  background-color: var(--gray-400);
  color: white;
}

/* Statistics Cards */
.stat-card {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-number {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section-header h2 {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-header p {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Elements */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-input {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: all var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-select {
  width: 100%;
  padding: var(--spacing-md);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  background-color: var(--bg-primary);
  cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }
  
  .nav-toggle {
    display: flex;
  }
  
  .feature-card,
  .module-card {
    padding: var(--spacing-lg);
  }
  
  .btn-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
  }
}
