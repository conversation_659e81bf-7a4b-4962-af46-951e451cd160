
%% main	
function [QRS_duration,T_amplitude, T_duration, ST_duration,C]  = new_main(val)
 ecg = val(1:12,:);
 DEC = mdwtdec('r',ecg,10,'db6'); 
 A10 = mdwtrec(DEC,'a',10);
figure; plot(ecg(1,:))    % noisy lead I
ecg = ecg - A10;          % BW elemination 
ecg = denoising(ecg);  % elem of high-freq noise
figure; plot(ecg(1,:) )     % denoised signal lead I
 %% START
DEC = mdwtdec('r',ecg,9,'haar'); 
cd2 = mdwtrec(DEC,'cd',2);
% Morophology
 [ QRS_duration, T_amplitude, T_duration, ST_duration] = qrst_new(ecg,cd2);
 % statistical based wavelet 
cd1= mdwtrec(DEC,'cd',1); % 1'st detail coefficients
mean1= mean(cd1,2);
median1= median(cd1,2);
std1= std(cd1,0,2);
e1= [wentropy(cd1(1,:),'log energy');wentropy(cd1(2,:),'log energy');wentropy(cd1(3,:),'log energy'); wentropy(cd1(4,:),'log energy') ; wentropy(cd1(5,:),'log energy');wentropy(cd1(6,:),'log energy') ;wentropy(cd1(7,:),'log energy') ;wentropy(cd1(8,:),'log energy');wentropy(cd1(9,:),'log energy');wentropy(cd1(10,:),'log energy');wentropy(cd1(11,:),'log energy');wentropy(cd1(12,:),'log energy')];%log energy entropy
C1 = [mean1;median1;std1;e1];
 mean2=mean(cd2,2);          %second detail coefficients
median2=median(cd2,2);
std2=std(cd2,0,2);
e2 = [wentropy(cd2(1,:),'log energy');wentropy(cd2(2,:),'log energy');wentropy(cd2(3,:),'log energy'); wentropy(cd2(4,:),'log energy') ; wentropy(cd2(5,:),'log energy');wentropy(cd2(6,:),'log energy') ;wentropy(cd2(7,:),'log energy') ;wentropy(cd2(8,:),'log energy');wentropy(cd2(9,:),'log energy');wentropy(cd2(10,:),'log energy');wentropy(cd2(11,:),'log energy');wentropy(cd2(12,:),'log energy')];%log energy entropy
C2 = [mean2;median2;std2;e2];
 cd3= mdwtrec(DEC,'cd',3); %3'rd detail coefficients
mean3=mean(cd3,2);
median3=median(cd3,2);
std3=std(cd3,0,2);
e3= [wentropy(cd3(1,:),'log energy');wentropy(cd3(2,:),'log energy');wentropy(cd3(3,:),'log energy'); wentropy(cd3(4,:),'log energy') ; wentropy(cd3(5,:),'log energy');wentropy(cd3(6,:),'log energy') ;wentropy(cd3(7,:),'log energy') ;wentropy(cd3(8,:),'log energy');wentropy(cd3(9,:),'log energy');wentropy(cd3(10,:),'log energy');wentropy(cd3(11,:),'log energy');wentropy(cd3(12,:),'log energy')];%log energy entropy
 C3 = [mean3;median3;std3;e3];
 cd4= mdwtrec(DEC,'cd',4); %4'th detail coefficients
mean4=mean(cd4,2);
median4=median(cd4,2);
std4=std(cd4,0,2);
e4= [wentropy(cd4(1,:),'log energy');wentropy(cd4(2,:),'log energy');wentropy(cd4(3,:),'log energy'); wentropy(cd4(4,:),'log energy') ; wentropy(cd4(5,:),'log energy');wentropy(cd4(6,:),'log energy') ;wentropy(cd4(7,:),'log energy') ;wentropy(cd4(8,:),'log energy');wentropy(cd4(9,:),'log energy');wentropy(cd4(10,:),'log energy');wentropy(cd4(11,:),'log energy');wentropy(cd4(12,:),'log energy')];%log energy entropy
 C4= [mean4;median4;std4;e4];
 cd5= mdwtrec(DEC,'cd',5); %5th detail coefficients
mean5=mean(cd5,2);
median5=median(cd5,2);
std5=std(cd5,0,2);
e5= [wentropy(cd5(1,:),'log energy');wentropy(cd5(2,:),'log energy');wentropy(cd5(3,:),'log energy'); wentropy(cd5(4,:),'log energy') ; wentropy(cd5(5,:),'log energy');wentropy(cd5(6,:),'log energy') ;wentropy(cd5(7,:),'log energy') ;wentropy(cd5(8,:),'log energy');wentropy(cd5(9,:),'log energy');wentropy(cd5(10,:),'log energy');wentropy(cd5(11,:),'log energy');wentropy(cd5(12,:),'log energy')];%log energy entropy
 C5= [mean5;median5;std5;e5];
 cd6= mdwtrec(DEC,'cd',6); %4'th detail coefficients
mean6=mean(cd6,2);
median6=median(cd6,2);
std6=std(cd6,0,2);
e6= [wentropy(cd6(1,:),'log energy');wentropy(cd6(2,:),'log energy');wentropy(cd6(3,:),'log energy'); wentropy(cd6(4,:),'log energy') ; wentropy(cd6(5,:),'log energy');wentropy(cd6(6,:),'log energy') ;wentropy(cd6(7,:),'log energy') ;wentropy(cd6(8,:),'log energy');wentropy(cd6(9,:),'log energy');wentropy(cd6(10,:),'log energy');wentropy(cd6(11,:),'log energy');wentropy(cd6(12,:),'log energy')];%log energy entropy
 C6= [mean6;median6;std6;e6];
 cd7= mdwtrec(DEC,'cd',7); %4'th detail coefficients
mean7=mean(cd7,2);
median7=median(cd7,2);
std7=std(cd7,0,2);
e7= [wentropy(cd7(1,:),'log energy');wentropy(cd7(2,:),'log energy');wentropy(cd7(3,:),'log energy'); wentropy(cd7(4,:),'log energy') ; wentropy(cd7(5,:),'log energy');wentropy(cd7(6,:),'log energy') ;wentropy(cd7(7,:),'log energy') ;wentropy(cd7(8,:),'log energy');wentropy(cd7(9,:),'log energy');wentropy(cd7(10,:),'log energy');wentropy(cd7(11,:),'log energy');wentropy(cd7(12,:),'log energy')];%log energy entropy
 C7= [mean7;median7;std7;e7];
 cd8= mdwtrec(DEC,'cd',8); %4'th detail coefficients
mean8=mean(cd8,2);
median8=median(cd8,2);
std8=std(cd8,0,2);
e8= [wentropy(cd8(1,:),'log energy');wentropy(cd8(2,:),'log energy');wentropy(cd8(3,:),'log energy'); wentropy(cd8(4,:),'log energy') ; wentropy(cd8(5,:),'log energy');wentropy(cd8(6,:),'log energy') ;wentropy(cd8(7,:),'log energy') ;wentropy(cd8(8,:),'log energy');wentropy(cd8(9,:),'log energy');wentropy(cd8(10,:),'log energy');wentropy(cd8(11,:),'log energy');wentropy(cd8(12,:),'log energy')];%log energy entropy
 C8= [mean8;median8;std8;e8];
 cd9= mdwtrec(DEC,'cd',9); %4'th detail coefficients
mean9=mean(cd9,2);
median9=median(cd9,2);
std9=std(cd9,0,2);
e9= [wentropy(cd9(1,:),'log energy');wentropy(cd9(2,:),'log energy');wentropy(cd9(3,:),'log energy'); wentropy(cd9(4,:),'log energy') ; wentropy(cd9(5,:),'log energy');wentropy(cd9(6,:),'log energy') ;wentropy(cd9(7,:),'log energy') ;wentropy(cd9(8,:),'log energy');wentropy(cd9(9,:),'log energy');wentropy(cd9(10,:),'log energy');wentropy(cd9(11,:),'log energy');wentropy(cd9(12,:),'log energy')];%log energy entropy
 C9= [mean9;median9;std9;e9];
  C = [C1',C2',C3',C4',C5',C6',C7',C8',C9'];
 %% Moropholog 
function [  BPM, Q_location, Q_amplitude, S_location, S_amplitude, T_location, T_amplitude, QRS_duration, T_duration, ST_duration] = morophology(ecg,cd2)
figure; plot(cd2(1,:) )  
%% searching for R_locations
  beats_in_minutes_all=[];
Samp_all =[];
Sloc_all =[];
Qloc_all =[];
Qamp_all=[];
Tloc_all=[];
Tamp_all=[];
qrsduration_all =[];
  tduration_all =[];
  stduration_all=[];
 for j = 1:12
mx = max(cd2(j,:)); 
thresholdmx = 0.60*mx; 
P = find((cd2(j,:) >= thresholdmx)); 
P1= P; 
P2=[]; 
last=P1(1); 
P2=[P2 last]; 
 % In this step we find R peaks which are at least 50 samples apart  
for(i=2:1:length(P1))    
    if (P1(i)>(last+50))          
        last=P1(i);    
        P2=[P2 last]; 
        RP = length(P2) ;  
    end
end
Fs = 1000;
l = length (cd2(j,:))*4;
sduration = l/Fs;
mduration = sduration/60; 
beats_in_minutes = round((RP/mduration)) ;
beats_in_minutes_all = [beats_in_minutes_all , beats_in_minutes ];
 P3=P2*4; %peaks in time domain
ecg_s = ecg(j,:); % leadJ
N = length(ecg_s);
 Rloc =[];
%search from first
rangefirst = P3(1)-50:P3(1)+50; 
rngmn = min(rangefirst);
if (rngmn<=0) 
    rangefirst = P3(1)-30:P3(1)+50; 
    if(rngmn<=0)  
        rangefirst = P3(1)-10:P3(1)+50; 
        if(rngmn<=0)   
            rangefirst =P3(1)-0:P3(1)+50; 
        end
    end
end
 m1=max(ecg_s(rangefirst));
    l1=find(ecg_s(rangefirst)==m1); 
    pos1=rangefirst(l1); 
    Rloc1= pos1;
 %search from the end
    rangelast = P3(end)-50:P3(end)+50; 
    rngmx = max(rangelast);
    if(rngmx>=N) 
        rangelast = P3(1)-50:P3(1)+30; 
        if(rngmx>=N)   
            rangelast = P3(1)-50:P3(1)+10;   
            if(rngmn>=N)  
                rangelast=P3(1)-50:P3(1)+0;  
            end
        end
    end
    if rangelast>0
       mlast = max(ecg_s(rangelast));  
    llast=find(ecg_s(rangelast)==mlast); 
    poslast=rangelast(llast); 
    Rlocend= poslast;
    
    else
        Rlocend =[];
    end
%Search within a window of +-10 samples in the original signal with reference to up scaled R locations detected in downsampled signal.
    for i=2:1:length(P3)-1
        range = P3(i)-50:P3(i)+50; 
        m=max(ecg_s(range));    
        l= find(ecg_s(range)==m);  
        pos=range(l);    
        Rloc=[Rloc pos]; 
    end
    R = [Rloc1 Rloc Rlocend];
    R_amp = ecg_s([Rloc1 Rloc Rlocend]);
    
    % Just for plotting (plot the firrst lead)
    if j == 1
        R1 = Rloc1;
        R2 = Rloc;
        R3 = Rlocend;
        R_l = [R1 R2 R3];
         Ram = ecg_s(R_l);
        figure;plot(ecg_s)
        hold on
        stem(R_l,Ram,'k')
       
            title('Detected R peaks in the original signal')
    end
   %% %% Q_peak
 y = ecg_s;
aq1=R(1)-100:R(1)-10; 
n0= min(aq1); 
if (n0<0) 
    aq1=R(1)-80:R(1)-10;  
    n0= min(aq1);
    if (n0<0) 
        aq1=R(1)-60:R(1)-10; 
        n0=min(aq1);
        if(n0<0)  
            aq1=R(1)-40:R(1)-10; 
            n0 = min(aq1); 
            if(n0<0)  
                aq1=0;  
            end
        end
    end
end
if(aq1~=0) 
    mq1 = min(y(aq1)); 
    bq1 = y(aq1)==mq1; 
    bq1 =aq1(bq1); 
    Qloc(1)= bq1; 
    Qamp(1)= mq1 ; 
end
for(b=2:1:length(R))
    aq = R(b)-100:R(b)-10;
    if aq>0
    mq = min(y(aq)); 
    bq = find(y(aq)== mq); 
    bq = bq(1); 
    bq =aq(bq);
    Qloc(b)= bq; 
    Qamp(b)= mq ;
    end
end
Qloc_all =[Qloc_all, Qloc,zeros(1,15-length(Qloc))];
Qamp_all = [Qamp_all, mean(Qamp)];
% Just Plotting
if j ==1
    figure; 
    plot(ecg_s)
    hold on 
    stem(Qloc,Qamp,'go')  
    title('Detected Q peaks in the original signal')
end
 %% %% S_peak
 aslast=R(length(R))+5:R(length(R))+100;  
n0= max(aslast); 
if (n0>N) 
    aslast=R(length(R))+5:R(length(R))+80; 
    n0= max(aslast); 
    if (n0>N) 
        aslast=R(length(R))+5:R(length(R))+60; 
        n0=max(aslast); 
        if(n0>N)  
            aslast=R(length(R))+5:R(length(R))+40;  
            n0 = max(aslast); 
            if(n0>N)     
                aslast=0;  
            end
        end
    end
end
if(aslast~=0)    
    mslast = min(y(aslast)); 
    bslast = y(aslast)==mslast; 
    bslast =aslast(bslast); 
    Sloc(length(R))= bslast; 
    Samp(length(R))= mslast; 
end
 
for c=1:1:length(R)-1
    sss= 0; 
    as = R(c)+5:R(c)+100;
    mins = min(as);
    maxs = max(as); 
    if (mins < 0) || (maxs>N)  
        as = R(c)+0:R(c)+80;
        if (mins< 0) || (maxs>N)      
            as = R(c)+0:R(c)+50; 
            if (mins< 0) || (maxs>N)      
                sss = 1; 
            end
        end
    end
    if (sss~=1)  
        mnn = min(y(as)); 
        bs =find(y(as)== mnn); 
        bs = bs(1); 
        bs = as(bs);
        
    end
    Sloc(c)= bs;
    Samp(c)= mnn; 
end
Sloc_all = [Sloc_all,Sloc,zeros(1,15-length(Sloc))];
 Samp_all =  [Samp_all, mean(Samp)]; 
% Plotting
if j ==1
    figure;     plot(ecg_s) 
    hold on 
    stem(Sloc,Samp,'ko')  
title('Detected S peaks in the original signal')
end
 
%% QRS_duration
 if (length(Qloc) > length(Sloc)) 
    Qloc(1)=[]; 
    Qamp(1) = [];
elseif(length(Sloc) > length(Qloc)) 
    Sloc(end)=[];
    Samp(end) = [];
end
if length(Sloc)==length(Qloc)
qrsdurationsamples = Sloc - Qloc;
qrsduration = (qrsdurationsamples * 1)/Fs;
mean_qrs_d = mean(qrsduration);
else
    mean_qrs_d =0;
end
    if aq1~=0
    Qloc = [bq1 Qloc]; 
    Qamp = [mq1 Qamp]; 
end
if aslast~=0
    Sloc = [Sloc bslast]; 
    Samp = [Samp mslast];
end
 qrsduration_all = [qrsduration_all, mean_qrs_d];
% Just Plotting
if j ==1
figure; plot(y)
hold on  
Qwave = stem(Qloc,Qamp,'r*'); 
Rwave = stem(R,R_amp,'k*');  
Swave = stem(Sloc,Samp,'g*');  
title('QRS waves');
end
 %% %% T_peak
 atlast=R(length(R))+100:R(length(R))+400; 
n0= max(atlast);
if (n0>N)  
    atlast=R(length(R))+100:R(length(R))+350; 
    n0= max(atlast); 
    if (n0>N) 
        atlast=R(length(R))+100:R(length(R))+300;
        n0=max(atlast); 
        if(n0>N)  
            atlast=R(length(R))+100:R(length(R))+250; 
            n0 =max(atlast);
            if(n0>N) 
                atlast=R(length(R))+100:R(length(R))+200; 
                n0 =max(atlast); 
                if(n0>N)      
                    atlast = 0; 
                end
            end
        end
    end
end
if (atlast ~=0) 
    mxtlast = max(y(atlast));
    btlast = y(atlast)==mxtlast; 
    btlast = atlast(btlast);  
    Tloc(length(R))=btlast;   
    Tamp(length(R))=mxtlast;  
end
 
for(d=1:1:length(R)-1)
    at = R(d)+100:R(d)+400;
    if at<length(y)
    mxt = max(y(at)); 
    bt = find(y(at)== mxt); 
    bt = bt(1); 
    bt = at(bt);
    Tloc(d)=bt;
    Tamp(d)=mxt;
    else
        Tloc(d)=[];
        Tamp(d)=[];
    end
end
Tloc_all =[Tloc_all, Tloc,zeros(1,15-length(Tloc))];
Tamp_all = [Tamp_all, mean(Tamp)];
 % Just Plotting
if j ==1
    figure; 
    plot(ecg_s)
    hold on  
    stem(Tloc,Tamp,'mo')  
    title('Detected T peaks in the original signal')
end
   %% T_duration
       % T_offset
     ftlast = 0; 
    ft = 0; 
    tofflast  = 0;
    if (atlast ~=0) 
        tofflast = Tloc(length(Tloc)):1:Tloc(length(Tloc))+150;
        n0= max(tofflast); 
        if (n0>N)   
            tofflast = Tloc(length(Tloc)):1:Tloc(length(Tloc))+120;  
            n0= max(tofflast); 
            if (n0>N)       
                tofflast = Tloc(length(Tloc)):1:Tloc(length(Tloc))+100;  
                n0=max(tofflast);  
                if(n0>N)      
                    tofflast = Tloc(length(Tloc)):1:Tloc(length(Tloc))+80; 
                    n0 =max(tofflast);   
                    if(n0>N)      
                        tofflast = Tloc(length(Tloc)):1:Tloc(length(Tloc))+60; 
                        if(n0>N)      
                            tofflast=1; 
                        end
                    end
                end
            end
        end
    end
    if (tofflast ~=1) 
        for k = 1:1:length(tofflast)-1  
            if (y(tofflast(k))>0 && y(tofflast(k+1))<=0 )  
                gtofflast = tofflast(k+1);  
                foundofflast = gtofflast;   
                tdoff(length(Tloc))= foundofflast;  
                tdoff_amp(length(Tloc))= y(tdoff(length(Tloc)));  
                ftlast = 1; 
                if (ftlast==1)  
                    break   
                end
            end
        end
    end
    
    for i = 1:1:length(Tloc)-1
        ft = 0;  
        toff = Tloc(i):1:Tloc(i)+300; 
        for k = 1:1:length(toff)-1  
            if toff(k)>length(y) || toff(k+1)>length(y)
                break
            end
            if (y(toff(k))>0 && y(toff(k+1))<=0 )  
                gtoff = toff(k+1); 
                foundtoff =  gtoff;  
                foundtoff = foundtoff(1);  
                toff(i)= foundtoff; 
                tdoff(i) = toff(i);   
                ft = 1;  
                if (ft==1)  
                    break   
                end
                
            end
        end
      
   end
     tdoff(i) = toff(i) ;
     if tdoff(i)<=length(y)
    tdoff_amp(i) = y(tdoff(i));   
     end
    % T_onset
    
    if (tofflast ~=0) 
        tonlast = Tloc(length(Tloc)):-1:Tloc(length(Tloc))-150;  
        for k = 1:1:length(tonlast)-1   
            ftonlast = 0; 
            if (y(tonlast(k))>0 && y(tonlast(k+1))<=0 )  
                gtonlast = tonlast(k+1); 
                foundonlast=gtonlast;    
                tdon(length(Tloc))= foundonlast;  
                tdon_amp(length(Tloc)) = y(tdon(length(Tloc))); 
                ftonlast = 1;   
                if (ftonlast==1)  
                    break  
                end
            end
        end
    end
    for i = 1:1:length(Tloc)-1; 
        fton = 0; 
        toni = Tloc(i):-1:Tloc(i)-150; 
       for k = 1:1:length(toni)-1     
           if (y(toni(k))>0 && y(toni(k+1))<=0 )  
               gton = toni(k+1);  
               foundton =  gton; 
               foundton = foundton(1);  
               ton(i)= foundton;   
               tdon(i) = ton(i);     
               fton = 1;   
               if (fton==1)   
                   break  
               end
           elseif (fton~=1) 
               gton = min(toni); 
               foundton =  gton;
               foundton = foundton(1);   
               ton(i)= foundton;  
               tdon(i) = ton(i);    
               fton = 1;   
           end
       end
   tdon(i) = ton(i); 
    tdon_amp(i) = y(tdon(i));  
  end 
    
    
    %% T_duration
   
    if (length(tdon) > length(tdoff)) 
        tdon(end)=[]; 
        tdon_amp(end) = []; 
    elseif (length(tdoff) > length(tdon)) 
        tdoff(1)=[]; 
        tdoff_amp(1) = [];
    end
 
tdurationsamples = tdoff - tdon; 
    tduration = (tdurationsamples * 1)/1000; 
    mean_t_d = mean(tduration);
tduration_all = [tduration_all, mean_t_d];
    %  Plotting
if j==1
    figure;plot(y) 
    hold on 
    tonset = stem(tdon,tdon_amp,'r*'); 
    toffset = stem(tdoff,tdoff_amp,'k*');
    title('T onset  and offset');
end
    %% ST-sigment
     if (length(Sloc) > length(Tloc)) 
    Sloc(1)=[]; 
    Samp(1) = [];
elseif(length(Tloc) > length(Sloc)) 
    Tloc(end)=[];
    Tamp(end) = [];
    end
if length(Sloc)==length(Tloc)
stdurationsamples = Sloc - Tloc;
stduration = (stdurationsamples * 1)/Fs;
mean_st_d = mean(stduration);
else
  mean_st_d =0;
end
        
    if (sss~=1)  
     Sloc = [bs,Sloc];
    Samp= [mnn,Samp];; 
end
if (atlast ~=0) 
    Tloc = [Tloc btlast]; 
    Tamp = [Tamp mxtlast];
end
stduration_all = [stduration_all, mean_st_d];
% Just Plotting
if j ==1
figure; plot(y)
hold on  
Swave = stem(Sloc,Samp,'r*'); 
Twave = stem(Tloc,Tamp,'g*');  
title('ST waves');
end
    end                  % end the leads loop
  %% outputs
  BPM =  beats_in_minutes_all;
  S_amplitude = Samp_all;
  S_location = Sloc_all;
  Q_amplitude = Qamp_all;
  Q_location = Qloc_all;
  T_amplitude = Tamp_all;
  T_location= Tloc_all;
  QRS_duration = qrsduration_all ;
T_duration = tduration_all ;
ST_duration = stduration_all;
 %% Wavelet Denoising
function  X = denoising(ecg)
X = zeros(size(ecg));
N = length(ecg);
for i =1:12
   [app,detail] = swt(ecg(i,:),3,'db4');
   th = median(detail(1,:));
   th1 = detail(1,:) - th; 
   mad = median(abs(th1));
   sigma = mad/0.6745;
   threshold1 = sigma*(sqrt(2*log(N))/(log(2)));
   th = median(detail(2,:));
   th2 = detail(2,:) - th;
   mad = median(abs(th2));
   sigma = mad/0.6745;
   threshold2 = sigma*(sqrt(2*log(N))/(log(3)));
   th = median(detail(3,:));
   th3 = detail(3,:) - th;
   mad = median(abs(th3));
   sigma = mad/0.6745;
   threshold3 = sigma*(sqrt(2*log(N))/(log(4)));
   Y1 = wthresh(detail(1,:),'s',threshold1);
   Y2 = wthresh(detail(2,:),'s',threshold2);
   Y3 = wthresh(detail(3,:),'s',threshold3);
   detail(1,:)=Y1;
   detail(2,:)=Y2;
   detail(3,:)=Y3;
   X(i,:) = iswt(app,detail,'db4');
end
 %% Features normalization
function [X_norm, mu, sigma] = featureNormalize(X)
mu = mean(X);
X_norm = bsxfun(@minus, X, mu);
 
sigma = std(X_norm);
X_norm = bsxfun(@rdivide, X_norm, sigma);
end
 %% NoiseGenerator
% Copyright (C) 2006  Reza Sameni
% Sharif University of Technology, Tehran, Iran -- LIS-INPG, Grenoble, France
% <EMAIL>

%% resampling china challange data
val= resample(ECG.data',2,1);
val = val';
 val= val(:,1:10000);
 [  BPM, Q_location, Q_amplitude, S_location, S_amplitude, T_location, T_amplitude, QRS_duration, T_duration, ST_duration,C]  = main(val);
z =[  BPM, Q_location, Q_amplitude, S_location, S_amplitude, T_location, T_amplitude, QRS_duration, T_duration, ST_duration,C] ;
W = [W;z,zeros(1,2000-length(z))];
close all
 
%%  synthetic ECG
% Copyright (c) 2003 by Patrick McSharry & Gari Clifford, All Rights Reserved  
% See IEEE Transactions On Biomedical Engineering, 50(3), 289-294, March 2003.
% Contact P. McSharry (<EMAIL>) or G. Clifford (<EMAIL>)

%% PCA
%% Machine Learning Online Class (coursera) 
function [Z ,k]= pca_de(features_labeled)
X = features_labeled(:,1:end-1);
 [X_norm, mu, sigma] = featureNormalize(X);
 
%  Run PCA
[U, S] = pca(X_norm);
for k = 1:length(X_norm)-1
k_sum=0;
for i=1:k
  k_sum = k_sum + S(i,i);
end
n_sum =0;
for j=1:length(S)
  n_sum = n_sum + S(j,j);
end
if (k_sum/n_sum)>=0.99
  break;
  end
end

%% 
plot(X_norm(:, 1), X_norm(:, 2), 'bo');
 
%  Project the data onto K dimension
 K =k; 
Z = projectData(X_norm, U, K);
 
 
X_rec  = recoverData(Z, U, K);
 
%% EM denoising  
[signal,ipeaks] = ecgsyn();
sig = resample(signal,360,650);
sig = sig(1:1024);
sig = sig';
po = mean(sig.^2);
noise = NoiseGenerator(3,po,-5,1024,360);
noise = noise';
noisy = sig+noise;
X = denoise_1D(noisy);
figure;
subplot(211)
plot(noisy)
title('Synthetic signal with added motion artifacts')
subplot(212)
plot(X)
title(' denoised signal still with EM artifacts')
%% adaptive filter
load('reference.mat');
y1=reference; 
y2= noisy; % ECG signal data
a1= (y1 (:,1)); % accelerometer x-axis data
a2= (y1 (:,1)); % accelerometer y-axis data
a3= (y1 (:,1)); % accelerometer z-axis data
a = a1+a2+a3;
a=a/max (a);
b = a';
b = b(:,1:1024);
Mu= 0.0008;
Hd = adaptfilt.lms (32, Mu);
[s2, e] = filter (Hd, b, y2);
figure; plot(e)
title(' Signal filtered with adaptive filter')


%% adaptive filter
 y1=reference; % this is an ECG signal with motionartifacts
y2= (y1 (:,1)); % ECG signal data
a1= (y1 (:,1)); % accelerometer x-axis data
a2= (y1 (:,1)); % accelerometer y-axis data
a3= (y1 (:,1)); % accelerometer z-axis data
y2 = y2/max (y2);
Subplot (3, 1, 1), plot (y2), title ('ECG Signal with motionartifacts'); grid on
a = a1+a2+a3;
a=a/max (a);
Mu= 0.0008;
Hd = adapt filt. Lms (32, mu);
[s2, e] = filter (Hd, a, y2);
Subplot (3, 1, 2); plot (s2), title %('Noise (motion artifact)
estimate'), grid on
Subplot (3, 1, 3), plot (e), title('Adaptively filtered/ Noise freeECG signal');
grid on
 
