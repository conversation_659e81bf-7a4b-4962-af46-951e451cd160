"""
ECG Signal Processing Platform
Advanced R-peak detection and HRV analysis with improved algorithms
"""

import numpy as np
import scipy.signal
from scipy import interpolate
from scipy.signal import butter, filtfilt, hilbert, welch
import matplotlib.pyplot as plt
from typing import Tuple, List, Dict, Optional, Union
import warnings
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ECGProcessor:
    """
    Advanced ECG processor with improved R-peak detection and HRV analysis
    """
    
    def __init__(self, sampling_rate: float = 360.0):
        """
        Initialize ECG processor
        
        Args:
            sampling_rate: Sampling rate in Hz (default: 360 Hz for MIT-BIH)
        """
        self.sampling_rate = sampling_rate
        self.nyquist_freq = sampling_rate / 2
        
    def bandpass_filter(self, signal: np.ndarray, 
                       low_cutoff: float = 5.0, 
                       high_cutoff: float = 15.0,
                       order: int = 4) -> np.ndarray:
        """
        Apply bandpass filter to ECG signal for R-peak detection
        
        Args:
            signal: Input ECG signal
            low_cutoff: Low cutoff frequency in Hz
            high_cutoff: High cutoff frequency in Hz
            order: Filter order
            
        Returns:
            Filtered signal
        """
        try:
            # Ensure cutoff frequencies are within valid range
            low_cutoff = max(low_cutoff, 0.1)
            high_cutoff = min(high_cutoff, self.nyquist_freq - 1)
            
            # Design Butterworth bandpass filter
            low = low_cutoff / self.nyquist_freq
            high = high_cutoff / self.nyquist_freq
            
            b, a = butter(order, [low, high], btype='band')
            
            # Apply zero-phase filtering
            filtered_signal = filtfilt(b, a, signal)
            
            return filtered_signal
            
        except Exception as e:
            logger.error(f"Error in bandpass filtering: {e}")
            return signal
    
    def differentiate_signal(self, signal: np.ndarray) -> np.ndarray:
        """
        Apply differentiation to enhance R-peak detection
        
        Args:
            signal: Input filtered signal
            
        Returns:
            Differentiated signal
        """
        try:
            # Five-point derivative approximation
            diff_signal = np.zeros_like(signal)
            
            # Use central difference for interior points
            for i in range(2, len(signal) - 2):
                diff_signal[i] = (signal[i-2] - 8*signal[i-1] + 8*signal[i+1] - signal[i+2]) / 12
            
            # Handle boundary conditions
            diff_signal[0] = signal[1] - signal[0]
            diff_signal[1] = signal[2] - signal[0]
            diff_signal[-2] = signal[-1] - signal[-3]
            diff_signal[-1] = signal[-1] - signal[-2]
            
            return diff_signal
            
        except Exception as e:
            logger.error(f"Error in signal differentiation: {e}")
            return signal
    
    def squared_signal(self, signal: np.ndarray) -> np.ndarray:
        """
        Square the differentiated signal to emphasize peaks
        
        Args:
            signal: Input differentiated signal
            
        Returns:
            Squared signal
        """
        return signal ** 2
    
    def moving_window_integration(self, signal: np.ndarray, 
                                window_size: Optional[int] = None) -> np.ndarray:
        """
        Apply moving window integration
        
        Args:
            signal: Input squared signal
            window_size: Integration window size (default: 0.15 seconds)
            
        Returns:
            Integrated signal
        """
        if window_size is None:
            window_size = int(0.15 * self.sampling_rate)  # 150ms window
        
        try:
            # Use convolution for efficient moving average
            kernel = np.ones(window_size) / window_size
            integrated_signal = np.convolve(signal, kernel, mode='same')
            
            return integrated_signal
            
        except Exception as e:
            logger.error(f"Error in moving window integration: {e}")
            return signal
    
    def adaptive_threshold(self, signal: np.ndarray, 
                         refractory_period: Optional[float] = None) -> Tuple[np.ndarray, float]:
        """
        Apply adaptive thresholding for R-peak detection
        
        Args:
            signal: Input integrated signal
            refractory_period: Minimum time between peaks in seconds
            
        Returns:
            Tuple of (peak_indices, threshold_value)
        """
        if refractory_period is None:
            refractory_period = 0.2  # 200ms refractory period
        
        refractory_samples = int(refractory_period * self.sampling_rate)
        
        try:
            # Initialize adaptive threshold parameters
            threshold_factor = 0.5
            signal_peak = np.max(signal)
            noise_peak = np.mean(signal)
            
            threshold = noise_peak + threshold_factor * (signal_peak - noise_peak)
            
            peaks = []
            last_peak_idx = -refractory_samples
            
            for i in range(1, len(signal) - 1):
                # Check if current sample is a local maximum above threshold
                if (signal[i] > signal[i-1] and 
                    signal[i] > signal[i+1] and 
                    signal[i] > threshold and 
                    i - last_peak_idx > refractory_samples):
                    
                    peaks.append(i)
                    last_peak_idx = i
                    
                    # Update adaptive threshold
                    signal_peak = 0.125 * signal[i] + 0.875 * signal_peak
                    threshold = noise_peak + threshold_factor * (signal_peak - noise_peak)
                
                else:
                    # Update noise estimate
                    if signal[i] < threshold:
                        noise_peak = 0.125 * signal[i] + 0.875 * noise_peak
                        threshold = noise_peak + threshold_factor * (signal_peak - noise_peak)
            
            return np.array(peaks), threshold
            
        except Exception as e:
            logger.error(f"Error in adaptive thresholding: {e}")
            return np.array([]), 0.0
    
    def detect_r_peaks(self, ecg_signal: np.ndarray, 
                      plot_steps: bool = False) -> Tuple[np.ndarray, Dict]:
        """
        Comprehensive R-peak detection using Pan-Tompkins algorithm with improvements
        
        Args:
            ecg_signal: Input ECG signal
            plot_steps: Whether to plot intermediate steps
            
        Returns:
            Tuple of (r_peak_indices, processing_info)
        """
        try:
            if len(ecg_signal) == 0:
                raise ValueError("Empty ECG signal provided")
            
            processing_info = {}
            
            # Step 1: Bandpass filtering (5-15 Hz)
            filtered_signal = self.bandpass_filter(ecg_signal)
            processing_info['filtered'] = filtered_signal
            
            # Step 2: Differentiation
            diff_signal = self.differentiate_signal(filtered_signal)
            processing_info['differentiated'] = diff_signal
            
            # Step 3: Squaring
            squared_signal = self.squared_signal(diff_signal)
            processing_info['squared'] = squared_signal
            
            # Step 4: Moving window integration
            integrated_signal = self.moving_window_integration(squared_signal)
            processing_info['integrated'] = integrated_signal
            
            # Step 5: Adaptive thresholding
            peak_candidates, threshold = self.adaptive_threshold(integrated_signal)
            processing_info['threshold'] = threshold
            
            # Step 6: Back-search for actual R-peaks in original signal
            r_peaks = self._find_actual_r_peaks(ecg_signal, peak_candidates)
            processing_info['r_peaks'] = r_peaks
            
            if plot_steps:
                self._plot_detection_steps(ecg_signal, processing_info)
            
            if len(r_peaks) == 0:
                logger.warning("No R-peaks detected in the signal")
            else:
                logger.info(f"Detected {len(r_peaks)} R-peaks")
            
            return r_peaks, processing_info
            
        except Exception as e:
            logger.error(f"Error in R-peak detection: {e}")
            return np.array([]), {}
    
    def _find_actual_r_peaks(self, ecg_signal: np.ndarray, 
                           peak_candidates: np.ndarray,
                           search_window: int = None) -> np.ndarray:
        """
        Find actual R-peaks in original ECG signal around detected candidates
        
        Args:
            ecg_signal: Original ECG signal
            peak_candidates: Candidate peak locations
            search_window: Search window size in samples
            
        Returns:
            Actual R-peak indices
        """
        if search_window is None:
            search_window = int(0.05 * self.sampling_rate)  # 50ms window
        
        r_peaks = []
        
        for candidate in peak_candidates:
            # Define search window around candidate
            start = max(0, candidate - search_window)
            end = min(len(ecg_signal), candidate + search_window)
            
            # Find maximum in the window
            window_signal = ecg_signal[start:end]
            max_idx = np.argmax(window_signal)
            actual_peak = start + max_idx
            
            r_peaks.append(actual_peak)
        
        return np.array(r_peaks)
    
    def _plot_detection_steps(self, ecg_signal: np.ndarray, 
                            processing_info: Dict) -> None:
        """
        Plot intermediate steps of R-peak detection
        """
        fig, axes = plt.subplots(6, 1, figsize=(12, 15))
        
        time_axis = np.arange(len(ecg_signal)) / self.sampling_rate
        
        # Original signal
        axes[0].plot(time_axis, ecg_signal, 'b-', linewidth=0.8)
        axes[0].set_title('Original ECG Signal')
        axes[0].set_ylabel('Amplitude')
        axes[0].grid(True, alpha=0.3)
        
        # Filtered signal
        axes[1].plot(time_axis, processing_info['filtered'], 'g-', linewidth=0.8)
        axes[1].set_title('Bandpass Filtered Signal (5-15 Hz)')
        axes[1].set_ylabel('Amplitude')
        axes[1].grid(True, alpha=0.3)
        
        # Differentiated signal
        axes[2].plot(time_axis, processing_info['differentiated'], 'r-', linewidth=0.8)
        axes[2].set_title('Differentiated Signal')
        axes[2].set_ylabel('Amplitude')
        axes[2].grid(True, alpha=0.3)
        
        # Squared signal
        axes[3].plot(time_axis, processing_info['squared'], 'm-', linewidth=0.8)
        axes[3].set_title('Squared Signal')
        axes[3].set_ylabel('Amplitude')
        axes[3].grid(True, alpha=0.3)
        
        # Integrated signal
        axes[4].plot(time_axis, processing_info['integrated'], 'c-', linewidth=0.8)
        axes[4].axhline(y=processing_info['threshold'], color='r', linestyle='--', 
                       label=f'Threshold: {processing_info["threshold"]:.3f}')
        axes[4].set_title('Integrated Signal with Adaptive Threshold')
        axes[4].set_ylabel('Amplitude')
        axes[4].legend()
        axes[4].grid(True, alpha=0.3)
        
        # Original signal with detected R-peaks
        axes[5].plot(time_axis, ecg_signal, 'b-', linewidth=0.8, label='ECG Signal')
        if len(processing_info['r_peaks']) > 0:
            r_peak_times = processing_info['r_peaks'] / self.sampling_rate
            axes[5].plot(r_peak_times, ecg_signal[processing_info['r_peaks']], 
                        'ro', markersize=6, label=f'R-peaks ({len(processing_info["r_peaks"])})')
        axes[5].set_title('R-peak Detection Results')
        axes[5].set_xlabel('Time (s)')
        axes[5].set_ylabel('Amplitude')
        axes[5].legend()
        axes[5].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def validate_r_peaks(self, r_peaks: np.ndarray, 
                        signal_length: int,
                        min_hr: float = 40.0,
                        max_hr: float = 200.0) -> Tuple[np.ndarray, Dict]:
        """
        Validate detected R-peaks and remove artifacts
        
        Args:
            r_peaks: Detected R-peak indices
            signal_length: Length of ECG signal
            min_hr: Minimum heart rate (bpm)
            max_hr: Maximum heart rate (bpm)
            
        Returns:
            Tuple of (validated_r_peaks, validation_info)
        """
        validation_info = {
            'original_count': len(r_peaks),
            'removed_peaks': [],
            'added_peaks': [],
            'final_count': 0
        }
        
        try:
            if len(r_peaks) < 2:
                logger.warning("Insufficient R-peaks for validation")
                return r_peaks, validation_info
            
            # Calculate RR intervals
            rr_intervals = np.diff(r_peaks) / self.sampling_rate
            
            # Convert heart rate limits to RR interval limits
            max_rr = 60.0 / min_hr  # Maximum RR interval
            min_rr = 60.0 / max_hr  # Minimum RR interval
            
            # Remove peaks with invalid RR intervals
            valid_peaks = [r_peaks[0]]  # Always keep first peak
            
            for i in range(1, len(r_peaks)):
                current_rr = rr_intervals[i-1]
                
                if min_rr <= current_rr <= max_rr:
                    valid_peaks.append(r_peaks[i])
                else:
                    validation_info['removed_peaks'].append(r_peaks[i])
            
            validated_r_peaks = np.array(valid_peaks)
            validation_info['final_count'] = len(validated_r_peaks)
            
            logger.info(f"Validation: {validation_info['original_count']} -> "
                       f"{validation_info['final_count']} R-peaks")
            
            return validated_r_peaks, validation_info
            
        except Exception as e:
            logger.error(f"Error in R-peak validation: {e}")
            return r_peaks, validation_info

# Create the HRV analyzer class
class HRVAnalyzer:
    """
    Heart Rate Variability analyzer with comprehensive time and frequency domain features
    """
    
    def __init__(self, sampling_rate: float = 360.0):
        """
        Initialize HRV analyzer
        
        Args:
            sampling_rate: Sampling rate in Hz
        """
        self.sampling_rate = sampling_rate
    
    def calculate_rr_intervals(self, r_peaks: np.ndarray) -> np.ndarray:
        """
        Calculate RR intervals from R-peaks
        
        Args:
            r_peaks: R-peak indices
            
        Returns:
            RR intervals in seconds
        """
        try:
            if len(r_peaks) < 2:
                raise ValueError("Need at least 2 R-peaks to calculate RR intervals")
            
            rr_intervals = np.diff(r_peaks) / self.sampling_rate
            return rr_intervals
            
        except Exception as e:
            logger.error(f"Error calculating RR intervals: {e}")
            return np.array([])
    
    def time_domain_features(self, rr_intervals: np.ndarray) -> Dict[str, float]:
        """
        Calculate comprehensive time domain HRV features
        
        Args:
            rr_intervals: RR intervals in seconds
            
        Returns:
            Dictionary of time domain features
        """
        features = {}
        
        try:
            if len(rr_intervals) == 0:
                logger.warning("No RR intervals provided for time domain analysis")
                return features
            
            # Convert to milliseconds for clinical relevance
            rr_ms = rr_intervals * 1000
            
            # Basic statistics
            features['mean_rr'] = np.mean(rr_ms)
            features['std_rr'] = np.std(rr_ms)
            features['min_rr'] = np.min(rr_ms)
            features['max_rr'] = np.max(rr_ms)
            features['median_rr'] = np.median(rr_ms)
            
            # Heart rate statistics
            hr = 60000 / rr_ms  # Heart rate in bpm
            features['mean_hr'] = np.mean(hr)
            features['std_hr'] = np.std(hr)
            features['min_hr'] = np.min(hr)
            features['max_hr'] = np.max(hr)
            
            # SDNN: Standard deviation of NN intervals
            features['sdnn'] = np.std(rr_ms)
            
            # RMSSD: Root mean square of successive differences
            if len(rr_ms) > 1:
                diff_rr = np.diff(rr_ms)
                features['rmssd'] = np.sqrt(np.mean(diff_rr**2))
            else:
                features['rmssd'] = 0.0
            
            # pNN50: Percentage of successive RR intervals differing by more than 50ms
            if len(rr_ms) > 1:
                diff_rr = np.abs(np.diff(rr_ms))
                features['pnn50'] = (np.sum(diff_rr > 50) / len(diff_rr)) * 100
            else:
                features['pnn50'] = 0.0
            
            # pNN20: Percentage of successive RR intervals differing by more than 20ms
            if len(rr_ms) > 1:
                diff_rr = np.abs(np.diff(rr_ms))
                features['pnn20'] = (np.sum(diff_rr > 20) / len(diff_rr)) * 100
            else:
                features['pnn20'] = 0.0
            
            # Triangular index: Total number of RR intervals / height of histogram
            if len(rr_ms) > 0:
                hist, bins = np.histogram(rr_ms, bins=int(np.max(rr_ms) - np.min(rr_ms)))
                if np.max(hist) > 0:
                    features['triangular_index'] = len(rr_ms) / np.max(hist)
                else:
                    features['triangular_index'] = 0.0
            
            # Coefficient of variation
            if features['mean_rr'] > 0:
                features['cv'] = (features['std_rr'] / features['mean_rr']) * 100
            else:
                features['cv'] = 0.0
            
            logger.info(f"Calculated {len(features)} time domain features")
            
        except Exception as e:
            logger.error(f"Error calculating time domain features: {e}")
        
        return features
    
    def frequency_domain_features(self, rr_intervals: np.ndarray,
                                interpolation_rate: float = 4.0,
                                method: str = 'welch') -> Dict[str, float]:
        """
        Calculate frequency domain HRV features using power spectral density
        
        Args:
            rr_intervals: RR intervals in seconds
            interpolation_rate: Interpolation rate in Hz
            method: PSD estimation method ('welch' or 'fft')
            
        Returns:
            Dictionary of frequency domain features
        """
        features = {}
        
        try:
            if len(rr_intervals) < 10:
                logger.warning("Insufficient RR intervals for frequency domain analysis")
                return features
            
            # Convert to milliseconds
            rr_ms = rr_intervals * 1000
            
            # Create time vector for RR intervals
            rr_times = np.cumsum(rr_intervals)
            rr_times = np.insert(rr_times, 0, 0)  # Add starting point
            
            # Interpolate RR intervals to uniform sampling
            time_uniform = np.arange(0, rr_times[-1], 1/interpolation_rate)
            
            # Handle edge case where time_uniform might be empty
            if len(time_uniform) == 0:
                logger.warning("Time vector is empty after interpolation")
                return features
            
            # Interpolate using cubic spline
            try:
                f_interp = interpolate.interp1d(rr_times[:-1], rr_ms, 
                                              kind='cubic', 
                                              bounds_error=False, 
                                              fill_value='extrapolate')
                rr_interpolated = f_interp(time_uniform)
            except:
                # Fallback to linear interpolation
                f_interp = interpolate.interp1d(rr_times[:-1], rr_ms, 
                                              kind='linear', 
                                              bounds_error=False, 
                                              fill_value='extrapolate')
                rr_interpolated = f_interp(time_uniform)
            
            # Remove DC component
            rr_interpolated = rr_interpolated - np.mean(rr_interpolated)
            
            # Calculate power spectral density
            if method == 'welch':
                freqs, psd = welch(rr_interpolated, fs=interpolation_rate,
                                 nperseg=min(256, len(rr_interpolated)//2),
                                 noverlap=None, nfft=None)
            else:  # FFT method
                n = len(rr_interpolated)
                freqs = np.fft.fftfreq(n, 1/interpolation_rate)
                fft_vals = np.fft.fft(rr_interpolated)
                psd = np.abs(fft_vals)**2 / n
                
                # Keep only positive frequencies
                freqs = freqs[:n//2]
                psd = psd[:n//2]
            
            # Define frequency bands (in Hz)
            vlf_band = (0.0033, 0.04)   # Very low frequency
            lf_band = (0.04, 0.15)      # Low frequency
            hf_band = (0.15, 0.4)       # High frequency
            
            # Calculate power in each band
            vlf_mask = (freqs >= vlf_band[0]) & (freqs <= vlf_band[1])
            lf_mask = (freqs >= lf_band[0]) & (freqs <= lf_band[1])
            hf_mask = (freqs >= hf_band[0]) & (freqs <= hf_band[1])
            
            # Integrate power using trapezoidal rule
            features['vlf_power'] = np.trapz(psd[vlf_mask], freqs[vlf_mask]) if np.any(vlf_mask) else 0.0
            features['lf_power'] = np.trapz(psd[lf_mask], freqs[lf_mask]) if np.any(lf_mask) else 0.0
            features['hf_power'] = np.trapz(psd[hf_mask], freqs[hf_mask]) if np.any(hf_mask) else 0.0
            
            # Total power
            features['total_power'] = features['vlf_power'] + features['lf_power'] + features['hf_power']
            
            # Relative powers (normalized)
            if features['total_power'] > 0:
                features['vlf_power_rel'] = features['vlf_power'] / features['total_power']
                features['lf_power_rel'] = features['lf_power'] / features['total_power']
                features['hf_power_rel'] = features['hf_power'] / features['total_power']
            else:
                features['vlf_power_rel'] = 0.0
                features['lf_power_rel'] = 0.0
                features['hf_power_rel'] = 0.0
            
            # LF/HF ratio
            if features['hf_power'] > 0:
                features['lf_hf_ratio'] = features['lf_power'] / features['hf_power']
            else:
                features['lf_hf_ratio'] = 0.0
            
            # Peak frequencies
            if np.any(lf_mask) and np.max(psd[lf_mask]) > 0:
                lf_peak_idx = np.argmax(psd[lf_mask])
                features['lf_peak_freq'] = freqs[lf_mask][lf_peak_idx]
            else:
                features['lf_peak_freq'] = 0.0
            
            if np.any(hf_mask) and np.max(psd[hf_mask]) > 0:
                hf_peak_idx = np.argmax(psd[hf_mask])
                features['hf_peak_freq'] = freqs[hf_mask][hf_peak_idx]
            else:
                features['hf_peak_freq'] = 0.0
            
            # Store PSD data for plotting
            features['_psd_data'] = {
                'freqs': freqs,
                'psd': psd,
                'interpolated_rr': rr_interpolated,
                'time_uniform': time_uniform
            }
            
            logger.info(f"Calculated {len([k for k in features.keys() if not k.startswith('_')])} frequency domain features")
            
        except Exception as e:
            logger.error(f"Error calculating frequency domain features: {e}")
        
        return features
    
    def analyze_hrv(self, r_peaks: np.ndarray, 
                   signal_length: int,
                   min_rr_interval: float = 0.3,
                   max_rr_interval: float = 2.0) -> Dict[str, Union[float, Dict]]:
        """
        Comprehensive HRV analysis with robust error handling
        
        Args:
            r_peaks: R-peak indices
            signal_length: Length of original ECG signal
            min_rr_interval: Minimum valid RR interval in seconds
            max_rr_interval: Maximum valid RR interval in seconds
            
        Returns:
            Dictionary containing all HRV features and analysis results
        """
        analysis_results = {
            'status': 'success',
            'n_peaks': len(r_peaks),
            'signal_duration': signal_length / self.sampling_rate,
            'time_domain': {},
            'frequency_domain': {},
            'quality_metrics': {}
        }
        
        try:
            # Check for sufficient R-peaks
            if len(r_peaks) < 3:
                analysis_results['status'] = 'insufficient_peaks'
                analysis_results['error'] = f"Need at least 3 R-peaks, got {len(r_peaks)}"
                logger.warning(analysis_results['error'])
                return analysis_results
            
            # Calculate RR intervals
            rr_intervals = self.calculate_rr_intervals(r_peaks)
            
            if len(rr_intervals) == 0:
                analysis_results['status'] = 'no_rr_intervals'
                analysis_results['error'] = "Could not calculate RR intervals"
                logger.error(analysis_results['error'])
                return analysis_results
            
            # Filter out invalid RR intervals
            valid_mask = (rr_intervals >= min_rr_interval) & (rr_intervals <= max_rr_interval)
            valid_rr_intervals = rr_intervals[valid_mask]
            
            analysis_results['n_valid_rr'] = len(valid_rr_intervals)
            analysis_results['n_invalid_rr'] = len(rr_intervals) - len(valid_rr_intervals)
            
            if len(valid_rr_intervals) < 2:
                analysis_results['status'] = 'insufficient_valid_rr'
                analysis_results['error'] = f"Need at least 2 valid RR intervals, got {len(valid_rr_intervals)}"
                logger.warning(analysis_results['error'])
                return analysis_results
            
            # Calculate time domain features
            analysis_results['time_domain'] = self.time_domain_features(valid_rr_intervals)
            
            # Calculate frequency domain features (need more RR intervals)
            if len(valid_rr_intervals) >= 10:
                analysis_results['frequency_domain'] = self.frequency_domain_features(valid_rr_intervals)
            else:
                analysis_results['frequency_domain'] = {}
                logger.warning("Insufficient RR intervals for frequency domain analysis")
            
            # Calculate quality metrics
            analysis_results['quality_metrics'] = self._calculate_quality_metrics(
                valid_rr_intervals, rr_intervals
            )
            
            logger.info(f"HRV analysis completed successfully for {len(valid_rr_intervals)} RR intervals")
            
        except Exception as e:
            analysis_results['status'] = 'error'
            analysis_results['error'] = str(e)
            logger.error(f"Error in HRV analysis: {e}")
        
        return analysis_results
    
    def _calculate_quality_metrics(self, valid_rr_intervals: np.ndarray, 
                                 all_rr_intervals: np.ndarray) -> Dict[str, float]:
        """
        Calculate quality metrics for HRV analysis
        
        Args:
            valid_rr_intervals: Valid RR intervals
            all_rr_intervals: All RR intervals (including invalid ones)
            
        Returns:
            Dictionary of quality metrics
        """
        metrics = {}
        
        try:
            # Data completeness
            metrics['data_completeness'] = len(valid_rr_intervals) / len(all_rr_intervals) if len(all_rr_intervals) > 0 else 0.0
            
            # Coefficient of variation (should be reasonable for normal HRV)
            if len(valid_rr_intervals) > 0:
                mean_rr = np.mean(valid_rr_intervals)
                std_rr = np.std(valid_rr_intervals)
                metrics['cv_rr'] = (std_rr / mean_rr) * 100 if mean_rr > 0 else 0.0
            else:
                metrics['cv_rr'] = 0.0
            
            # Artifact detection (extreme values)
            if len(valid_rr_intervals) > 2:
                q1 = np.percentile(valid_rr_intervals, 25)
                q3 = np.percentile(valid_rr_intervals, 75)
                iqr = q3 - q1
                
                outlier_threshold_low = q1 - 1.5 * iqr
                outlier_threshold_high = q3 + 1.5 * iqr
                
                outliers = np.sum((valid_rr_intervals < outlier_threshold_low) | 
                                (valid_rr_intervals > outlier_threshold_high))
                metrics['outlier_percentage'] = (outliers / len(valid_rr_intervals)) * 100
            else:
                metrics['outlier_percentage'] = 0.0
            
            # Signal quality score (0-100)
            quality_score = 100 * metrics['data_completeness']
            quality_score -= min(metrics['outlier_percentage'], 50)  # Penalize outliers
            
            # Penalize extreme CV values
            if metrics['cv_rr'] > 20 or metrics['cv_rr'] < 2:
                quality_score -= 20
            
            metrics['quality_score'] = max(0, quality_score)
            
        except Exception as e:
            logger.error(f"Error calculating quality metrics: {e}")
        
        return metrics
    
    def plot_hrv_analysis(self, analysis_results: Dict, 
                         r_peaks: np.ndarray = None,
                         ecg_signal: np.ndarray = None) -> None:
        """
        Plot comprehensive HRV analysis results
        
        Args:
            analysis_results: Results from analyze_hrv()
            r_peaks: R-peak indices (optional, for ECG plot)
            ecg_signal: Original ECG signal (optional, for ECG plot)
        """
        try:
            if analysis_results['status'] != 'success':
                print(f"Cannot plot: {analysis_results.get('error', 'Unknown error')}")
                return
            
            # Calculate RR intervals for plotting
            if r_peaks is not None and len(r_peaks) > 1:
                rr_intervals = np.diff(r_peaks) / self.sampling_rate * 1000  # Convert to ms
                rr_times = np.cumsum(np.diff(r_peaks) / self.sampling_rate)
            else:
                logger.warning("No R-peaks provided for plotting")
                return
            
            # Create figure with subplots
            fig = plt.figure(figsize=(15, 12))
            
            # Plot 1: ECG signal with R-peaks (if available)
            if ecg_signal is not None and r_peaks is not None:
                ax1 = plt.subplot(3, 2, 1)
                time_axis = np.arange(len(ecg_signal)) / self.sampling_rate
                plt.plot(time_axis, ecg_signal, 'b-', alpha=0.7, label='ECG Signal')
                r_peak_times = r_peaks / self.sampling_rate
                plt.plot(r_peak_times, ecg_signal[r_peaks], 'ro', markersize=6, 
                        label=f'R-peaks ({len(r_peaks)})')
                plt.xlabel('Time (s)')
                plt.ylabel('Amplitude')
                plt.title('ECG Signal with Detected R-peaks')
                plt.legend()
                plt.grid(True, alpha=0.3)
            
            # Plot 2: RR interval tachogram
            ax2 = plt.subplot(3, 2, 2)
            plt.plot(rr_times, rr_intervals, 'b-o', markersize=4, linewidth=1)
            plt.xlabel('Time (s)')
            plt.ylabel('RR Interval (ms)')
            plt.title('RR Interval Tachogram')
            plt.grid(True, alpha=0.3)
            
            # Plot 3: RR interval histogram
            ax3 = plt.subplot(3, 2, 3)
            plt.hist(rr_intervals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
            plt.xlabel('RR Interval (ms)')
            plt.ylabel('Frequency')
            plt.title('RR Interval Distribution')
            plt.grid(True, alpha=0.3)
            
            # Plot 4: Poincaré plot
            ax4 = plt.subplot(3, 2, 4)
            if len(rr_intervals) > 1:
                plt.scatter(rr_intervals[:-1], rr_intervals[1:], alpha=0.6, s=30)
                plt.xlabel('RR_n (ms)')
                plt.ylabel('RR_n+1 (ms)')
                plt.title('Poincaré Plot')
                plt.grid(True, alpha=0.3)
            
            # Plot 5: Power spectral density (if available)
            if analysis_results['frequency_domain'] and '_psd_data' in analysis_results['frequency_domain']:
                ax5 = plt.subplot(3, 2, 5)
                psd_data = analysis_results['frequency_domain']['_psd_data']
                freqs = psd_data['freqs']
                psd = psd_data['psd']
                
                plt.semilogy(freqs, psd, 'b-', linewidth=1)
                
                # Highlight frequency bands
                vlf_band = (0.0033, 0.04)
                lf_band = (0.04, 0.15)
                hf_band = (0.15, 0.4)
                
                plt.axvspan(vlf_band[0], vlf_band[1], alpha=0.2, color='red', label='VLF')
                plt.axvspan(lf_band[0], lf_band[1], alpha=0.2, color='yellow', label='LF')
                plt.axvspan(hf_band[0], hf_band[1], alpha=0.2, color='green', label='HF')
                
                plt.xlabel('Frequency (Hz)')
                plt.ylabel('Power Spectral Density')
                plt.title('Power Spectral Density')
                plt.legend()
                plt.grid(True, alpha=0.3)
                plt.xlim(0, 0.5)
            
            # Plot 6: Feature summary
            ax6 = plt.subplot(3, 2, 6)
            ax6.axis('off')
            
            # Create feature summary text
            summary_text = "HRV Analysis Summary\n" + "="*25 + "\n\n"
            
            # Time domain features
            td_features = analysis_results['time_domain']
            summary_text += "Time Domain Features:\n"
            summary_text += f"SDNN: {td_features.get('sdnn', 0):.1f} ms\n"
            summary_text += f"RMSSD: {td_features.get('rmssd', 0):.1f} ms\n"
            summary_text += f"pNN50: {td_features.get('pnn50', 0):.1f}%\n"
            summary_text += f"Mean HR: {td_features.get('mean_hr', 0):.1f} bpm\n\n"
            
            # Frequency domain features
            fd_features = analysis_results['frequency_domain']
            if fd_features:
                summary_text += "Frequency Domain Features:\n"
                summary_text += f"LF Power: {fd_features.get('lf_power', 0):.1f} ms²\n"
                summary_text += f"HF Power: {fd_features.get('hf_power', 0):.1f} ms²\n"
                summary_text += f"LF/HF Ratio: {fd_features.get('lf_hf_ratio', 0):.2f}\n\n"
            
            # Quality metrics
            quality = analysis_results['quality_metrics']
            summary_text += "Quality Metrics:\n"
            summary_text += f"Data Completeness: {quality.get('data_completeness', 0)*100:.1f}%\n"
            summary_text += f"Quality Score: {quality.get('quality_score', 0):.1f}/100\n"
            
            ax6.text(0.05, 0.95, summary_text, transform=ax6.transAxes, 
                    verticalalignment='top', fontsize=10, fontfamily='monospace')
            
            plt.tight_layout()
            plt.show()
            
        except Exception as e:
            logger.error(f"Error plotting HRV analysis: {e}")