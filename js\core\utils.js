/**
 * Utility Functions - Biosignal Virtual Lab
 * Common utility functions used across the platform
 */

/**
 * Mathematical utilities
 */
const MathUtils = {
    /**
     * Calculate mean of an array
     */
    mean(arr) {
        if (!arr || arr.length === 0) return 0;
        return arr.reduce((sum, val) => sum + val, 0) / arr.length;
    },
    
    /**
     * Calculate standard deviation
     */
    std(arr) {
        if (!arr || arr.length === 0) return 0;
        const mean = this.mean(arr);
        const variance = arr.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / arr.length;
        return Math.sqrt(variance);
    },
    
    /**
     * Calculate root mean square of successive differences
     */
    rmssd(arr) {
        if (!arr || arr.length < 2) return 0;
        const diffs = [];
        for (let i = 1; i < arr.length; i++) {
            diffs.push(Math.pow(arr[i] - arr[i-1], 2));
        }
        return Math.sqrt(this.mean(diffs));
    },
    
    /**
     * Calculate percentage of successive differences > threshold
     */
    pnnx(arr, threshold) {
        if (!arr || arr.length < 2) return 0;
        let count = 0;
        for (let i = 1; i < arr.length; i++) {
            if (Math.abs(arr[i] - arr[i-1]) > threshold) {
                count++;
            }
        }
        return (count / (arr.length - 1)) * 100;
    },
    
    /**
     * Find peaks in signal
     */
    findPeaks(signal, minHeight = 0, minDistance = 1) {
        const peaks = [];
        for (let i = 1; i < signal.length - 1; i++) {
            if (signal[i] > signal[i-1] && 
                signal[i] > signal[i+1] && 
                signal[i] > minHeight) {
                
                // Check minimum distance
                if (peaks.length === 0 || i - peaks[peaks.length - 1] >= minDistance) {
                    peaks.push(i);
                }
            }
        }
        return peaks;
    },
    
    /**
     * Apply moving average filter
     */
    movingAverage(signal, windowSize) {
        const filtered = [];
        const halfWindow = Math.floor(windowSize / 2);
        
        for (let i = 0; i < signal.length; i++) {
            let sum = 0;
            let count = 0;
            
            for (let j = Math.max(0, i - halfWindow); 
                 j <= Math.min(signal.length - 1, i + halfWindow); 
                 j++) {
                sum += signal[j];
                count++;
            }
            
            filtered.push(sum / count);
        }
        
        return filtered;
    },
    
    /**
     * Normalize signal to range [0, 1]
     */
    normalize(signal) {
        const min = Math.min(...signal);
        const max = Math.max(...signal);
        const range = max - min;
        
        if (range === 0) return signal.map(() => 0);
        
        return signal.map(val => (val - min) / range);
    },
    
    /**
     * Calculate correlation coefficient
     */
    correlation(x, y) {
        if (x.length !== y.length) return 0;
        
        const n = x.length;
        const meanX = this.mean(x);
        const meanY = this.mean(y);
        
        let numerator = 0;
        let sumXSquared = 0;
        let sumYSquared = 0;
        
        for (let i = 0; i < n; i++) {
            const deltaX = x[i] - meanX;
            const deltaY = y[i] - meanY;
            
            numerator += deltaX * deltaY;
            sumXSquared += deltaX * deltaX;
            sumYSquared += deltaY * deltaY;
        }
        
        const denominator = Math.sqrt(sumXSquared * sumYSquared);
        return denominator === 0 ? 0 : numerator / denominator;
    }
};

/**
 * Signal processing utilities
 */
const SignalUtils = {
    /**
     * Apply bandpass filter (simplified)
     */
    bandpassFilter(signal, lowFreq, highFreq, samplingRate) {
        // Simplified bandpass filter implementation
        // In a real application, you would use a proper filter design
        const nyquist = samplingRate / 2;
        const low = lowFreq / nyquist;
        const high = highFreq / nyquist;
        
        // Simple frequency domain filtering
        const fft = this.fft(signal);
        const filtered = fft.map((val, i) => {
            const freq = i / fft.length;
            if (freq >= low && freq <= high) {
                return val;
            }
            return { real: 0, imag: 0 };
        });
        
        return this.ifft(filtered).map(c => c.real);
    },
    
    /**
     * Simple FFT implementation (for demonstration)
     */
    fft(signal) {
        const N = signal.length;
        if (N <= 1) return signal.map(x => ({ real: x, imag: 0 }));
        
        // Simplified FFT - in practice, use a proper FFT library
        const result = [];
        for (let k = 0; k < N; k++) {
            let real = 0, imag = 0;
            for (let n = 0; n < N; n++) {
                const angle = -2 * Math.PI * k * n / N;
                real += signal[n] * Math.cos(angle);
                imag += signal[n] * Math.sin(angle);
            }
            result.push({ real, imag });
        }
        return result;
    },
    
    /**
     * Simple IFFT implementation
     */
    ifft(spectrum) {
        const N = spectrum.length;
        const result = [];
        
        for (let n = 0; n < N; n++) {
            let real = 0, imag = 0;
            for (let k = 0; k < N; k++) {
                const angle = 2 * Math.PI * k * n / N;
                real += spectrum[k].real * Math.cos(angle) - spectrum[k].imag * Math.sin(angle);
                imag += spectrum[k].real * Math.sin(angle) + spectrum[k].imag * Math.cos(angle);
            }
            result.push({ real: real / N, imag: imag / N });
        }
        
        return result;
    },
    
    /**
     * Calculate power spectral density
     */
    powerSpectralDensity(signal, samplingRate) {
        const fft = this.fft(signal);
        const psd = fft.map(c => c.real * c.real + c.imag * c.imag);
        const frequencies = fft.map((_, i) => i * samplingRate / fft.length);
        
        return { frequencies, psd };
    }
};

/**
 * Data validation utilities
 */
const ValidationUtils = {
    /**
     * Validate ECG signal
     */
    validateECGSignal(signal) {
        const errors = [];
        
        if (!Array.isArray(signal)) {
            errors.push('Signal must be an array');
            return { valid: false, errors };
        }
        
        if (signal.length < 100) {
            errors.push('Signal too short (minimum 100 samples)');
        }
        
        if (signal.some(val => typeof val !== 'number' || isNaN(val))) {
            errors.push('Signal contains non-numeric values');
        }
        
        const range = Math.max(...signal) - Math.min(...signal);
        if (range === 0) {
            errors.push('Signal has no variation');
        }
        
        return { valid: errors.length === 0, errors };
    },
    
    /**
     * Validate sampling rate
     */
    validateSamplingRate(rate) {
        if (typeof rate !== 'number' || isNaN(rate)) {
            return { valid: false, error: 'Sampling rate must be a number' };
        }
        
        if (rate <= 0) {
            return { valid: false, error: 'Sampling rate must be positive' };
        }
        
        if (rate < 100) {
            return { valid: false, error: 'Sampling rate too low (minimum 100 Hz)' };
        }
        
        return { valid: true };
    },
    
    /**
     * Validate R-peaks
     */
    validateRPeaks(peaks, signalLength) {
        const errors = [];
        
        if (!Array.isArray(peaks)) {
            errors.push('R-peaks must be an array');
            return { valid: false, errors };
        }
        
        if (peaks.length < 2) {
            errors.push('Need at least 2 R-peaks for analysis');
        }
        
        if (peaks.some(peak => peak < 0 || peak >= signalLength)) {
            errors.push('R-peak indices out of signal range');
        }
        
        // Check for duplicate peaks
        const uniquePeaks = [...new Set(peaks)];
        if (uniquePeaks.length !== peaks.length) {
            errors.push('Duplicate R-peak indices found');
        }
        
        return { valid: errors.length === 0, errors };
    }
};

/**
 * File handling utilities
 */
const FileUtils = {
    /**
     * Read file as text
     */
    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = e => reject(e);
            reader.readAsText(file);
        });
    },
    
    /**
     * Parse CSV data
     */
    parseCSV(csvText, delimiter = ',') {
        const lines = csvText.trim().split('\n');
        const data = [];
        
        for (const line of lines) {
            const values = line.split(delimiter).map(val => {
                const num = parseFloat(val.trim());
                return isNaN(num) ? val.trim() : num;
            });
            data.push(values);
        }
        
        return data;
    },
    
    /**
     * Download data as file
     */
    downloadAsFile(data, filename, type = 'text/plain') {
        const blob = new Blob([data], { type });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
    },
    
    /**
     * Format data as CSV
     */
    formatAsCSV(data, headers = null) {
        let csv = '';
        
        if (headers) {
            csv += headers.join(',') + '\n';
        }
        
        for (const row of data) {
            csv += row.join(',') + '\n';
        }
        
        return csv;
    }
};

/**
 * Format utilities
 */
const FormatUtils = {
    /**
     * Format number with specified decimal places
     */
    formatNumber(num, decimals = 2) {
        if (typeof num !== 'number' || isNaN(num)) return 'N/A';
        return num.toFixed(decimals);
    },
    
    /**
     * Format time duration
     */
    formatDuration(seconds) {
        if (seconds < 60) {
            return `${seconds.toFixed(1)}s`;
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }
    },
    
    /**
     * Format file size
     */
    formatFileSize(bytes) {
        const units = ['B', 'KB', 'MB', 'GB'];
        let size = bytes;
        let unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return `${size.toFixed(1)} ${units[unitIndex]}`;
    },
    
    /**
     * Format timestamp
     */
    formatTimestamp(date) {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }
        
        return date.toLocaleString();
    }
};

// Export utilities to global scope
window.MathUtils = MathUtils;
window.SignalUtils = SignalUtils;
window.ValidationUtils = ValidationUtils;
window.FileUtils = FileUtils;
window.FormatUtils = FormatUtils;
