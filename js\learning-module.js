/**
 * Learning Module JavaScript - Biosignal Virtual Lab
 * Interactive slide-based learning system with animations and demos
 * 
 * Author: Dr. <PERSON>
 * Institution: SUST - BME
 * Year: 2025
 * Contact: <EMAIL>
 * 
 * Copyright © 2025 Dr. <PERSON> Esmail. All rights reserved.
 */

class LearningModule {
    constructor() {
        this.swiper = null;
        this.currentSlide = 0;
        this.totalSlides = 12;
        this.animations = {};
        this.isECGAnimating = false;
        
        this.initializeModule();
    }
    
    /**
     * Initialize the learning module
     */
    initializeModule() {
        console.log('Initializing Learning Module...');
        
        this.initializeSwiper();
        this.initializeAnimations();
        this.bindEvents();
        this.startPreviewAnimation();
        this.generateThumbnails();
        
        // Hide loading overlay
        setTimeout(() => {
            this.hideLoading();
        }, 1000);
    }
    
    /**
     * Initialize Swiper slider
     */
    initializeSwiper() {
        this.swiper = new Swiper('.learning-swiper', {
            slidesPerView: 1,
            spaceBetween: 30,
            centeredSlides: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                dynamicBullets: true,
            },
            navigation: {
                nextEl: '.next-btn',
                prevEl: '.prev-btn',
            },
            on: {
                slideChange: (swiper) => {
                    this.onSlideChange(swiper.activeIndex);
                },
                slideChangeTransitionEnd: (swiper) => {
                    this.onSlideTransitionEnd(swiper.activeIndex);
                }
            },
            effect: 'slide',
            speed: 600,
            allowTouchMove: true,
            keyboard: {
                enabled: true,
                onlyInViewport: true,
            }
        });
    }
    
    /**
     * Initialize canvas animations
     */
    initializeAnimations() {
        this.initializePreviewCanvas();
        this.initializeBiosignalCanvas();
        this.initializeECGWaveformCanvas();
        this.initializeSamplingCanvas();
    }
    
    /**
     * Initialize preview canvas animation
     */
    initializePreviewCanvas() {
        const canvas = document.getElementById('previewCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        let time = 0;
        
        const animate = () => {
            ctx.clearRect(0, 0, width, height);
            
            // Draw ECG-like waveform
            ctx.strokeStyle = '#6366f1';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            for (let x = 0; x < width; x++) {
                const t = (x / width) * 4 * Math.PI + time;
                let y = height / 2;
                
                // Create ECG-like pattern
                const heartbeat = Math.sin(t * 1.2) * 0.3;
                const qrs = Math.sin(t * 8) * 0.7 * Math.exp(-Math.pow((t % (2 * Math.PI)) - Math.PI, 2) * 2);
                
                y += (heartbeat + qrs) * height * 0.3;
                
                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
            
            time += 0.05;
            requestAnimationFrame(animate);
        };
        
        animate();
    }
    
    /**
     * Initialize biosignal canvas
     */
    initializeBiosignalCanvas() {
        const canvas = document.getElementById('biosignalCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        let time = 0;
        const signals = [
            { color: '#ef4444', frequency: 1.2, amplitude: 0.3, offset: 0 },      // ECG
            { color: '#10b981', frequency: 8, amplitude: 0.2, offset: height/3 }, // EEG
            { color: '#f59e0b', frequency: 4, amplitude: 0.25, offset: 2*height/3 } // EMG
        ];
        
        const animate = () => {
            ctx.clearRect(0, 0, width, height);
            
            signals.forEach((signal, index) => {
                ctx.strokeStyle = signal.color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                for (let x = 0; x < width; x++) {
                    const t = (x / width) * 4 * Math.PI + time;
                    let y = signal.offset + height/6;
                    
                    if (index === 0) {
                        // ECG pattern
                        const heartbeat = Math.sin(t * signal.frequency) * signal.amplitude;
                        const qrs = Math.sin(t * 6) * 0.5 * Math.exp(-Math.pow((t % (2 * Math.PI)) - Math.PI, 2) * 3);
                        y += (heartbeat + qrs) * height * 0.4;
                    } else {
                        // Other signals
                        y += Math.sin(t * signal.frequency) * signal.amplitude * height * 0.4;
                        y += Math.sin(t * signal.frequency * 2.3) * signal.amplitude * height * 0.2;
                    }
                    
                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                
                ctx.stroke();
            });
            
            time += 0.03;
            requestAnimationFrame(animate);
        };
        
        animate();
    }
    
    /**
     * Initialize ECG waveform canvas
     */
    initializeECGWaveformCanvas() {
        const canvas = document.getElementById('ecgWaveformCanvas');
        if (!canvas) return;
        
        this.ecgCanvas = canvas;
        this.ecgCtx = canvas.getContext('2d');
        this.ecgTime = 0;
        this.ecgAnimationId = null;
        
        this.drawECGWaveform();
    }
    
    /**
     * Draw ECG waveform
     */
    drawECGWaveform() {
        if (!this.ecgCtx) return;
        
        const width = this.ecgCanvas.width;
        const height = this.ecgCanvas.height;
        
        this.ecgCtx.clearRect(0, 0, width, height);
        
        // Draw grid
        this.drawECGGrid();
        
        // Draw ECG waveform
        this.ecgCtx.strokeStyle = '#ef4444';
        this.ecgCtx.lineWidth = 2;
        this.ecgCtx.beginPath();
        
        for (let x = 0; x < width; x++) {
            const t = (x / width) * 6 * Math.PI + this.ecgTime;
            let y = height / 2;
            
            // P wave
            const pWave = this.generatePWave(t) * height * 0.1;
            // QRS complex
            const qrsComplex = this.generateQRSComplex(t) * height * 0.3;
            // T wave
            const tWave = this.generateTWave(t) * height * 0.15;
            
            y += pWave + qrsComplex + tWave;
            
            if (x === 0) {
                this.ecgCtx.moveTo(x, y);
            } else {
                this.ecgCtx.lineTo(x, y);
            }
        }
        
        this.ecgCtx.stroke();
        
        if (this.isECGAnimating) {
            this.ecgTime += 0.02;
            this.ecgAnimationId = requestAnimationFrame(() => this.drawECGWaveform());
        }
    }
    
    /**
     * Draw ECG grid
     */
    drawECGGrid() {
        const width = this.ecgCanvas.width;
        const height = this.ecgCanvas.height;
        
        this.ecgCtx.strokeStyle = 'rgba(200, 200, 200, 0.3)';
        this.ecgCtx.lineWidth = 1;
        
        // Vertical lines
        for (let x = 0; x < width; x += 20) {
            this.ecgCtx.beginPath();
            this.ecgCtx.moveTo(x, 0);
            this.ecgCtx.lineTo(x, height);
            this.ecgCtx.stroke();
        }
        
        // Horizontal lines
        for (let y = 0; y < height; y += 20) {
            this.ecgCtx.beginPath();
            this.ecgCtx.moveTo(0, y);
            this.ecgCtx.lineTo(width, y);
            this.ecgCtx.stroke();
        }
    }
    
    /**
     * Generate P wave
     */
    generatePWave(t) {
        const cycle = t % (2 * Math.PI);
        if (cycle > 0.5 && cycle < 1.2) {
            return Math.sin((cycle - 0.5) * Math.PI / 0.7) * 0.5;
        }
        return 0;
    }
    
    /**
     * Generate QRS complex
     */
    generateQRSComplex(t) {
        const cycle = t % (2 * Math.PI);
        if (cycle > 1.8 && cycle < 2.3) {
            const phase = (cycle - 1.8) / 0.5;
            if (phase < 0.2) {
                return -Math.sin(phase * Math.PI / 0.2) * 0.3; // Q wave
            } else if (phase < 0.6) {
                return Math.sin((phase - 0.2) * Math.PI / 0.4) * 2; // R wave
            } else {
                return -Math.sin((phase - 0.6) * Math.PI / 0.4) * 0.5; // S wave
            }
        }
        return 0;
    }
    
    /**
     * Generate T wave
     */
    generateTWave(t) {
        const cycle = t % (2 * Math.PI);
        if (cycle > 3.5 && cycle < 4.8) {
            return Math.sin((cycle - 3.5) * Math.PI / 1.3) * 0.8;
        }
        return 0;
    }
    
    /**
     * Initialize sampling canvas
     */
    initializeSamplingCanvas() {
        const canvas = document.getElementById('samplingCanvas');
        if (!canvas) return;
        
        this.samplingCanvas = canvas;
        this.samplingCtx = canvas.getContext('2d');
        
        this.updateSamplingDemo();
    }
    
    /**
     * Update sampling demonstration
     */
    updateSamplingDemo() {
        if (!this.samplingCtx) return;
        
        const width = this.samplingCanvas.width;
        const height = this.samplingCanvas.height;
        const samplingRate = parseInt(document.getElementById('samplingRateSlider')?.value || 360);
        
        this.samplingCtx.clearRect(0, 0, width, height);
        
        // Draw continuous signal
        this.samplingCtx.strokeStyle = '#6366f1';
        this.samplingCtx.lineWidth = 2;
        this.samplingCtx.beginPath();
        
        for (let x = 0; x < width; x++) {
            const t = (x / width) * 4 * Math.PI;
            const y = height/2 + Math.sin(t * 2) * height * 0.3;
            
            if (x === 0) {
                this.samplingCtx.moveTo(x, y);
            } else {
                this.samplingCtx.lineTo(x, y);
            }
        }
        
        this.samplingCtx.stroke();
        
        // Draw sampling points
        this.samplingCtx.fillStyle = '#ef4444';
        const sampleInterval = width / (samplingRate / 50); // Adjust for visualization
        
        for (let x = 0; x < width; x += sampleInterval) {
            const t = (x / width) * 4 * Math.PI;
            const y = height/2 + Math.sin(t * 2) * height * 0.3;
            
            this.samplingCtx.beginPath();
            this.samplingCtx.arc(x, y, 3, 0, 2 * Math.PI);
            this.samplingCtx.fill();
        }
        
        // Update sampling rate display
        const display = document.getElementById('samplingRateValue');
        if (display) {
            display.textContent = samplingRate;
        }
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // Sampling rate slider
        const samplingSlider = document.getElementById('samplingRateSlider');
        if (samplingSlider) {
            samplingSlider.addEventListener('input', () => {
                this.updateSamplingDemo();
            });
        }
        
        // Quiz options
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quiz-option')) {
                this.handleQuizAnswer(e.target);
            }
        });
        
        // Component highlighting
        document.addEventListener('click', (e) => {
            if (e.target.closest('.component-item')) {
                this.highlightECGComponent(e.target.closest('.component-item'));
            }
        });
        
        // Thumbnail navigation
        document.addEventListener('click', (e) => {
            if (e.target.closest('.thumbnail-item')) {
                const slideIndex = parseInt(e.target.closest('.thumbnail-item').dataset.slide);
                this.goToSlide(slideIndex);
            }
        });
    }
    
    /**
     * Handle slide change
     */
    onSlideChange(index) {
        this.currentSlide = index;
        this.updateProgress();
        this.updateNavigationButtons();
        this.updateThumbnails();
        
        // Trigger slide-specific animations
        this.triggerSlideAnimations(index);
    }
    
    /**
     * Handle slide transition end
     */
    onSlideTransitionEnd(index) {
        // Start any delayed animations
        this.startSlideInteractions(index);
    }
    
    /**
     * Update progress bar
     */
    updateProgress() {
        const progressFill = document.getElementById('progressFill');
        const currentSlideSpan = document.getElementById('currentSlide');
        
        if (progressFill) {
            const progress = ((this.currentSlide + 1) / this.totalSlides) * 100;
            progressFill.style.width = `${progress}%`;
        }
        
        if (currentSlideSpan) {
            currentSlideSpan.textContent = this.currentSlide + 1;
        }
    }
    
    /**
     * Update navigation buttons
     */
    updateNavigationButtons() {
        const prevBtn = document.querySelector('.prev-btn');
        const nextBtn = document.querySelector('.next-btn');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentSlide === 0;
        }
        
        if (nextBtn) {
            nextBtn.disabled = this.currentSlide === this.totalSlides - 1;
        }
    }
    
    /**
     * Update thumbnail highlights
     */
    updateThumbnails() {
        document.querySelectorAll('.thumbnail-item').forEach((item, index) => {
            if (index === this.currentSlide) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }
    
    /**
     * Start preview animation
     */
    startPreviewAnimation() {
        // This is handled by initializePreviewCanvas
    }
    
    /**
     * Generate thumbnails for all slides
     */
    generateThumbnails() {
        const thumbnailGrid = document.querySelector('.thumbnail-grid');
        if (!thumbnailGrid) return;
        
        const slides = [
            { title: 'Introduction to Biosignals', description: 'Basic concepts and types' },
            { title: 'ECG Fundamentals', description: 'Cardiac electrical activity' },
            { title: 'Signal Acquisition', description: 'From analog to digital' },
            { title: 'Noise and Artifacts', description: 'Common signal problems' },
            { title: 'Filtering Techniques', description: 'Signal preprocessing' },
            { title: 'R-Peak Detection', description: 'Pan-Tompkins algorithm' },
            { title: 'HRV Time Domain', description: 'Statistical measures' },
            { title: 'HRV Frequency Domain', description: 'Spectral analysis' },
            { title: 'Nonlinear Analysis', description: 'Complexity measures' },
            { title: 'Clinical Applications', description: 'Real-world usage' },
            { title: 'Quality Assessment', description: 'Signal validation' },
            { title: 'Summary & Practice', description: 'Key takeaways' }
        ];
        
        // Clear existing thumbnails except the first few
        const existingThumbnails = thumbnailGrid.querySelectorAll('.thumbnail-item');
        for (let i = 3; i < existingThumbnails.length; i++) {
            existingThumbnails[i].remove();
        }
        
        // Add remaining thumbnails
        for (let i = 3; i < slides.length; i++) {
            const slide = slides[i];
            const thumbnailItem = document.createElement('div');
            thumbnailItem.className = 'thumbnail-item';
            thumbnailItem.dataset.slide = i;
            
            thumbnailItem.innerHTML = `
                <div class="thumbnail-number">${String(i + 1).padStart(2, '0')}</div>
                <div class="thumbnail-content">
                    <h4>${slide.title}</h4>
                    <p>${slide.description}</p>
                </div>
            `;
            
            thumbnailGrid.appendChild(thumbnailItem);
        }
        
        this.totalSlides = slides.length;
        document.getElementById('totalSlides').textContent = this.totalSlides;
    }
    
    /**
     * Show loading overlay
     */
    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('active');
        }
    }
    
    /**
     * Hide loading overlay
     */
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }

    /**
     * Go to specific slide
     */
    goToSlide(index) {
        if (this.swiper && index >= 0 && index < this.totalSlides) {
            this.swiper.slideTo(index);
        }
    }

    /**
     * Handle quiz answer
     */
    handleQuizAnswer(button) {
        const isCorrect = button.dataset.answer === 'true';
        const allOptions = button.parentElement.querySelectorAll('.quiz-option');

        // Disable all options
        allOptions.forEach(option => {
            option.style.pointerEvents = 'none';
            if (option.dataset.answer === 'true') {
                option.classList.add('correct');
            } else if (option === button && !isCorrect) {
                option.classList.add('incorrect');
            }
        });

        // Show feedback
        setTimeout(() => {
            if (isCorrect) {
                this.showNotification('Correct! Well done!', 'success');
            } else {
                this.showNotification('Not quite right. The correct answer is highlighted.', 'info');
            }
        }, 500);
    }

    /**
     * Highlight ECG component
     */
    highlightECGComponent(componentItem) {
        const component = componentItem.dataset.component;

        // Remove previous highlights
        document.querySelectorAll('.component-item').forEach(item => {
            item.classList.remove('highlighted');
        });

        // Add highlight to selected component
        componentItem.classList.add('highlighted');

        // Show component information
        this.showComponentInfo(component);
    }

    /**
     * Show component information
     */
    showComponentInfo(component) {
        const info = {
            'p-wave': {
                title: 'P Wave',
                description: 'Represents atrial depolarization. Duration: 80-100ms. Amplitude: 0.1-0.3mV.',
                timing: 'Occurs before QRS complex'
            },
            'qrs-complex': {
                title: 'QRS Complex',
                description: 'Represents ventricular depolarization. Duration: 80-120ms. Amplitude: 0.5-2.0mV.',
                timing: 'Main component of heartbeat'
            },
            't-wave': {
                title: 'T Wave',
                description: 'Represents ventricular repolarization. Duration: 120-200ms. Amplitude: 0.1-0.5mV.',
                timing: 'Occurs after QRS complex'
            }
        };

        const componentInfo = info[component];
        if (componentInfo) {
            this.showNotification(
                `${componentInfo.title}: ${componentInfo.description} ${componentInfo.timing}`,
                'info'
            );
        }
    }

    /**
     * Trigger slide-specific animations
     */
    triggerSlideAnimations(index) {
        // Add slide-specific animation triggers here
        switch (index) {
            case 0:
                this.animateInfoCards();
                break;
            case 1:
                this.animateECGComponents();
                break;
            case 2:
                this.animateAcquisitionSteps();
                break;
        }
    }

    /**
     * Start slide interactions
     */
    startSlideInteractions(index) {
        // Add slide-specific interaction starters here
        switch (index) {
            case 1:
                this.startHeartAnimation();
                break;
            case 2:
                this.startSignalFlowAnimation();
                break;
        }
    }

    /**
     * Animate info cards
     */
    animateInfoCards() {
        const cards = document.querySelectorAll('.info-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.animation = `slideInFromLeft 0.6s ease-out forwards`;
            }, index * 200);
        });
    }

    /**
     * Animate ECG components
     */
    animateECGComponents() {
        const components = document.querySelectorAll('.component-item');
        components.forEach((component, index) => {
            setTimeout(() => {
                component.style.animation = `scaleIn 0.5s ease-out forwards`;
            }, index * 300);
        });
    }

    /**
     * Animate acquisition steps
     */
    animateAcquisitionSteps() {
        const steps = document.querySelectorAll('.step-item');
        steps.forEach((step, index) => {
            setTimeout(() => {
                step.style.animation = `slideInFromLeft 0.6s ease-out forwards`;
            }, index * 200);
        });
    }

    /**
     * Start heart animation
     */
    startHeartAnimation() {
        const pulseElements = document.querySelectorAll('.pulse-animation');
        pulseElements.forEach((element, index) => {
            setTimeout(() => {
                element.style.animation = `pulse 2s ease-in-out infinite`;
            }, index * 500);
        });
    }

    /**
     * Start signal flow animation
     */
    startSignalFlowAnimation() {
        const flowSteps = document.querySelectorAll('.flow-step');
        flowSteps.forEach((step, index) => {
            setTimeout(() => {
                step.style.animation = `scaleIn 0.5s ease-out forwards`;
            }, index * 300);
        });
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * Get notification icon
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Global functions for HTML onclick handlers
window.nextSlide = function() {
    if (window.learningModule && window.learningModule.swiper) {
        window.learningModule.swiper.slideNext();
    }
};

window.previousSlide = function() {
    if (window.learningModule && window.learningModule.swiper) {
        window.learningModule.swiper.slidePrev();
    }
};

window.toggleECGAnimation = function() {
    if (window.learningModule) {
        const module = window.learningModule;
        const playIcon = document.getElementById('ecgPlayIcon');

        if (module.isECGAnimating) {
            module.isECGAnimating = false;
            if (module.ecgAnimationId) {
                cancelAnimationFrame(module.ecgAnimationId);
            }
            if (playIcon) {
                playIcon.className = 'fas fa-play';
            }
        } else {
            module.isECGAnimating = true;
            module.drawECGWaveform();
            if (playIcon) {
                playIcon.className = 'fas fa-pause';
            }
        }
    }
};

// Add notification styles
const notificationStyles = `
.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-lg);
    max-width: 400px;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-info {
    border-left: 4px solid var(--info-color);
}

.notification-success i {
    color: var(--success-color);
}

.notification-error i {
    color: var(--error-color);
}

.notification-warning i {
    color: var(--warning-color);
}

.notification-info i {
    color: var(--info-color);
}

.component-item.highlighted {
    border-color: var(--accent-color) !important;
    background: var(--accent-color) !important;
    color: white !important;
    transform: translateX(10px) scale(1.02) !important;
}

.component-item.highlighted .component-info h4,
.component-item.highlighted .component-info p {
    color: white !important;
}
`;

// Inject notification styles
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);

// Initialize learning module when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.learningModule = new LearningModule();
});
