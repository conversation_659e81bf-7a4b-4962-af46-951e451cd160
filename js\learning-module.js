/**
 * Learning Module JavaScript - Biosignal Virtual Lab
 * Interactive slide-based learning system with animations and demos
 * 
 * Author: Dr. <PERSON>
 * Institution: SUST - BME
 * Year: 2025
 * Contact: <EMAIL>
 * 
 * Copyright © 2025 Dr. <PERSON> Esmail. All rights reserved.
 */

class LearningModule {
    constructor() {
        this.swiper = null;
        this.currentSlide = 0;
        this.totalSlides = 12;
        this.animations = {};
        this.isECGAnimating = false;
        
        this.initializeModule();
    }
    
    /**
     * Initialize the learning module
     */
    initializeModule() {
        console.log('Initializing Learning Module...');
        
        this.initializeSwiper();
        this.initializeAnimations();
        this.bindEvents();
        this.startPreviewAnimation();
        this.generateThumbnails();
        
        // Hide loading overlay
        setTimeout(() => {
            this.hideLoading();
        }, 1000);
    }
    
    /**
     * Initialize Swiper slider
     */
    initializeSwiper() {
        this.swiper = new Swiper('.learning-swiper', {
            slidesPerView: 1,
            spaceBetween: 30,
            centeredSlides: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
                dynamicBullets: true,
            },
            navigation: {
                nextEl: '.next-btn',
                prevEl: '.prev-btn',
            },
            on: {
                slideChange: (swiper) => {
                    this.onSlideChange(swiper.activeIndex);
                },
                slideChangeTransitionEnd: (swiper) => {
                    this.onSlideTransitionEnd(swiper.activeIndex);
                }
            },
            effect: 'slide',
            speed: 600,
            allowTouchMove: true,
            keyboard: {
                enabled: true,
                onlyInViewport: true,
            }
        });
    }
    
    /**
     * Initialize canvas animations
     */
    initializeAnimations() {
        this.initializePreviewCanvas();
        this.initializeBiosignalCanvas();
        this.initializeECGWaveformCanvas();
        this.initializeSamplingCanvas();
    }
    
    /**
     * Initialize preview canvas animation
     */
    initializePreviewCanvas() {
        const canvas = document.getElementById('previewCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        let time = 0;
        
        const animate = () => {
            ctx.clearRect(0, 0, width, height);
            
            // Draw ECG-like waveform
            ctx.strokeStyle = '#6366f1';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            for (let x = 0; x < width; x++) {
                const t = (x / width) * 4 * Math.PI + time;
                let y = height / 2;
                
                // Create ECG-like pattern
                const heartbeat = Math.sin(t * 1.2) * 0.3;
                const qrs = Math.sin(t * 8) * 0.7 * Math.exp(-Math.pow((t % (2 * Math.PI)) - Math.PI, 2) * 2);
                
                y += (heartbeat + qrs) * height * 0.3;
                
                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            }
            
            ctx.stroke();
            
            time += 0.05;
            requestAnimationFrame(animate);
        };
        
        animate();
    }
    
    /**
     * Initialize biosignal canvas
     */
    initializeBiosignalCanvas() {
        const canvas = document.getElementById('biosignalCanvas');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        let time = 0;
        const signals = [
            { color: '#ef4444', frequency: 1.2, amplitude: 0.3, offset: 0 },      // ECG
            { color: '#10b981', frequency: 8, amplitude: 0.2, offset: height/3 }, // EEG
            { color: '#f59e0b', frequency: 4, amplitude: 0.25, offset: 2*height/3 } // EMG
        ];
        
        const animate = () => {
            ctx.clearRect(0, 0, width, height);
            
            signals.forEach((signal, index) => {
                ctx.strokeStyle = signal.color;
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                for (let x = 0; x < width; x++) {
                    const t = (x / width) * 4 * Math.PI + time;
                    let y = signal.offset + height/6;
                    
                    if (index === 0) {
                        // ECG pattern
                        const heartbeat = Math.sin(t * signal.frequency) * signal.amplitude;
                        const qrs = Math.sin(t * 6) * 0.5 * Math.exp(-Math.pow((t % (2 * Math.PI)) - Math.PI, 2) * 3);
                        y += (heartbeat + qrs) * height * 0.4;
                    } else {
                        // Other signals
                        y += Math.sin(t * signal.frequency) * signal.amplitude * height * 0.4;
                        y += Math.sin(t * signal.frequency * 2.3) * signal.amplitude * height * 0.2;
                    }
                    
                    if (x === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                
                ctx.stroke();
            });
            
            time += 0.03;
            requestAnimationFrame(animate);
        };
        
        animate();
    }
    
    /**
     * Initialize ECG waveform canvas
     */
    initializeECGWaveformCanvas() {
        const canvas = document.getElementById('ecgWaveformCanvas');
        if (!canvas) return;
        
        this.ecgCanvas = canvas;
        this.ecgCtx = canvas.getContext('2d');
        this.ecgTime = 0;
        this.ecgAnimationId = null;
        
        this.drawECGWaveform();
    }
    
    /**
     * Draw ECG waveform
     */
    drawECGWaveform() {
        if (!this.ecgCtx) return;
        
        const width = this.ecgCanvas.width;
        const height = this.ecgCanvas.height;
        
        this.ecgCtx.clearRect(0, 0, width, height);
        
        // Draw grid
        this.drawECGGrid();
        
        // Draw ECG waveform
        this.ecgCtx.strokeStyle = '#ef4444';
        this.ecgCtx.lineWidth = 2;
        this.ecgCtx.beginPath();
        
        for (let x = 0; x < width; x++) {
            const t = (x / width) * 6 * Math.PI + this.ecgTime;
            let y = height / 2;
            
            // P wave
            const pWave = this.generatePWave(t) * height * 0.1;
            // QRS complex
            const qrsComplex = this.generateQRSComplex(t) * height * 0.3;
            // T wave
            const tWave = this.generateTWave(t) * height * 0.15;
            
            y += pWave + qrsComplex + tWave;
            
            if (x === 0) {
                this.ecgCtx.moveTo(x, y);
            } else {
                this.ecgCtx.lineTo(x, y);
            }
        }
        
        this.ecgCtx.stroke();
        
        if (this.isECGAnimating) {
            this.ecgTime += 0.02;
            this.ecgAnimationId = requestAnimationFrame(() => this.drawECGWaveform());
        }
    }
    
    /**
     * Draw ECG grid
     */
    drawECGGrid() {
        const width = this.ecgCanvas.width;
        const height = this.ecgCanvas.height;
        
        this.ecgCtx.strokeStyle = 'rgba(200, 200, 200, 0.3)';
        this.ecgCtx.lineWidth = 1;
        
        // Vertical lines
        for (let x = 0; x < width; x += 20) {
            this.ecgCtx.beginPath();
            this.ecgCtx.moveTo(x, 0);
            this.ecgCtx.lineTo(x, height);
            this.ecgCtx.stroke();
        }
        
        // Horizontal lines
        for (let y = 0; y < height; y += 20) {
            this.ecgCtx.beginPath();
            this.ecgCtx.moveTo(0, y);
            this.ecgCtx.lineTo(width, y);
            this.ecgCtx.stroke();
        }
    }
    
    /**
     * Generate P wave
     */
    generatePWave(t) {
        const cycle = t % (2 * Math.PI);
        if (cycle > 0.5 && cycle < 1.2) {
            return Math.sin((cycle - 0.5) * Math.PI / 0.7) * 0.5;
        }
        return 0;
    }
    
    /**
     * Generate QRS complex
     */
    generateQRSComplex(t) {
        const cycle = t % (2 * Math.PI);
        if (cycle > 1.8 && cycle < 2.3) {
            const phase = (cycle - 1.8) / 0.5;
            if (phase < 0.2) {
                return -Math.sin(phase * Math.PI / 0.2) * 0.3; // Q wave
            } else if (phase < 0.6) {
                return Math.sin((phase - 0.2) * Math.PI / 0.4) * 2; // R wave
            } else {
                return -Math.sin((phase - 0.6) * Math.PI / 0.4) * 0.5; // S wave
            }
        }
        return 0;
    }
    
    /**
     * Generate T wave
     */
    generateTWave(t) {
        const cycle = t % (2 * Math.PI);
        if (cycle > 3.5 && cycle < 4.8) {
            return Math.sin((cycle - 3.5) * Math.PI / 1.3) * 0.8;
        }
        return 0;
    }
    
    /**
     * Initialize sampling canvas
     */
    initializeSamplingCanvas() {
        const canvas = document.getElementById('samplingCanvas');
        if (!canvas) return;
        
        this.samplingCanvas = canvas;
        this.samplingCtx = canvas.getContext('2d');
        
        this.updateSamplingDemo();
    }
    
    /**
     * Update sampling demonstration
     */
    updateSamplingDemo() {
        if (!this.samplingCtx) return;
        
        const width = this.samplingCanvas.width;
        const height = this.samplingCanvas.height;
        const samplingRate = parseInt(document.getElementById('samplingRateSlider')?.value || 360);
        
        this.samplingCtx.clearRect(0, 0, width, height);
        
        // Draw continuous signal
        this.samplingCtx.strokeStyle = '#6366f1';
        this.samplingCtx.lineWidth = 2;
        this.samplingCtx.beginPath();
        
        for (let x = 0; x < width; x++) {
            const t = (x / width) * 4 * Math.PI;
            const y = height/2 + Math.sin(t * 2) * height * 0.3;
            
            if (x === 0) {
                this.samplingCtx.moveTo(x, y);
            } else {
                this.samplingCtx.lineTo(x, y);
            }
        }
        
        this.samplingCtx.stroke();
        
        // Draw sampling points
        this.samplingCtx.fillStyle = '#ef4444';
        const sampleInterval = width / (samplingRate / 50); // Adjust for visualization
        
        for (let x = 0; x < width; x += sampleInterval) {
            const t = (x / width) * 4 * Math.PI;
            const y = height/2 + Math.sin(t * 2) * height * 0.3;
            
            this.samplingCtx.beginPath();
            this.samplingCtx.arc(x, y, 3, 0, 2 * Math.PI);
            this.samplingCtx.fill();
        }
        
        // Update sampling rate display
        const display = document.getElementById('samplingRateValue');
        if (display) {
            display.textContent = samplingRate;
        }
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // Sampling rate slider
        const samplingSlider = document.getElementById('samplingRateSlider');
        if (samplingSlider) {
            samplingSlider.addEventListener('input', () => {
                this.updateSamplingDemo();
            });
        }
        
        // Quiz options
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('quiz-option')) {
                this.handleQuizAnswer(e.target);
            }
        });
        
        // Component highlighting
        document.addEventListener('click', (e) => {
            if (e.target.closest('.component-item')) {
                this.highlightECGComponent(e.target.closest('.component-item'));
            }
        });
        
        // Thumbnail navigation
        document.addEventListener('click', (e) => {
            if (e.target.closest('.thumbnail-item')) {
                const slideIndex = parseInt(e.target.closest('.thumbnail-item').dataset.slide);
                this.goToSlide(slideIndex);
            }
        });
    }
    
    /**
     * Handle slide change
     */
    onSlideChange(index) {
        this.currentSlide = index;
        this.updateProgress();
        this.updateNavigationButtons();
        this.updateThumbnails();
        
        // Trigger slide-specific animations
        this.triggerSlideAnimations(index);
    }
    
    /**
     * Handle slide transition end
     */
    onSlideTransitionEnd(index) {
        // Start any delayed animations
        this.startSlideInteractions(index);
    }
    
    /**
     * Update progress bar
     */
    updateProgress() {
        const progressFill = document.getElementById('progressFill');
        const currentSlideSpan = document.getElementById('currentSlide');
        
        if (progressFill) {
            const progress = ((this.currentSlide + 1) / this.totalSlides) * 100;
            progressFill.style.width = `${progress}%`;
        }
        
        if (currentSlideSpan) {
            currentSlideSpan.textContent = this.currentSlide + 1;
        }
    }
    
    /**
     * Update navigation buttons
     */
    updateNavigationButtons() {
        const prevBtn = document.querySelector('.prev-btn');
        const nextBtn = document.querySelector('.next-btn');
        
        if (prevBtn) {
            prevBtn.disabled = this.currentSlide === 0;
        }
        
        if (nextBtn) {
            nextBtn.disabled = this.currentSlide === this.totalSlides - 1;
        }
    }
    
    /**
     * Update thumbnail highlights
     */
    updateThumbnails() {
        document.querySelectorAll('.thumbnail-item').forEach((item, index) => {
            if (index === this.currentSlide) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }
    
    /**
     * Start preview animation
     */
    startPreviewAnimation() {
        // This is handled by initializePreviewCanvas
    }
    
    /**
     * Generate thumbnails for all slides
     */
    generateThumbnails() {
        const thumbnailGrid = document.querySelector('.thumbnail-grid');
        if (!thumbnailGrid) return;
        
        const slides = [
            { title: 'Introduction to Biosignals', description: 'Basic concepts and types' },
            { title: 'ECG Fundamentals', description: 'Cardiac electrical activity' },
            { title: 'Signal Acquisition', description: 'From analog to digital' },
            { title: 'Noise and Artifacts', description: 'Common signal problems' },
            { title: 'Filtering Techniques', description: 'Signal preprocessing' },
            { title: 'R-Peak Detection', description: 'Pan-Tompkins algorithm' },
            { title: 'HRV Time Domain', description: 'Statistical measures' },
            { title: 'HRV Frequency Domain', description: 'Spectral analysis' },
            { title: 'Nonlinear Analysis', description: 'Complexity measures' },
            { title: 'Clinical Applications', description: 'Real-world usage' },
            { title: 'Quality Assessment', description: 'Signal validation' },
            { title: 'Summary & Practice', description: 'Key takeaways' }
        ];
        
        // Clear existing thumbnails except the first few
        const existingThumbnails = thumbnailGrid.querySelectorAll('.thumbnail-item');
        for (let i = 3; i < existingThumbnails.length; i++) {
            existingThumbnails[i].remove();
        }
        
        // Add remaining thumbnails
        for (let i = 3; i < slides.length; i++) {
            const slide = slides[i];
            const thumbnailItem = document.createElement('div');
            thumbnailItem.className = 'thumbnail-item';
            thumbnailItem.dataset.slide = i;
            
            thumbnailItem.innerHTML = `
                <div class="thumbnail-number">${String(i + 1).padStart(2, '0')}</div>
                <div class="thumbnail-content">
                    <h4>${slide.title}</h4>
                    <p>${slide.description}</p>
                </div>
            `;
            
            thumbnailGrid.appendChild(thumbnailItem);
        }
        
        this.totalSlides = slides.length;
        document.getElementById('totalSlides').textContent = this.totalSlides;
    }
    
    /**
     * Show loading overlay
     */
    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('active');
        }
    }
    
    /**
     * Hide loading overlay
     */
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }

    /**
     * Go to specific slide
     */
    goToSlide(index) {
        if (this.swiper && index >= 0 && index < this.totalSlides) {
            this.swiper.slideTo(index);
        }
    }

    /**
     * Handle quiz answer
     */
    handleQuizAnswer(button) {
        const isCorrect = button.dataset.answer === 'true';
        const allOptions = button.parentElement.querySelectorAll('.quiz-option');

        // Disable all options
        allOptions.forEach(option => {
            option.style.pointerEvents = 'none';
            if (option.dataset.answer === 'true') {
                option.classList.add('correct');
            } else if (option === button && !isCorrect) {
                option.classList.add('incorrect');
            }
        });

        // Show feedback
        setTimeout(() => {
            if (isCorrect) {
                this.showNotification('Correct! Well done!', 'success');
            } else {
                this.showNotification('Not quite right. The correct answer is highlighted.', 'info');
            }
        }, 500);
    }

    /**
     * Highlight ECG component
     */
    highlightECGComponent(componentItem) {
        const component = componentItem.dataset.component;

        // Remove previous highlights
        document.querySelectorAll('.component-item').forEach(item => {
            item.classList.remove('highlighted');
        });

        // Add highlight to selected component
        componentItem.classList.add('highlighted');

        // Show component information
        this.showComponentInfo(component);
    }

    /**
     * Show component information
     */
    showComponentInfo(component) {
        const info = {
            'p-wave': {
                title: 'P Wave',
                description: 'Represents atrial depolarization. Duration: 80-100ms. Amplitude: 0.1-0.3mV.',
                timing: 'Occurs before QRS complex'
            },
            'qrs-complex': {
                title: 'QRS Complex',
                description: 'Represents ventricular depolarization. Duration: 80-120ms. Amplitude: 0.5-2.0mV.',
                timing: 'Main component of heartbeat'
            },
            't-wave': {
                title: 'T Wave',
                description: 'Represents ventricular repolarization. Duration: 120-200ms. Amplitude: 0.1-0.5mV.',
                timing: 'Occurs after QRS complex'
            }
        };

        const componentInfo = info[component];
        if (componentInfo) {
            this.showNotification(
                `${componentInfo.title}: ${componentInfo.description} ${componentInfo.timing}`,
                'info'
            );
        }
    }

    /**
     * Trigger slide-specific animations
     */
    triggerSlideAnimations(index) {
        // Add slide-specific animation triggers here
        switch (index) {
            case 0:
                this.animateInfoCards();
                break;
            case 1:
                this.animateECGComponents();
                break;
            case 2:
                this.animateAcquisitionSteps();
                break;
        }
    }

    /**
     * Start slide interactions
     */
    startSlideInteractions(index) {
        // Add slide-specific interaction starters here
        switch (index) {
            case 1:
                this.startHeartAnimation();
                break;
            case 2:
                this.startSignalFlowAnimation();
                break;
        }
    }

    /**
     * Animate info cards
     */
    animateInfoCards() {
        const cards = document.querySelectorAll('.info-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.animation = `slideInFromLeft 0.6s ease-out forwards`;
            }, index * 200);
        });
    }

    /**
     * Animate ECG components
     */
    animateECGComponents() {
        const components = document.querySelectorAll('.component-item');
        components.forEach((component, index) => {
            setTimeout(() => {
                component.style.animation = `scaleIn 0.5s ease-out forwards`;
            }, index * 300);
        });
    }

    /**
     * Animate acquisition steps
     */
    animateAcquisitionSteps() {
        const steps = document.querySelectorAll('.step-item');
        steps.forEach((step, index) => {
            setTimeout(() => {
                step.style.animation = `slideInFromLeft 0.6s ease-out forwards`;
            }, index * 200);
        });
    }

    /**
     * Start heart animation
     */
    startHeartAnimation() {
        const pulseElements = document.querySelectorAll('.pulse-animation');
        pulseElements.forEach((element, index) => {
            setTimeout(() => {
                element.style.animation = `pulse 2s ease-in-out infinite`;
            }, index * 500);
        });
    }

    /**
     * Start signal flow animation
     */
    startSignalFlowAnimation() {
        const flowSteps = document.querySelectorAll('.flow-step');
        flowSteps.forEach((step, index) => {
            setTimeout(() => {
                step.style.animation = `scaleIn 0.5s ease-out forwards`;
            }, index * 300);
        });
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Remove after delay
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * Get notification icon
     */
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    /**
     * Start R-Peak detection demo
     */
    startRPeakDetectionDemo() {
        const canvas = document.getElementById('rPeakCanvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        let time = 0;
        let rPeakCount = 0;
        let lastRPeak = 0;
        const rPeaks = [];

        const animate = () => {
            ctx.clearRect(0, 0, width, height);

            // Draw ECG signal
            ctx.strokeStyle = '#ef4444';
            ctx.lineWidth = 2;
            ctx.beginPath();

            for (let x = 0; x < width; x++) {
                const t = (x / width) * 6 * Math.PI + time;
                let y = height / 2;

                // Generate ECG with R-peaks
                const heartbeat = this.generateCompleteECG(t);
                y += heartbeat * height * 0.3;

                if (x === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }

                // Detect R-peaks
                if (this.isRPeak(t) && (time - lastRPeak) > 0.5) {
                    rPeaks.push({ x, y, time: t });
                    rPeakCount++;
                    lastRPeak = time;
                }
            }

            ctx.stroke();

            // Draw R-peak markers
            ctx.fillStyle = '#10b981';
            rPeaks.forEach(peak => {
                ctx.beginPath();
                ctx.arc(peak.x, peak.y, 5, 0, 2 * Math.PI);
                ctx.fill();

                // Draw vertical line
                ctx.strokeStyle = '#10b981';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(peak.x, 0);
                ctx.lineTo(peak.x, height);
                ctx.stroke();
            });

            // Update statistics
            const hrElement = document.getElementById('detectedHR');
            const countElement = document.getElementById('rPeakCount');

            if (hrElement && rPeaks.length > 1) {
                const avgRR = (rPeaks[rPeaks.length - 1].time - rPeaks[0].time) / (rPeaks.length - 1);
                const hr = Math.round(60 / (avgRR / (2 * Math.PI)) * 1.2); // Approximate conversion
                hrElement.textContent = hr;
            }

            if (countElement) {
                countElement.textContent = rPeakCount;
            }

            time += 0.02;

            // Clear old peaks
            if (rPeaks.length > 20) {
                rPeaks.shift();
            }

            requestAnimationFrame(animate);
        };

        animate();
        this.showNotification('R-Peak detection demo started!', 'success');
    }

    /**
     * Generate complete ECG waveform
     */
    generateCompleteECG(t) {
        const cycle = t % (2 * Math.PI);
        let signal = 0;

        // P wave
        if (cycle > 0.5 && cycle < 1.2) {
            signal += Math.sin((cycle - 0.5) * Math.PI / 0.7) * 0.1;
        }

        // QRS complex
        if (cycle > 1.8 && cycle < 2.3) {
            const phase = (cycle - 1.8) / 0.5;
            if (phase < 0.2) {
                signal -= Math.sin(phase * Math.PI / 0.2) * 0.15; // Q wave
            } else if (phase < 0.6) {
                signal += Math.sin((phase - 0.2) * Math.PI / 0.4) * 1.0; // R wave
            } else {
                signal -= Math.sin((phase - 0.6) * Math.PI / 0.4) * 0.25; // S wave
            }
        }

        // T wave
        if (cycle > 3.5 && cycle < 4.8) {
            signal += Math.sin((cycle - 3.5) * Math.PI / 1.3) * 0.2;
        }

        return signal;
    }

    /**
     * Check if current point is an R-peak
     */
    isRPeak(t) {
        const cycle = t % (2 * Math.PI);
        return cycle > 2.0 && cycle < 2.1; // R wave peak
    }

    /**
     * Calculate HRV parameters
     */
    calculateHRVParameters() {
        // Generate sample RR intervals
        const rrIntervals = this.generateSampleRRIntervals();

        // Calculate time domain parameters
        const sdnn = this.calculateSDNN(rrIntervals);
        const rmssd = this.calculateRMSSD(rrIntervals);
        const pnn50 = this.calculatePNN50(rrIntervals);
        const meanHR = this.calculateMeanHR(rrIntervals);

        // Update display
        document.getElementById('sdnnValue').textContent = sdnn.toFixed(1);
        document.getElementById('rmssdValue').textContent = rmssd.toFixed(1);
        document.getElementById('pnn50Value').textContent = pnn50.toFixed(1);
        document.getElementById('meanHRValue').textContent = meanHR.toFixed(0);

        // Draw RR interval plot
        this.drawRRIntervalPlot(rrIntervals);

        this.showNotification('HRV parameters calculated successfully!', 'success');
    }

    /**
     * Generate sample RR intervals
     */
    generateSampleRRIntervals() {
        const intervals = [];
        const baseRR = 800; // Base RR interval in ms

        for (let i = 0; i < 100; i++) {
            // Add some variability
            const variation = (Math.random() - 0.5) * 100;
            const respiratory = Math.sin(i * 0.1) * 30;
            const rr = baseRR + variation + respiratory;
            intervals.push(Math.max(600, Math.min(1200, rr)));
        }

        return intervals;
    }

    /**
     * Calculate SDNN
     */
    calculateSDNN(intervals) {
        const mean = intervals.reduce((sum, val) => sum + val, 0) / intervals.length;
        const variance = intervals.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / intervals.length;
        return Math.sqrt(variance);
    }

    /**
     * Calculate RMSSD
     */
    calculateRMSSD(intervals) {
        const differences = [];
        for (let i = 1; i < intervals.length; i++) {
            differences.push(Math.pow(intervals[i] - intervals[i-1], 2));
        }
        const meanSquaredDiff = differences.reduce((sum, val) => sum + val, 0) / differences.length;
        return Math.sqrt(meanSquaredDiff);
    }

    /**
     * Calculate pNN50
     */
    calculatePNN50(intervals) {
        let count = 0;
        for (let i = 1; i < intervals.length; i++) {
            if (Math.abs(intervals[i] - intervals[i-1]) > 50) {
                count++;
            }
        }
        return (count / (intervals.length - 1)) * 100;
    }

    /**
     * Calculate mean heart rate
     */
    calculateMeanHR(intervals) {
        const meanRR = intervals.reduce((sum, val) => sum + val, 0) / intervals.length;
        return 60000 / meanRR; // Convert to BPM
    }

    /**
     * Draw RR interval plot
     */
    drawRRIntervalPlot(intervals) {
        const canvas = document.getElementById('rrIntervalCanvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        ctx.clearRect(0, 0, width, height);

        // Draw grid
        ctx.strokeStyle = 'rgba(200, 200, 200, 0.3)';
        ctx.lineWidth = 1;

        for (let x = 0; x < width; x += 40) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }

        for (let y = 0; y < height; y += 30) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }

        // Draw RR intervals
        ctx.strokeStyle = '#6366f1';
        ctx.lineWidth = 2;
        ctx.beginPath();

        const minRR = Math.min(...intervals);
        const maxRR = Math.max(...intervals);
        const range = maxRR - minRR;

        for (let i = 0; i < intervals.length && i < width; i++) {
            const x = (i / intervals.length) * width;
            const y = height - ((intervals[i] - minRR) / range) * height;

            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }

        ctx.stroke();
    }

    /**
     * Generate power spectrum
     */
    generatePowerSpectrum() {
        const canvas = document.getElementById('spectrumCanvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        ctx.clearRect(0, 0, width, height);

        // Generate sample spectrum data
        const frequencies = [];
        const powers = [];

        for (let f = 0; f <= 0.5; f += 0.001) {
            frequencies.push(f);

            // Simulate HRV spectrum with VLF, LF, and HF components
            let power = 0;

            // VLF component (0.003-0.04 Hz)
            if (f >= 0.003 && f <= 0.04) {
                power += 1000 * Math.exp(-Math.pow((f - 0.02) / 0.01, 2));
            }

            // LF component (0.04-0.15 Hz)
            if (f >= 0.04 && f <= 0.15) {
                power += 800 * Math.exp(-Math.pow((f - 0.1) / 0.03, 2));
            }

            // HF component (0.15-0.4 Hz)
            if (f >= 0.15 && f <= 0.4) {
                power += 600 * Math.exp(-Math.pow((f - 0.25) / 0.05, 2));
            }

            // Add some noise
            power += Math.random() * 50;

            powers.push(power);
        }

        // Draw spectrum
        ctx.strokeStyle = '#6366f1';
        ctx.lineWidth = 2;
        ctx.beginPath();

        const maxPower = Math.max(...powers);

        for (let i = 0; i < frequencies.length; i++) {
            const x = (frequencies[i] / 0.5) * width;
            const y = height - (powers[i] / maxPower) * height;

            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }

        ctx.stroke();

        // Calculate band powers
        const vlfPower = this.calculateBandPower(frequencies, powers, 0.003, 0.04);
        const lfPower = this.calculateBandPower(frequencies, powers, 0.04, 0.15);
        const hfPower = this.calculateBandPower(frequencies, powers, 0.15, 0.4);
        const lfhfRatio = lfPower / hfPower;

        // Update display
        document.getElementById('vlfPower').textContent = vlfPower.toFixed(0);
        document.getElementById('lfPower').textContent = lfPower.toFixed(0);
        document.getElementById('hfPower').textContent = hfPower.toFixed(0);
        document.getElementById('lfhfRatio').textContent = lfhfRatio.toFixed(2);

        this.showNotification('Power spectrum generated successfully!', 'success');
    }

    /**
     * Calculate band power
     */
    calculateBandPower(frequencies, powers, minFreq, maxFreq) {
        let bandPower = 0;
        let count = 0;

        for (let i = 0; i < frequencies.length; i++) {
            if (frequencies[i] >= minFreq && frequencies[i] <= maxFreq) {
                bandPower += powers[i];
                count++;
            }
        }

        return count > 0 ? bandPower / count : 0;
    }

    /**
     * Generate Poincaré data
     */
    generatePoincareData() {
        const canvas = document.getElementById('poincareCanvas');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        ctx.clearRect(0, 0, width, height);

        // Generate RR intervals
        const rrIntervals = this.generateSampleRRIntervals();

        // Create Poincaré plot data
        const plotData = [];
        for (let i = 1; i < rrIntervals.length; i++) {
            plotData.push({
                x: rrIntervals[i-1],
                y: rrIntervals[i]
            });
        }

        // Find min/max for scaling
        const minRR = Math.min(...rrIntervals);
        const maxRR = Math.max(...rrIntervals);
        const range = maxRR - minRR;
        const margin = 20;

        // Draw grid
        ctx.strokeStyle = 'rgba(200, 200, 200, 0.3)';
        ctx.lineWidth = 1;

        for (let i = 0; i <= 10; i++) {
            const x = margin + (i / 10) * (width - 2 * margin);
            const y = margin + (i / 10) * (height - 2 * margin);

            ctx.beginPath();
            ctx.moveTo(x, margin);
            ctx.lineTo(x, height - margin);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(margin, y);
            ctx.lineTo(width - margin, y);
            ctx.stroke();
        }

        // Draw identity line
        ctx.strokeStyle = 'rgba(100, 100, 100, 0.5)';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(margin, height - margin);
        ctx.lineTo(width - margin, margin);
        ctx.stroke();

        // Draw data points
        ctx.fillStyle = '#6366f1';
        plotData.forEach(point => {
            const x = margin + ((point.x - minRR) / range) * (width - 2 * margin);
            const y = height - margin - ((point.y - minRR) / range) * (height - 2 * margin);

            ctx.beginPath();
            ctx.arc(x, y, 2, 0, 2 * Math.PI);
            ctx.fill();
        });

        // Calculate SD1 and SD2
        const sd1 = this.calculateSD1(plotData);
        const sd2 = this.calculateSD2(plotData);
        const sd1sd2Ratio = sd1 / sd2;
        const ellipseArea = Math.PI * sd1 * sd2;

        // Update display
        document.getElementById('sd1Value').textContent = sd1.toFixed(1);
        document.getElementById('sd2Value').textContent = sd2.toFixed(1);
        document.getElementById('sd1sd2Ratio').textContent = sd1sd2Ratio.toFixed(3);
        document.getElementById('ellipseArea').textContent = ellipseArea.toFixed(0);

        this.showNotification('Poincaré plot generated successfully!', 'success');
    }

    /**
     * Calculate SD1 (short-term variability)
     */
    calculateSD1(plotData) {
        const differences = plotData.map(point => (point.y - point.x) / Math.sqrt(2));
        const mean = differences.reduce((sum, val) => sum + val, 0) / differences.length;
        const variance = differences.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / differences.length;
        return Math.sqrt(variance);
    }

    /**
     * Calculate SD2 (long-term variability)
     */
    calculateSD2(plotData) {
        const sums = plotData.map(point => (point.y + point.x) / Math.sqrt(2));
        const mean = sums.reduce((sum, val) => sum + val, 0) / sums.length;
        const variance = sums.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / sums.length;
        return Math.sqrt(variance);
    }

    /**
     * Analyze signal quality
     */
    analyzeSignalQuality() {
        // Simulate quality analysis
        const snr = 15 + Math.random() * 10; // 15-25 dB
        const artifacts = Math.random() * 8; // 0-8%
        const accuracy = 95 + Math.random() * 4; // 95-99%

        // Update progress bars
        document.getElementById('snrBar').style.width = `${(snr / 30) * 100}%`;
        document.getElementById('artifactBar').style.width = `${(artifacts / 20) * 100}%`;
        document.getElementById('accuracyBar').style.width = `${accuracy}%`;

        // Update values
        document.getElementById('snrValue').textContent = `${snr.toFixed(1)} dB`;
        document.getElementById('artifactValue').textContent = `${artifacts.toFixed(1)}%`;
        document.getElementById('accuracyValue').textContent = `${accuracy.toFixed(1)}%`;

        // Determine overall quality
        let quality = 'GOOD';
        let qualityClass = 'good';

        if (snr < 15 || artifacts > 10 || accuracy < 95) {
            quality = 'POOR';
            qualityClass = 'poor';
        } else if (snr < 20 || artifacts > 5 || accuracy < 97) {
            quality = 'FAIR';
            qualityClass = 'fair';
        }

        const qualityStatus = document.getElementById('qualityStatus');
        if (qualityStatus) {
            qualityStatus.textContent = quality;
            qualityStatus.className = `quality-status ${qualityClass}`;
        }

        this.showNotification(`Signal quality analysis complete: ${quality}`,
                            quality === 'GOOD' ? 'success' : quality === 'FAIR' ? 'warning' : 'error');
    }

    /**
     * Download certificate
     */
    downloadCertificate() {
        this.showNotification('Certificate download feature coming soon!', 'info');
    }

    /**
     * Restart course
     */
    restartCourse() {
        if (confirm('Are you sure you want to restart the course? This will reset your progress.')) {
            this.goToSlide(0);
            this.showNotification('Course restarted successfully!', 'success');
        }
    }

    /**
     * Provide feedback
     */
    provideFeedback() {
        const feedback = prompt('Please provide your feedback about this learning module:');
        if (feedback) {
            this.showNotification('Thank you for your feedback!', 'success');
            console.log('User feedback:', feedback);
        }
    }

    /**
     * Share module
     */
    shareModule() {
        if (navigator.share) {
            navigator.share({
                title: 'Biosignal Processing Learning Module',
                text: 'Check out this interactive learning module for biosignal processing and HRV analysis!',
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                this.showNotification('Link copied to clipboard!', 'success');
            });
        }
    }
}

// Global functions for HTML onclick handlers
window.nextSlide = function() {
    if (window.learningModule && window.learningModule.swiper) {
        window.learningModule.swiper.slideNext();
    }
};

window.previousSlide = function() {
    if (window.learningModule && window.learningModule.swiper) {
        window.learningModule.swiper.slidePrev();
    }
};

window.toggleECGAnimation = function() {
    if (window.learningModule) {
        const module = window.learningModule;
        const playIcon = document.getElementById('ecgPlayIcon');

        if (module.isECGAnimating) {
            module.isECGAnimating = false;
            if (module.ecgAnimationId) {
                cancelAnimationFrame(module.ecgAnimationId);
            }
            if (playIcon) {
                playIcon.className = 'fas fa-play';
            }
        } else {
            module.isECGAnimating = true;
            module.drawECGWaveform();
            if (playIcon) {
                playIcon.className = 'fas fa-pause';
            }
        }
    }
};

// Add notification styles
const notificationStyles = `
.notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-lg);
    max-width: 400px;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification-warning {
    border-left: 4px solid var(--warning-color);
}

.notification-info {
    border-left: 4px solid var(--info-color);
}

.notification-success i {
    color: var(--success-color);
}

.notification-error i {
    color: var(--error-color);
}

.notification-warning i {
    color: var(--warning-color);
}

.notification-info i {
    color: var(--info-color);
}

.component-item.highlighted {
    border-color: var(--accent-color) !important;
    background: var(--accent-color) !important;
    color: white !important;
    transform: translateX(10px) scale(1.02) !important;
}

.component-item.highlighted .component-info h4,
.component-item.highlighted .component-info p {
    color: white !important;
}
`;

// Inject notification styles
const styleSheet = document.createElement('style');
styleSheet.textContent = notificationStyles;
document.head.appendChild(styleSheet);

// Additional global functions for new slides
window.startRPeakDemo = function() {
    if (window.learningModule) {
        window.learningModule.startRPeakDetectionDemo();
    }
};

window.calculateHRV = function() {
    if (window.learningModule) {
        window.learningModule.calculateHRVParameters();
    }
};

window.generateSpectrum = function() {
    if (window.learningModule) {
        window.learningModule.generatePowerSpectrum();
    }
};

window.generatePoincareData = function() {
    if (window.learningModule) {
        window.learningModule.generatePoincareData();
    }
};

window.analyzeSignalQuality = function() {
    if (window.learningModule) {
        window.learningModule.analyzeSignalQuality();
    }
};

window.downloadCertificate = function() {
    if (window.learningModule) {
        window.learningModule.downloadCertificate();
    }
};

window.restartCourse = function() {
    if (window.learningModule) {
        window.learningModule.restartCourse();
    }
};

window.provideFeedback = function() {
    if (window.learningModule) {
        window.learningModule.provideFeedback();
    }
};

window.shareModule = function() {
    if (window.learningModule) {
        window.learningModule.shareModule();
    }
};

// Initialize learning module when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.learningModule = new LearningModule();
});
