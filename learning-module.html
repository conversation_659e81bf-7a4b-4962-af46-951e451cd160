<!DOCTYPE html>
<!--
    Biosignal Virtual Lab - Interactive Learning Module
    
    Author: Dr. <PERSON> Esmail
    Institution: SUST - BME (Sudan University of Science and Technology - Biomedical Engineering)
    Year: 2025
    
    Contact Information:
    Email: <EMAIL>
    Phone: +249912867327, +966538076790
    
    Copyright © 2025 Dr. <PERSON>il. All rights reserved.
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Learning Module - Biosignal Virtual Lab</title>
    <meta name="author" content="Dr. <PERSON>smail">
    <meta name="copyright" content="© 2025 Dr. <PERSON>il">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/learning-module.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-heartbeat"></i>
                <span>Biosignal Virtual Lab</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="learning-module.html" class="nav-link active">Learning Module</a>
                <a href="signal-processing.html" class="nav-link">Signal Processing</a>
                <a href="r-peak-detection.html" class="nav-link">R-Peak Detection</a>
                <a href="hrv-analysis.html" class="nav-link">HRV Analysis</a>
                <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
                <div class="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Learning Module Header -->
    <div class="learning-header">
        <div class="container">
            <div class="header-content">
                <div class="header-text">
                    <h1 class="learning-title">
                        <i class="fas fa-graduation-cap"></i>
                        Interactive Biosignal Processing Learning Module
                    </h1>
                    <p class="learning-subtitle">
                        Master ECG signal processing, R-peak detection, and HRV analysis through interactive slides, 
                        real-world examples, and hands-on demonstrations.
                    </p>
                    <div class="progress-overview">
                        <div class="progress-item">
                            <span class="progress-number">12</span>
                            <span class="progress-label">Interactive Slides</span>
                        </div>
                        <div class="progress-item">
                            <span class="progress-number">8</span>
                            <span class="progress-label">Practical Demos</span>
                        </div>
                        <div class="progress-item">
                            <span class="progress-number">5</span>
                            <span class="progress-label">Interactive Tools</span>
                        </div>
                    </div>
                </div>
                <div class="header-visual">
                    <div class="learning-preview">
                        <div class="preview-screen">
                            <div class="slide-preview">
                                <div class="preview-content">
                                    <div class="preview-icon">
                                        <i class="fas fa-heartbeat"></i>
                                    </div>
                                    <h3>ECG Signal Processing</h3>
                                    <div class="preview-waveform">
                                        <canvas id="previewCanvas" width="300" height="100"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide Navigation -->
    <div class="slide-navigation">
        <div class="container">
            <div class="nav-controls">
                <button type="button" class="nav-btn prev-btn" onclick="previousSlide()" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                <div class="slide-counter">
                    <span id="currentSlide">1</span> / <span id="totalSlides">12</span>
                </div>
                <button type="button" class="nav-btn next-btn" onclick="nextSlide()">
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
    </div>

    <!-- Main Slide Container -->
    <div class="slide-container">
        <div class="swiper learning-swiper">
            <div class="swiper-wrapper">
                
                <!-- Slide 1: Introduction to Biosignals -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">01</div>
                            <h2 class="slide-title">Introduction to Biosignals</h2>
                            <p class="slide-subtitle">Understanding the electrical activity of the human body</p>
                        </div>
                        
                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>What are Biosignals?</h3>
                                    <p>Biosignals are electrical, chemical, or mechanical signals that can be measured from biological systems. They provide valuable information about physiological processes and health status.</p>
                                    
                                    <div class="info-cards">
                                        <div class="info-card animate-slide-in-left">
                                            <div class="card-icon">
                                                <i class="fas fa-heartbeat"></i>
                                            </div>
                                            <h4>ECG</h4>
                                            <p>Electrocardiogram - Heart electrical activity</p>
                                        </div>
                                        <div class="info-card animate-slide-in-left" style="animation-delay: 0.2s;">
                                            <div class="card-icon">
                                                <i class="fas fa-brain"></i>
                                            </div>
                                            <h4>EEG</h4>
                                            <p>Electroencephalogram - Brain electrical activity</p>
                                        </div>
                                        <div class="info-card animate-slide-in-left" style="animation-delay: 0.4s;">
                                            <div class="card-icon">
                                                <i class="fas fa-dumbbell"></i>
                                            </div>
                                            <h4>EMG</h4>
                                            <p>Electromyogram - Muscle electrical activity</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="biosignal-demo">
                                            <div class="demo-image">
                                                <img src="https://images.unsplash.com/photo-**********-5c350d0d3c56?w=500&h=300&fit=crop&crop=center" alt="Medical monitoring equipment" class="demo-photo">
                                                <div class="image-overlay">
                                                    <div class="overlay-content">
                                                        <i class="fas fa-play-circle"></i>
                                                        <span>Interactive Demo</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="signal-visualization">
                                                <canvas id="biosignalCanvas" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="slide-footer">
                            <div class="key-points">
                                <h4>Key Learning Points:</h4>
                                <ul>
                                    <li>Biosignals reflect physiological processes</li>
                                    <li>Different types serve different diagnostic purposes</li>
                                    <li>Signal processing is essential for analysis</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 2: ECG Fundamentals -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">02</div>
                            <h2 class="slide-title">ECG Fundamentals</h2>
                            <p class="slide-subtitle">Understanding the electrocardiogram and cardiac electrical activity</p>
                        </div>
                        
                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>The Cardiac Electrical System</h3>
                                    <p>The heart has its own electrical system that controls the heartbeat. This electrical activity can be measured on the skin surface as an ECG.</p>
                                    
                                    <div class="ecg-components">
                                        <div class="component-item" data-component="p-wave">
                                            <div class="component-marker">P</div>
                                            <div class="component-info">
                                                <h4>P Wave</h4>
                                                <p>Atrial depolarization</p>
                                            </div>
                                        </div>
                                        <div class="component-item" data-component="qrs-complex">
                                            <div class="component-marker">QRS</div>
                                            <div class="component-info">
                                                <h4>QRS Complex</h4>
                                                <p>Ventricular depolarization</p>
                                            </div>
                                        </div>
                                        <div class="component-item" data-component="t-wave">
                                            <div class="component-marker">T</div>
                                            <div class="component-info">
                                                <h4>T Wave</h4>
                                                <p>Ventricular repolarization</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="heart-anatomy">
                                            <img src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop&crop=center" alt="Heart anatomy" class="anatomy-image">
                                            <div class="anatomy-overlay">
                                                <div class="electrical-path">
                                                    <div class="path-point sa-node" data-tooltip="SA Node - Natural Pacemaker">
                                                        <div class="pulse-animation"></div>
                                                    </div>
                                                    <div class="path-point av-node" data-tooltip="AV Node - Electrical Relay">
                                                        <div class="pulse-animation"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ecg-waveform-demo">
                                            <canvas id="ecgWaveformCanvas" width="400" height="150"></canvas>
                                            <div class="waveform-controls">
                                                <button type="button" class="control-btn" onclick="toggleECGAnimation()">
                                                    <i class="fas fa-play" id="ecgPlayIcon"></i>
                                                </button>
                                                <div class="heart-rate-display">
                                                    <span id="heartRateValue">72</span> BPM
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="slide-footer">
                            <div class="interactive-quiz">
                                <h4>Quick Check:</h4>
                                <div class="quiz-question">
                                    <p>Which component represents ventricular depolarization?</p>
                                    <div class="quiz-options">
                                        <button type="button" class="quiz-option" data-answer="false">P Wave</button>
                                        <button type="button" class="quiz-option" data-answer="true">QRS Complex</button>
                                        <button type="button" class="quiz-option" data-answer="false">T Wave</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 3: Signal Acquisition -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">03</div>
                            <h2 class="slide-title">Signal Acquisition</h2>
                            <p class="slide-subtitle">How ECG signals are captured and digitized</p>
                        </div>

                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>From Heart to Digital Signal</h3>
                                    <p>ECG acquisition involves several steps to convert the heart's electrical activity into a digital signal that can be processed and analyzed.</p>

                                    <div class="acquisition-steps">
                                        <div class="step-item">
                                            <div class="step-number">1</div>
                                            <div class="step-content">
                                                <h4>Electrode Placement</h4>
                                                <p>Electrodes detect electrical activity on the skin surface</p>
                                            </div>
                                        </div>
                                        <div class="step-item">
                                            <div class="step-number">2</div>
                                            <div class="step-content">
                                                <h4>Amplification</h4>
                                                <p>Weak electrical signals are amplified for processing</p>
                                            </div>
                                        </div>
                                        <div class="step-item">
                                            <div class="step-number">3</div>
                                            <div class="step-content">
                                                <h4>Filtering</h4>
                                                <p>Unwanted noise and artifacts are removed</p>
                                            </div>
                                        </div>
                                        <div class="step-item">
                                            <div class="step-number">4</div>
                                            <div class="step-content">
                                                <h4>Digitization</h4>
                                                <p>Analog signals are converted to digital format</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="acquisition-diagram">
                                            <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=500&h=300&fit=crop&crop=center" alt="ECG monitoring setup" class="setup-image">
                                            <div class="signal-flow">
                                                <div class="flow-step" data-step="1">
                                                    <div class="flow-icon">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <span>Patient</span>
                                                </div>
                                                <div class="flow-arrow">→</div>
                                                <div class="flow-step" data-step="2">
                                                    <div class="flow-icon">
                                                        <i class="fas fa-plug"></i>
                                                    </div>
                                                    <span>Electrodes</span>
                                                </div>
                                                <div class="flow-arrow">→</div>
                                                <div class="flow-step" data-step="3">
                                                    <div class="flow-icon">
                                                        <i class="fas fa-microchip"></i>
                                                    </div>
                                                    <span>Amplifier</span>
                                                </div>
                                                <div class="flow-arrow">→</div>
                                                <div class="flow-step" data-step="4">
                                                    <div class="flow-icon">
                                                        <i class="fas fa-laptop"></i>
                                                    </div>
                                                    <span>Computer</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="sampling-demo">
                                            <h4>Sampling Rate Demonstration</h4>
                                            <div class="sampling-controls">
                                                <label>Sampling Rate: <span id="samplingRateValue">360</span> Hz</label>
                                                <input type="range" id="samplingRateSlider" min="50" max="1000" value="360" step="10">
                                            </div>
                                            <canvas id="samplingCanvas" width="400" height="120"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 4: Noise and Artifacts -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">04</div>
                            <h2 class="slide-title">Noise and Artifacts</h2>
                            <p class="slide-subtitle">Common signal problems and their sources</p>
                        </div>

                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>Types of ECG Artifacts</h3>
                                    <p>ECG signals can be contaminated by various types of noise and artifacts that must be identified and removed for accurate analysis.</p>

                                    <div class="artifact-types">
                                        <div class="artifact-item" data-artifact="baseline">
                                            <div class="artifact-icon">
                                                <i class="fas fa-wave-square"></i>
                                            </div>
                                            <div class="artifact-info">
                                                <h4>Baseline Wander</h4>
                                                <p>Low-frequency drift caused by respiration and movement</p>
                                                <span class="frequency-range">0.05-2 Hz</span>
                                            </div>
                                        </div>

                                        <div class="artifact-item" data-artifact="powerline">
                                            <div class="artifact-icon">
                                                <i class="fas fa-bolt"></i>
                                            </div>
                                            <div class="artifact-info">
                                                <h4>Powerline Interference</h4>
                                                <p>50/60 Hz electrical interference from power lines</p>
                                                <span class="frequency-range">50/60 Hz</span>
                                            </div>
                                        </div>

                                        <div class="artifact-item" data-artifact="muscle">
                                            <div class="artifact-icon">
                                                <i class="fas fa-dumbbell"></i>
                                            </div>
                                            <div class="artifact-info">
                                                <h4>Muscle Artifacts</h4>
                                                <p>EMG interference from muscle contractions</p>
                                                <span class="frequency-range">20-200 Hz</span>
                                            </div>
                                        </div>

                                        <div class="artifact-item" data-artifact="motion">
                                            <div class="artifact-icon">
                                                <i class="fas fa-running"></i>
                                            </div>
                                            <div class="artifact-info">
                                                <h4>Motion Artifacts</h4>
                                                <p>Electrode movement and poor contact</p>
                                                <span class="frequency-range">Variable</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="artifact-demo">
                                            <img src="https://images.unsplash.com/photo-1551601651-2a8555f1a136?w=500&h=250&fit=crop&crop=center" alt="ECG with artifacts" class="demo-photo">
                                            <div class="artifact-controls">
                                                <h4>Interactive Artifact Simulator</h4>
                                                <div class="control-group">
                                                    <label>
                                                        <input type="checkbox" id="baselineWander" checked>
                                                        Baseline Wander
                                                    </label>
                                                    <label>
                                                        <input type="checkbox" id="powerlineNoise">
                                                        Powerline Noise
                                                    </label>
                                                    <label>
                                                        <input type="checkbox" id="muscleArtifact">
                                                        Muscle Artifact
                                                    </label>
                                                </div>
                                                <canvas id="artifactCanvas" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="slide-footer">
                            <div class="artifact-solutions">
                                <h4>💡 Artifact Solutions:</h4>
                                <div class="solution-grid">
                                    <div class="solution-item">
                                        <strong>Baseline Wander:</strong> High-pass filtering (0.5 Hz)
                                    </div>
                                    <div class="solution-item">
                                        <strong>Powerline Noise:</strong> Notch filtering (50/60 Hz)
                                    </div>
                                    <div class="solution-item">
                                        <strong>Muscle Artifacts:</strong> Low-pass filtering (40 Hz)
                                    </div>
                                    <div class="solution-item">
                                        <strong>Motion Artifacts:</strong> Proper electrode placement
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 5: Filtering Techniques -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">05</div>
                            <h2 class="slide-title">Filtering Techniques</h2>
                            <p class="slide-subtitle">Signal preprocessing and noise removal methods</p>
                        </div>

                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>Digital Filter Types</h3>
                                    <p>Digital filters are essential for removing noise and artifacts while preserving the important characteristics of the ECG signal.</p>

                                    <div class="filter-types">
                                        <div class="filter-card" data-filter="lowpass">
                                            <div class="filter-header">
                                                <div class="filter-icon">
                                                    <i class="fas fa-arrow-down"></i>
                                                </div>
                                                <h4>Low-Pass Filter</h4>
                                            </div>
                                            <p>Removes high-frequency noise while preserving low-frequency components</p>
                                            <div class="filter-specs">
                                                <span>Cutoff: 40-100 Hz</span>
                                                <span>Use: Remove muscle artifacts</span>
                                            </div>
                                        </div>

                                        <div class="filter-card" data-filter="highpass">
                                            <div class="filter-header">
                                                <div class="filter-icon">
                                                    <i class="fas fa-arrow-up"></i>
                                                </div>
                                                <h4>High-Pass Filter</h4>
                                            </div>
                                            <p>Removes low-frequency drift and baseline wander</p>
                                            <div class="filter-specs">
                                                <span>Cutoff: 0.5-1 Hz</span>
                                                <span>Use: Remove baseline drift</span>
                                            </div>
                                        </div>

                                        <div class="filter-card" data-filter="bandpass">
                                            <div class="filter-header">
                                                <div class="filter-icon">
                                                    <i class="fas fa-arrows-alt-h"></i>
                                                </div>
                                                <h4>Band-Pass Filter</h4>
                                            </div>
                                            <p>Preserves frequencies within a specific range</p>
                                            <div class="filter-specs">
                                                <span>Range: 0.5-40 Hz</span>
                                                <span>Use: ECG signal band</span>
                                            </div>
                                        </div>

                                        <div class="filter-card" data-filter="notch">
                                            <div class="filter-header">
                                                <div class="filter-icon">
                                                    <i class="fas fa-times"></i>
                                                </div>
                                                <h4>Notch Filter</h4>
                                            </div>
                                            <p>Removes specific frequency components</p>
                                            <div class="filter-specs">
                                                <span>Frequency: 50/60 Hz</span>
                                                <span>Use: Remove powerline noise</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="filter-demo">
                                            <img src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=500&h=200&fit=crop&crop=center" alt="Signal processing" class="demo-photo">
                                            <div class="interactive-filter">
                                                <h4>Interactive Filter Designer</h4>
                                                <div class="filter-controls">
                                                    <div class="control-row">
                                                        <label>Filter Type:</label>
                                                        <select id="filterType">
                                                            <option value="lowpass">Low-Pass</option>
                                                            <option value="highpass">High-Pass</option>
                                                            <option value="bandpass" selected>Band-Pass</option>
                                                            <option value="notch">Notch</option>
                                                        </select>
                                                    </div>
                                                    <div class="control-row">
                                                        <label>Cutoff Frequency: <span id="cutoffValue">40</span> Hz</label>
                                                        <input type="range" id="cutoffSlider" min="1" max="100" value="40">
                                                    </div>
                                                </div>
                                                <canvas id="filterResponseCanvas" width="400" height="150"></canvas>
                                                <canvas id="filteredSignalCanvas" width="400" height="120"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 6: R-Peak Detection -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">06</div>
                            <h2 class="slide-title">R-Peak Detection</h2>
                            <p class="slide-subtitle">Pan-Tompkins algorithm and QRS detection</p>
                        </div>

                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>Pan-Tompkins Algorithm</h3>
                                    <p>The Pan-Tompkins algorithm is the gold standard for real-time QRS detection in ECG signals, combining filtering, differentiation, and adaptive thresholding.</p>

                                    <div class="algorithm-steps">
                                        <div class="algo-step" data-step="1">
                                            <div class="step-badge">1</div>
                                            <div class="step-details">
                                                <h4>Band-Pass Filtering</h4>
                                                <p>5-15 Hz filter to enhance QRS complex</p>
                                            </div>
                                        </div>

                                        <div class="algo-step" data-step="2">
                                            <div class="step-badge">2</div>
                                            <div class="step-details">
                                                <h4>Differentiation</h4>
                                                <p>Emphasize high-frequency components</p>
                                            </div>
                                        </div>

                                        <div class="algo-step" data-step="3">
                                            <div class="step-badge">3</div>
                                            <div class="step-details">
                                                <h4>Squaring</h4>
                                                <p>Make all data positive and emphasize peaks</p>
                                            </div>
                                        </div>

                                        <div class="algo-step" data-step="4">
                                            <div class="step-badge">4</div>
                                            <div class="step-details">
                                                <h4>Moving Window Integration</h4>
                                                <p>Smooth the signal and extract features</p>
                                            </div>
                                        </div>

                                        <div class="algo-step" data-step="5">
                                            <div class="step-badge">5</div>
                                            <div class="step-details">
                                                <h4>Adaptive Thresholding</h4>
                                                <p>Dynamic threshold for peak detection</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="rpeak-demo">
                                            <img src="https://images.unsplash.com/photo-**********-5c350d0d3c56?w=500&h=200&fit=crop&crop=center" alt="ECG analysis" class="demo-photo">
                                            <div class="detection-demo">
                                                <h4>Live R-Peak Detection</h4>
                                                <div class="demo-controls">
                                                    <button type="button" class="demo-btn" onclick="startRPeakDemo()">
                                                        <i class="fas fa-play"></i> Start Demo
                                                    </button>
                                                    <div class="detection-stats">
                                                        <span>Heart Rate: <span id="detectedHR">--</span> BPM</span>
                                                        <span>R-Peaks: <span id="rPeakCount">0</span></span>
                                                    </div>
                                                </div>
                                                <canvas id="rPeakCanvas" width="400" height="200"></canvas>
                                                <div class="processing-stages">
                                                    <canvas id="filteredCanvas" width="400" height="80"></canvas>
                                                    <canvas id="differentiatedCanvas" width="400" height="80"></canvas>
                                                    <canvas id="squaredCanvas" width="400" height="80"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="slide-footer">
                            <div class="algorithm-performance">
                                <h4>🎯 Algorithm Performance:</h4>
                                <div class="performance-metrics">
                                    <div class="metric-item">
                                        <span class="metric-label">Sensitivity:</span>
                                        <span class="metric-value">99.3%</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">Specificity:</span>
                                        <span class="metric-value">99.7%</span>
                                    </div>
                                    <div class="metric-item">
                                        <span class="metric-label">Accuracy:</span>
                                        <span class="metric-value">99.5%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 7: HRV Time Domain -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">07</div>
                            <h2 class="slide-title">HRV Time Domain Analysis</h2>
                            <p class="slide-subtitle">Statistical measures of heart rate variability</p>
                        </div>

                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>Time Domain Parameters</h3>
                                    <p>Time domain analysis examines the variation in RR intervals over time using statistical and geometric methods.</p>

                                    <div class="hrv-parameters">
                                        <div class="parameter-card" data-param="sdnn">
                                            <div class="param-icon">
                                                <i class="fas fa-chart-line"></i>
                                            </div>
                                            <div class="param-info">
                                                <h4>SDNN</h4>
                                                <p>Standard deviation of NN intervals</p>
                                                <div class="param-details">
                                                    <span>Normal: 20-50 ms</span>
                                                    <span>Reflects: Overall HRV</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="parameter-card" data-param="rmssd">
                                            <div class="param-icon">
                                                <i class="fas fa-square-root-alt"></i>
                                            </div>
                                            <div class="param-info">
                                                <h4>RMSSD</h4>
                                                <p>Root mean square of successive differences</p>
                                                <div class="param-details">
                                                    <span>Normal: 15-40 ms</span>
                                                    <span>Reflects: Parasympathetic activity</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="parameter-card" data-param="pnn50">
                                            <div class="param-icon">
                                                <i class="fas fa-percentage"></i>
                                            </div>
                                            <div class="param-info">
                                                <h4>pNN50</h4>
                                                <p>Percentage of NN intervals > 50ms different</p>
                                                <div class="param-details">
                                                    <span>Normal: 5-20%</span>
                                                    <span>Reflects: Vagal tone</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="parameter-card" data-param="triangular">
                                            <div class="param-icon">
                                                <i class="fas fa-play"></i>
                                            </div>
                                            <div class="param-info">
                                                <h4>Triangular Index</h4>
                                                <p>Total NN intervals / height of histogram</p>
                                                <div class="param-details">
                                                    <span>Normal: 20-50</span>
                                                    <span>Reflects: Overall variability</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="hrv-analysis">
                                            <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=500&h=200&fit=crop&crop=center" alt="Heart rate monitoring" class="demo-photo">
                                            <div class="hrv-calculator">
                                                <h4>Interactive HRV Calculator</h4>
                                                <div class="rr-intervals">
                                                    <canvas id="rrIntervalCanvas" width="400" height="150"></canvas>
                                                </div>
                                                <div class="hrv-results">
                                                    <div class="result-grid">
                                                        <div class="result-item">
                                                            <span class="result-label">SDNN:</span>
                                                            <span class="result-value" id="sdnnValue">--</span>
                                                            <span class="result-unit">ms</span>
                                                        </div>
                                                        <div class="result-item">
                                                            <span class="result-label">RMSSD:</span>
                                                            <span class="result-value" id="rmssdValue">--</span>
                                                            <span class="result-unit">ms</span>
                                                        </div>
                                                        <div class="result-item">
                                                            <span class="result-label">pNN50:</span>
                                                            <span class="result-value" id="pnn50Value">--</span>
                                                            <span class="result-unit">%</span>
                                                        </div>
                                                        <div class="result-item">
                                                            <span class="result-label">Mean HR:</span>
                                                            <span class="result-value" id="meanHRValue">--</span>
                                                            <span class="result-unit">BPM</span>
                                                        </div>
                                                    </div>
                                                    <button type="button" class="calculate-btn" onclick="calculateHRV()">
                                                        <i class="fas fa-calculator"></i> Calculate HRV
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 8: HRV Frequency Domain -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">08</div>
                            <h2 class="slide-title">HRV Frequency Domain Analysis</h2>
                            <p class="slide-subtitle">Spectral analysis of heart rate variability</p>
                        </div>

                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>Frequency Bands</h3>
                                    <p>Frequency domain analysis uses power spectral density to analyze the distribution of power in different frequency bands.</p>

                                    <div class="frequency-bands">
                                        <div class="band-card" data-band="vlf">
                                            <div class="band-color vlf-color"></div>
                                            <div class="band-info">
                                                <h4>VLF (Very Low Frequency)</h4>
                                                <p>0.003 - 0.04 Hz</p>
                                                <div class="band-details">
                                                    <span>Reflects: Thermoregulation, hormonal influences</span>
                                                    <span>Normal: 20-30% of total power</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="band-card" data-band="lf">
                                            <div class="band-color lf-color"></div>
                                            <div class="band-info">
                                                <h4>LF (Low Frequency)</h4>
                                                <p>0.04 - 0.15 Hz</p>
                                                <div class="band-details">
                                                    <span>Reflects: Sympathetic and parasympathetic activity</span>
                                                    <span>Normal: 30-40% of total power</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="band-card" data-band="hf">
                                            <div class="band-color hf-color"></div>
                                            <div class="band-info">
                                                <h4>HF (High Frequency)</h4>
                                                <p>0.15 - 0.4 Hz</p>
                                                <div class="band-details">
                                                    <span>Reflects: Parasympathetic (vagal) activity</span>
                                                    <span>Normal: 30-40% of total power</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="ratio-analysis">
                                        <h4>Important Ratios</h4>
                                        <div class="ratio-item">
                                            <strong>LF/HF Ratio:</strong> Sympatho-vagal balance (Normal: 1.5-2.0)
                                        </div>
                                        <div class="ratio-item">
                                            <strong>Total Power:</strong> Overall autonomic activity
                                        </div>
                                    </div>
                                </div>

                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="spectral-analysis">
                                            <img src="https://images.unsplash.com/photo-1551601651-2a8555f1a136?w=500&h=200&fit=crop&crop=center" alt="Spectral analysis" class="demo-photo">
                                            <div class="spectrum-demo">
                                                <h4>Power Spectral Density</h4>
                                                <canvas id="spectrumCanvas" width="400" height="200"></canvas>
                                                <div class="spectrum-controls">
                                                    <button type="button" class="spectrum-btn" onclick="generateSpectrum()">
                                                        <i class="fas fa-chart-area"></i> Generate Spectrum
                                                    </button>
                                                    <div class="band-powers">
                                                        <div class="power-item vlf-bg">
                                                            <span>VLF: <span id="vlfPower">--</span> ms²</span>
                                                        </div>
                                                        <div class="power-item lf-bg">
                                                            <span>LF: <span id="lfPower">--</span> ms²</span>
                                                        </div>
                                                        <div class="power-item hf-bg">
                                                            <span>HF: <span id="hfPower">--</span> ms²</span>
                                                        </div>
                                                        <div class="power-item ratio-bg">
                                                            <span>LF/HF: <span id="lfhfRatio">--</span></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 9: Nonlinear Analysis -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">09</div>
                            <h2 class="slide-title">Nonlinear HRV Analysis</h2>
                            <p class="slide-subtitle">Complexity and chaos measures</p>
                        </div>

                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>Nonlinear Methods</h3>
                                    <p>Nonlinear analysis provides insights into the complex dynamics of heart rate regulation that cannot be captured by traditional linear methods.</p>

                                    <div class="nonlinear-methods">
                                        <div class="method-card" data-method="poincare">
                                            <div class="method-icon">
                                                <i class="fas fa-circle-notch"></i>
                                            </div>
                                            <div class="method-info">
                                                <h4>Poincaré Plot</h4>
                                                <p>Scatter plot of RR(n) vs RR(n+1)</p>
                                                <div class="method-params">
                                                    <span>SD1: Short-term variability</span>
                                                    <span>SD2: Long-term variability</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="method-card" data-method="dfa">
                                            <div class="method-icon">
                                                <i class="fas fa-project-diagram"></i>
                                            </div>
                                            <div class="method-info">
                                                <h4>Detrended Fluctuation Analysis</h4>
                                                <p>Measures fractal scaling properties</p>
                                                <div class="method-params">
                                                    <span>α1: Short-term scaling (4-11 beats)</span>
                                                    <span>α2: Long-term scaling (11-64 beats)</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="method-card" data-method="entropy">
                                            <div class="method-icon">
                                                <i class="fas fa-random"></i>
                                            </div>
                                            <div class="method-info">
                                                <h4>Sample Entropy</h4>
                                                <p>Measures signal regularity and complexity</p>
                                                <div class="method-params">
                                                    <span>Lower values: More regular</span>
                                                    <span>Higher values: More complex</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="nonlinear-demo">
                                            <img src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=500&h=200&fit=crop&crop=center" alt="Complex analysis" class="demo-photo">
                                            <div class="poincare-plot">
                                                <h4>Interactive Poincaré Plot</h4>
                                                <canvas id="poincareCanvas" width="300" height="300"></canvas>
                                                <div class="poincare-metrics">
                                                    <div class="metric-row">
                                                        <span>SD1: <span id="sd1Value">--</span> ms</span>
                                                        <span>SD2: <span id="sd2Value">--</span> ms</span>
                                                    </div>
                                                    <div class="metric-row">
                                                        <span>SD1/SD2: <span id="sd1sd2Ratio">--</span></span>
                                                        <span>Area: <span id="ellipseArea">--</span> ms²</span>
                                                    </div>
                                                </div>
                                                <button type="button" class="plot-btn" onclick="generatePoincareData()">
                                                    <i class="fas fa-sync-alt"></i> Generate New Data
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="slide-footer">
                            <div class="clinical-significance">
                                <h4>🏥 Clinical Significance:</h4>
                                <ul>
                                    <li><strong>Reduced complexity:</strong> Associated with aging and disease</li>
                                    <li><strong>Fractal properties:</strong> Indicate healthy heart rate dynamics</li>
                                    <li><strong>Entropy measures:</strong> Reflect autonomic nervous system function</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 10: Clinical Applications -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">10</div>
                            <h2 class="slide-title">Clinical Applications</h2>
                            <p class="slide-subtitle">Real-world medical applications of HRV analysis</p>
                        </div>

                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>Medical Applications</h3>
                                    <p>HRV analysis has proven valuable in various clinical settings for diagnosis, prognosis, and treatment monitoring.</p>

                                    <div class="clinical-applications">
                                        <div class="application-card" data-app="cardiology">
                                            <div class="app-icon">
                                                <i class="fas fa-heart"></i>
                                            </div>
                                            <div class="app-info">
                                                <h4>Cardiology</h4>
                                                <ul>
                                                    <li>Post-MI risk stratification</li>
                                                    <li>Heart failure monitoring</li>
                                                    <li>Arrhythmia prediction</li>
                                                    <li>Sudden cardiac death risk</li>
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="application-card" data-app="diabetes">
                                            <div class="app-icon">
                                                <i class="fas fa-tint"></i>
                                            </div>
                                            <div class="app-info">
                                                <h4>Diabetes</h4>
                                                <ul>
                                                    <li>Diabetic neuropathy detection</li>
                                                    <li>Autonomic dysfunction assessment</li>
                                                    <li>Glycemic control monitoring</li>
                                                    <li>Complication prediction</li>
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="application-card" data-app="sports">
                                            <div class="app-icon">
                                                <i class="fas fa-running"></i>
                                            </div>
                                            <div class="app-info">
                                                <h4>Sports Medicine</h4>
                                                <ul>
                                                    <li>Training load optimization</li>
                                                    <li>Recovery monitoring</li>
                                                    <li>Overtraining detection</li>
                                                    <li>Performance prediction</li>
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="application-card" data-app="mental">
                                            <div class="app-icon">
                                                <i class="fas fa-brain"></i>
                                            </div>
                                            <div class="app-info">
                                                <h4>Mental Health</h4>
                                                <ul>
                                                    <li>Stress level assessment</li>
                                                    <li>Depression monitoring</li>
                                                    <li>Anxiety disorder evaluation</li>
                                                    <li>Meditation effectiveness</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="case-study">
                                            <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=500&h=200&fit=crop&crop=center" alt="Clinical monitoring" class="demo-photo">
                                            <div class="case-example">
                                                <h4>Case Study: Post-MI Patient</h4>
                                                <div class="patient-data">
                                                    <div class="data-section">
                                                        <h5>Patient Profile</h5>
                                                        <div class="profile-item">Age: 65 years</div>
                                                        <div class="profile-item">Gender: Male</div>
                                                        <div class="profile-item">Condition: 3 months post-MI</div>
                                                    </div>

                                                    <div class="data-section">
                                                        <h5>HRV Parameters</h5>
                                                        <div class="hrv-comparison">
                                                            <div class="comparison-row">
                                                                <span>SDNN:</span>
                                                                <span class="normal-value">Normal: 40±15 ms</span>
                                                                <span class="patient-value low">Patient: 18 ms</span>
                                                            </div>
                                                            <div class="comparison-row">
                                                                <span>RMSSD:</span>
                                                                <span class="normal-value">Normal: 30±12 ms</span>
                                                                <span class="patient-value low">Patient: 12 ms</span>
                                                            </div>
                                                            <div class="comparison-row">
                                                                <span>LF/HF:</span>
                                                                <span class="normal-value">Normal: 1.5-2.0</span>
                                                                <span class="patient-value high">Patient: 3.2</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="data-section">
                                                        <h5>Clinical Interpretation</h5>
                                                        <div class="interpretation">
                                                            <div class="finding">
                                                                <i class="fas fa-exclamation-triangle"></i>
                                                                <span>Reduced HRV indicates increased risk</span>
                                                            </div>
                                                            <div class="finding">
                                                                <i class="fas fa-chart-line"></i>
                                                                <span>Elevated LF/HF suggests sympathetic dominance</span>
                                                            </div>
                                                            <div class="finding">
                                                                <i class="fas fa-stethoscope"></i>
                                                                <span>Requires close monitoring and intervention</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 11: Quality Assessment -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">11</div>
                            <h2 class="slide-title">Signal Quality Assessment</h2>
                            <p class="slide-subtitle">Ensuring reliable HRV analysis</p>
                        </div>

                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>Quality Control Measures</h3>
                                    <p>Signal quality assessment is crucial for reliable HRV analysis. Poor quality signals can lead to incorrect clinical interpretations.</p>

                                    <div class="quality-metrics">
                                        <div class="metric-card" data-metric="snr">
                                            <div class="metric-icon">
                                                <i class="fas fa-signal"></i>
                                            </div>
                                            <div class="metric-info">
                                                <h4>Signal-to-Noise Ratio</h4>
                                                <p>Measures signal quality relative to noise</p>
                                                <div class="metric-threshold">
                                                    <span class="good">Good: > 20 dB</span>
                                                    <span class="poor">Poor: < 10 dB</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="metric-card" data-metric="artifacts">
                                            <div class="metric-icon">
                                                <i class="fas fa-exclamation-circle"></i>
                                            </div>
                                            <div class="metric-info">
                                                <h4>Artifact Percentage</h4>
                                                <p>Proportion of corrupted beats</p>
                                                <div class="metric-threshold">
                                                    <span class="good">Acceptable: < 5%</span>
                                                    <span class="poor">Unacceptable: > 20%</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="metric-card" data-metric="duration">
                                            <div class="metric-icon">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                            <div class="metric-info">
                                                <h4>Recording Duration</h4>
                                                <p>Minimum time for reliable analysis</p>
                                                <div class="metric-threshold">
                                                    <span class="good">Short-term: 5 min</span>
                                                    <span class="good">Long-term: 24 hours</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="quality-checklist">
                                        <h4>Quality Assessment Checklist</h4>
                                        <div class="checklist-item">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Proper electrode placement and skin preparation</span>
                                        </div>
                                        <div class="checklist-item">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Adequate sampling rate (≥ 250 Hz)</span>
                                        </div>
                                        <div class="checklist-item">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Minimal motion artifacts and noise</span>
                                        </div>
                                        <div class="checklist-item">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Sufficient recording duration</span>
                                        </div>
                                        <div class="checklist-item">
                                            <i class="fas fa-check-circle"></i>
                                            <span>Accurate R-peak detection (> 95%)</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="quality-demo">
                                            <img src="https://images.unsplash.com/photo-**********-5c350d0d3c56?w=500&h=200&fit=crop&crop=center" alt="Signal quality" class="demo-photo">
                                            <div class="quality-analyzer">
                                                <h4>Signal Quality Analyzer</h4>
                                                <div class="quality-display">
                                                    <canvas id="qualityCanvas" width="400" height="200"></canvas>
                                                    <div class="quality-indicators">
                                                        <div class="indicator-row">
                                                            <span class="indicator-label">SNR:</span>
                                                            <div class="indicator-bar">
                                                                <div class="bar-fill" id="snrBar" style="width: 75%"></div>
                                                            </div>
                                                            <span class="indicator-value" id="snrValue">18.5 dB</span>
                                                        </div>

                                                        <div class="indicator-row">
                                                            <span class="indicator-label">Artifacts:</span>
                                                            <div class="indicator-bar">
                                                                <div class="bar-fill artifact-bar" id="artifactBar" style="width: 15%"></div>
                                                            </div>
                                                            <span class="indicator-value" id="artifactValue">3.2%</span>
                                                        </div>

                                                        <div class="indicator-row">
                                                            <span class="indicator-label">R-Peak Accuracy:</span>
                                                            <div class="indicator-bar">
                                                                <div class="bar-fill" id="accuracyBar" style="width: 97%"></div>
                                                            </div>
                                                            <span class="indicator-value" id="accuracyValue">97.3%</span>
                                                        </div>
                                                    </div>

                                                    <div class="overall-quality">
                                                        <span class="quality-label">Overall Quality:</span>
                                                        <span class="quality-status good" id="qualityStatus">GOOD</span>
                                                    </div>
                                                </div>

                                                <button type="button" class="analyze-btn" onclick="analyzeSignalQuality()">
                                                    <i class="fas fa-search"></i> Analyze Quality
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 12: Summary & Practice -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">12</div>
                            <h2 class="slide-title">Summary & Practice</h2>
                            <p class="slide-subtitle">Key takeaways and hands-on exercises</p>
                        </div>

                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>Course Summary</h3>
                                    <p>You have completed a comprehensive journey through biosignal processing and HRV analysis. Let's review the key concepts and practice your skills.</p>

                                    <div class="summary-topics">
                                        <div class="topic-summary">
                                            <h4>🔬 Signal Processing</h4>
                                            <ul>
                                                <li>ECG signal acquisition and digitization</li>
                                                <li>Noise identification and filtering techniques</li>
                                                <li>R-peak detection using Pan-Tompkins algorithm</li>
                                            </ul>
                                        </div>

                                        <div class="topic-summary">
                                            <h4>📊 HRV Analysis</h4>
                                            <ul>
                                                <li>Time domain parameters (SDNN, RMSSD, pNN50)</li>
                                                <li>Frequency domain analysis (VLF, LF, HF bands)</li>
                                                <li>Nonlinear methods (Poincaré, DFA, entropy)</li>
                                            </ul>
                                        </div>

                                        <div class="topic-summary">
                                            <h4>🏥 Clinical Applications</h4>
                                            <ul>
                                                <li>Cardiovascular risk assessment</li>
                                                <li>Autonomic nervous system evaluation</li>
                                                <li>Quality control and validation</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="practice-exercises">
                                        <h4>Practice Exercises</h4>
                                        <div class="exercise-list">
                                            <div class="exercise-item">
                                                <span class="exercise-number">1</span>
                                                <span>Load sample ECG data and perform R-peak detection</span>
                                            </div>
                                            <div class="exercise-item">
                                                <span class="exercise-number">2</span>
                                                <span>Calculate time domain HRV parameters</span>
                                            </div>
                                            <div class="exercise-item">
                                                <span class="exercise-number">3</span>
                                                <span>Generate power spectral density plot</span>
                                            </div>
                                            <div class="exercise-item">
                                                <span class="exercise-number">4</span>
                                                <span>Create Poincaré plot and calculate SD1/SD2</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="completion-certificate">
                                            <img src="https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=500&h=300&fit=crop&crop=center" alt="Certificate" class="demo-photo">
                                            <div class="certificate-content">
                                                <h4>🎓 Course Completion</h4>
                                                <div class="achievement-stats">
                                                    <div class="stat-item">
                                                        <span class="stat-number">12</span>
                                                        <span class="stat-label">Slides Completed</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-number">8</span>
                                                        <span class="stat-label">Interactive Demos</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-number">5</span>
                                                        <span class="stat-label">Quizzes Passed</span>
                                                    </div>
                                                </div>

                                                <div class="next-steps">
                                                    <h5>Next Steps</h5>
                                                    <button type="button" class="action-btn primary" onclick="navigateToModule('signal-processing')">
                                                        <i class="fas fa-play"></i> Start Practical Lab
                                                    </button>
                                                    <button type="button" class="action-btn secondary" onclick="downloadCertificate()">
                                                        <i class="fas fa-download"></i> Download Certificate
                                                    </button>
                                                    <button type="button" class="action-btn outline" onclick="restartCourse()">
                                                        <i class="fas fa-redo"></i> Restart Course
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="slide-footer">
                            <div class="course-feedback">
                                <h4>📝 Course Feedback</h4>
                                <p>Help us improve this learning module by providing your feedback.</p>
                                <div class="feedback-actions">
                                    <button type="button" class="feedback-btn" onclick="provideFeedback()">
                                        <i class="fas fa-comment"></i> Provide Feedback
                                    </button>
                                    <button type="button" class="feedback-btn" onclick="shareModule()">
                                        <i class="fas fa-share"></i> Share Module
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Swiper Pagination -->
            <div class="swiper-pagination"></div>
        </div>
    </div>

    <!-- Slide Thumbnails -->
    <div class="slide-thumbnails">
        <div class="container">
            <h3>Course Outline</h3>
            <div class="thumbnail-grid">
                <div class="thumbnail-item active" data-slide="0">
                    <div class="thumbnail-number">01</div>
                    <div class="thumbnail-content">
                        <h4>Introduction to Biosignals</h4>
                        <p>Basic concepts and types</p>
                    </div>
                </div>
                <div class="thumbnail-item" data-slide="1">
                    <div class="thumbnail-number">02</div>
                    <div class="thumbnail-content">
                        <h4>ECG Fundamentals</h4>
                        <p>Cardiac electrical activity</p>
                    </div>
                </div>
                <div class="thumbnail-item" data-slide="2">
                    <div class="thumbnail-number">03</div>
                    <div class="thumbnail-content">
                        <h4>Signal Acquisition</h4>
                        <p>From analog to digital</p>
                    </div>
                </div>
                <!-- More thumbnails will be added via JavaScript -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading interactive content...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/core/utils.js"></script>
    <script src="js/learning-module.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
