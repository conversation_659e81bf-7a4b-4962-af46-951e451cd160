<!DOCTYPE html>
<!--
    Biosignal Virtual Lab - Interactive Learning Module
    
    Author: Dr. <PERSON> Esmail
    Institution: SUST - BME (Sudan University of Science and Technology - Biomedical Engineering)
    Year: 2025
    
    Contact Information:
    Email: <EMAIL>
    Phone: +249912867327, +966538076790
    
    Copyright © 2025 Dr. <PERSON>il. All rights reserved.
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Learning Module - Biosignal Virtual Lab</title>
    <meta name="author" content="Dr. <PERSON>smail">
    <meta name="copyright" content="© 2025 Dr. <PERSON>il">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/learning-module.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-heartbeat"></i>
                <span>Biosignal Virtual Lab</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="learning-module.html" class="nav-link active">Learning Module</a>
                <a href="signal-processing.html" class="nav-link">Signal Processing</a>
                <a href="r-peak-detection.html" class="nav-link">R-Peak Detection</a>
                <a href="hrv-analysis.html" class="nav-link">HRV Analysis</a>
                <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
                <div class="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Learning Module Header -->
    <div class="learning-header">
        <div class="container">
            <div class="header-content">
                <div class="header-text">
                    <h1 class="learning-title">
                        <i class="fas fa-graduation-cap"></i>
                        Interactive Biosignal Processing Learning Module
                    </h1>
                    <p class="learning-subtitle">
                        Master ECG signal processing, R-peak detection, and HRV analysis through interactive slides, 
                        real-world examples, and hands-on demonstrations.
                    </p>
                    <div class="progress-overview">
                        <div class="progress-item">
                            <span class="progress-number">12</span>
                            <span class="progress-label">Interactive Slides</span>
                        </div>
                        <div class="progress-item">
                            <span class="progress-number">8</span>
                            <span class="progress-label">Practical Demos</span>
                        </div>
                        <div class="progress-item">
                            <span class="progress-number">5</span>
                            <span class="progress-label">Interactive Tools</span>
                        </div>
                    </div>
                </div>
                <div class="header-visual">
                    <div class="learning-preview">
                        <div class="preview-screen">
                            <div class="slide-preview">
                                <div class="preview-content">
                                    <div class="preview-icon">
                                        <i class="fas fa-heartbeat"></i>
                                    </div>
                                    <h3>ECG Signal Processing</h3>
                                    <div class="preview-waveform">
                                        <canvas id="previewCanvas" width="300" height="100"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Slide Navigation -->
    <div class="slide-navigation">
        <div class="container">
            <div class="nav-controls">
                <button type="button" class="nav-btn prev-btn" onclick="previousSlide()" disabled>
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                <div class="slide-counter">
                    <span id="currentSlide">1</span> / <span id="totalSlides">12</span>
                </div>
                <button type="button" class="nav-btn next-btn" onclick="nextSlide()">
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
    </div>

    <!-- Main Slide Container -->
    <div class="slide-container">
        <div class="swiper learning-swiper">
            <div class="swiper-wrapper">
                
                <!-- Slide 1: Introduction to Biosignals -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">01</div>
                            <h2 class="slide-title">Introduction to Biosignals</h2>
                            <p class="slide-subtitle">Understanding the electrical activity of the human body</p>
                        </div>
                        
                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>What are Biosignals?</h3>
                                    <p>Biosignals are electrical, chemical, or mechanical signals that can be measured from biological systems. They provide valuable information about physiological processes and health status.</p>
                                    
                                    <div class="info-cards">
                                        <div class="info-card animate-slide-in-left">
                                            <div class="card-icon">
                                                <i class="fas fa-heartbeat"></i>
                                            </div>
                                            <h4>ECG</h4>
                                            <p>Electrocardiogram - Heart electrical activity</p>
                                        </div>
                                        <div class="info-card animate-slide-in-left" style="animation-delay: 0.2s;">
                                            <div class="card-icon">
                                                <i class="fas fa-brain"></i>
                                            </div>
                                            <h4>EEG</h4>
                                            <p>Electroencephalogram - Brain electrical activity</p>
                                        </div>
                                        <div class="info-card animate-slide-in-left" style="animation-delay: 0.4s;">
                                            <div class="card-icon">
                                                <i class="fas fa-dumbbell"></i>
                                            </div>
                                            <h4>EMG</h4>
                                            <p>Electromyogram - Muscle electrical activity</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="biosignal-demo">
                                            <div class="demo-image">
                                                <img src="https://images.unsplash.com/photo-**********-5c350d0d3c56?w=500&h=300&fit=crop&crop=center" alt="Medical monitoring equipment" class="demo-photo">
                                                <div class="image-overlay">
                                                    <div class="overlay-content">
                                                        <i class="fas fa-play-circle"></i>
                                                        <span>Interactive Demo</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="signal-visualization">
                                                <canvas id="biosignalCanvas" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="slide-footer">
                            <div class="key-points">
                                <h4>Key Learning Points:</h4>
                                <ul>
                                    <li>Biosignals reflect physiological processes</li>
                                    <li>Different types serve different diagnostic purposes</li>
                                    <li>Signal processing is essential for analysis</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 2: ECG Fundamentals -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">02</div>
                            <h2 class="slide-title">ECG Fundamentals</h2>
                            <p class="slide-subtitle">Understanding the electrocardiogram and cardiac electrical activity</p>
                        </div>
                        
                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>The Cardiac Electrical System</h3>
                                    <p>The heart has its own electrical system that controls the heartbeat. This electrical activity can be measured on the skin surface as an ECG.</p>
                                    
                                    <div class="ecg-components">
                                        <div class="component-item" data-component="p-wave">
                                            <div class="component-marker">P</div>
                                            <div class="component-info">
                                                <h4>P Wave</h4>
                                                <p>Atrial depolarization</p>
                                            </div>
                                        </div>
                                        <div class="component-item" data-component="qrs-complex">
                                            <div class="component-marker">QRS</div>
                                            <div class="component-info">
                                                <h4>QRS Complex</h4>
                                                <p>Ventricular depolarization</p>
                                            </div>
                                        </div>
                                        <div class="component-item" data-component="t-wave">
                                            <div class="component-marker">T</div>
                                            <div class="component-info">
                                                <h4>T Wave</h4>
                                                <p>Ventricular repolarization</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="heart-anatomy">
                                            <img src="https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop&crop=center" alt="Heart anatomy" class="anatomy-image">
                                            <div class="anatomy-overlay">
                                                <div class="electrical-path">
                                                    <div class="path-point sa-node" data-tooltip="SA Node - Natural Pacemaker">
                                                        <div class="pulse-animation"></div>
                                                    </div>
                                                    <div class="path-point av-node" data-tooltip="AV Node - Electrical Relay">
                                                        <div class="pulse-animation"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ecg-waveform-demo">
                                            <canvas id="ecgWaveformCanvas" width="400" height="150"></canvas>
                                            <div class="waveform-controls">
                                                <button type="button" class="control-btn" onclick="toggleECGAnimation()">
                                                    <i class="fas fa-play" id="ecgPlayIcon"></i>
                                                </button>
                                                <div class="heart-rate-display">
                                                    <span id="heartRateValue">72</span> BPM
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="slide-footer">
                            <div class="interactive-quiz">
                                <h4>Quick Check:</h4>
                                <div class="quiz-question">
                                    <p>Which component represents ventricular depolarization?</p>
                                    <div class="quiz-options">
                                        <button type="button" class="quiz-option" data-answer="false">P Wave</button>
                                        <button type="button" class="quiz-option" data-answer="true">QRS Complex</button>
                                        <button type="button" class="quiz-option" data-answer="false">T Wave</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide 3: Signal Acquisition -->
                <div class="swiper-slide">
                    <div class="slide-content">
                        <div class="slide-header">
                            <div class="slide-number">03</div>
                            <h2 class="slide-title">Signal Acquisition</h2>
                            <p class="slide-subtitle">How ECG signals are captured and digitized</p>
                        </div>
                        
                        <div class="slide-body">
                            <div class="content-grid">
                                <div class="content-text">
                                    <h3>From Heart to Digital Signal</h3>
                                    <p>ECG acquisition involves several steps to convert the heart's electrical activity into a digital signal that can be processed and analyzed.</p>
                                    
                                    <div class="acquisition-steps">
                                        <div class="step-item">
                                            <div class="step-number">1</div>
                                            <div class="step-content">
                                                <h4>Electrode Placement</h4>
                                                <p>Electrodes detect electrical activity on the skin surface</p>
                                            </div>
                                        </div>
                                        <div class="step-item">
                                            <div class="step-number">2</div>
                                            <div class="step-content">
                                                <h4>Amplification</h4>
                                                <p>Weak electrical signals are amplified for processing</p>
                                            </div>
                                        </div>
                                        <div class="step-item">
                                            <div class="step-number">3</div>
                                            <div class="step-content">
                                                <h4>Filtering</h4>
                                                <p>Unwanted noise and artifacts are removed</p>
                                            </div>
                                        </div>
                                        <div class="step-item">
                                            <div class="step-number">4</div>
                                            <div class="step-content">
                                                <h4>Digitization</h4>
                                                <p>Analog signals are converted to digital format</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="content-visual">
                                    <div class="visual-container">
                                        <div class="acquisition-diagram">
                                            <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=500&h=300&fit=crop&crop=center" alt="ECG monitoring setup" class="setup-image">
                                            <div class="signal-flow">
                                                <div class="flow-step" data-step="1">
                                                    <div class="flow-icon">
                                                        <i class="fas fa-user"></i>
                                                    </div>
                                                    <span>Patient</span>
                                                </div>
                                                <div class="flow-arrow">→</div>
                                                <div class="flow-step" data-step="2">
                                                    <div class="flow-icon">
                                                        <i class="fas fa-plug"></i>
                                                    </div>
                                                    <span>Electrodes</span>
                                                </div>
                                                <div class="flow-arrow">→</div>
                                                <div class="flow-step" data-step="3">
                                                    <div class="flow-icon">
                                                        <i class="fas fa-microchip"></i>
                                                    </div>
                                                    <span>Amplifier</span>
                                                </div>
                                                <div class="flow-arrow">→</div>
                                                <div class="flow-step" data-step="4">
                                                    <div class="flow-icon">
                                                        <i class="fas fa-laptop"></i>
                                                    </div>
                                                    <span>Computer</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="sampling-demo">
                                            <h4>Sampling Rate Demonstration</h4>
                                            <div class="sampling-controls">
                                                <label>Sampling Rate: <span id="samplingRateValue">360</span> Hz</label>
                                                <input type="range" id="samplingRateSlider" min="50" max="1000" value="360" step="10">
                                            </div>
                                            <canvas id="samplingCanvas" width="400" height="120"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            
            <!-- Swiper Pagination -->
            <div class="swiper-pagination"></div>
        </div>
    </div>

    <!-- Slide Thumbnails -->
    <div class="slide-thumbnails">
        <div class="container">
            <h3>Course Outline</h3>
            <div class="thumbnail-grid">
                <div class="thumbnail-item active" data-slide="0">
                    <div class="thumbnail-number">01</div>
                    <div class="thumbnail-content">
                        <h4>Introduction to Biosignals</h4>
                        <p>Basic concepts and types</p>
                    </div>
                </div>
                <div class="thumbnail-item" data-slide="1">
                    <div class="thumbnail-number">02</div>
                    <div class="thumbnail-content">
                        <h4>ECG Fundamentals</h4>
                        <p>Cardiac electrical activity</p>
                    </div>
                </div>
                <div class="thumbnail-item" data-slide="2">
                    <div class="thumbnail-number">03</div>
                    <div class="thumbnail-content">
                        <h4>Signal Acquisition</h4>
                        <p>From analog to digital</p>
                    </div>
                </div>
                <!-- More thumbnails will be added via JavaScript -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading interactive content...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/core/utils.js"></script>
    <script src="js/learning-module.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
