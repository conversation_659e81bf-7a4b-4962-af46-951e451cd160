(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const i of l)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(l){const i={};return l.integrity&&(i.integrity=l.integrity),l.referrerPolicy&&(i.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?i.credentials="include":l.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(l){if(l.ep)return;l.ep=!0;const i=n(l);fetch(l.href,i)}})();var Xs={exports:{}},nl={},Zs={exports:{}},L={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xn=Symbol.for("react.element"),dc=Symbol.for("react.portal"),fc=Symbol.for("react.fragment"),pc=Symbol.for("react.strict_mode"),mc=Symbol.for("react.profiler"),hc=Symbol.for("react.provider"),vc=Symbol.for("react.context"),gc=Symbol.for("react.forward_ref"),yc=Symbol.for("react.suspense"),xc=Symbol.for("react.memo"),wc=Symbol.for("react.lazy"),Fo=Symbol.iterator;function kc(e){return e===null||typeof e!="object"?null:(e=Fo&&e[Fo]||e["@@iterator"],typeof e=="function"?e:null)}var Js={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},qs=Object.assign,bs={};function un(e,t,n){this.props=e,this.context=t,this.refs=bs,this.updater=n||Js}un.prototype.isReactComponent={};un.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};un.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function eu(){}eu.prototype=un.prototype;function $i(e,t,n){this.props=e,this.context=t,this.refs=bs,this.updater=n||Js}var Vi=$i.prototype=new eu;Vi.constructor=$i;qs(Vi,un.prototype);Vi.isPureReactComponent=!0;var Oo=Array.isArray,tu=Object.prototype.hasOwnProperty,Hi={current:null},nu={key:!0,ref:!0,__self:!0,__source:!0};function ru(e,t,n){var r,l={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)tu.call(t,r)&&!nu.hasOwnProperty(r)&&(l[r]=t[r]);var s=arguments.length-2;if(s===1)l.children=n;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(r in s=e.defaultProps,s)l[r]===void 0&&(l[r]=s[r]);return{$$typeof:Xn,type:e,key:i,ref:o,props:l,_owner:Hi.current}}function Sc(e,t){return{$$typeof:Xn,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Wi(e){return typeof e=="object"&&e!==null&&e.$$typeof===Xn}function Nc(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Ao=/\/+/g;function kl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Nc(""+e.key):t.toString(36)}function wr(e,t,n,r,l){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case Xn:case dc:o=!0}}if(o)return o=e,l=l(o),e=r===""?"."+kl(o,0):r,Oo(l)?(n="",e!=null&&(n=e.replace(Ao,"$&/")+"/"),wr(l,t,n,"",function(c){return c})):l!=null&&(Wi(l)&&(l=Sc(l,n+(!l.key||o&&o.key===l.key?"":(""+l.key).replace(Ao,"$&/")+"/")+e)),t.push(l)),1;if(o=0,r=r===""?".":r+":",Oo(e))for(var s=0;s<e.length;s++){i=e[s];var u=r+kl(i,s);o+=wr(i,t,n,u,l)}else if(u=kc(e),typeof u=="function")for(e=u.call(e),s=0;!(i=e.next()).done;)i=i.value,u=r+kl(i,s++),o+=wr(i,t,n,u,l);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function rr(e,t,n){if(e==null)return e;var r=[],l=0;return wr(e,r,"","",function(i){return t.call(n,i,l++)}),r}function jc(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ae={current:null},kr={transition:null},Ec={ReactCurrentDispatcher:ae,ReactCurrentBatchConfig:kr,ReactCurrentOwner:Hi};function lu(){throw Error("act(...) is not supported in production builds of React.")}L.Children={map:rr,forEach:function(e,t,n){rr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return rr(e,function(){t++}),t},toArray:function(e){return rr(e,function(t){return t})||[]},only:function(e){if(!Wi(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};L.Component=un;L.Fragment=fc;L.Profiler=mc;L.PureComponent=$i;L.StrictMode=pc;L.Suspense=yc;L.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ec;L.act=lu;L.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=qs({},e.props),l=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Hi.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)tu.call(t,u)&&!nu.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&s!==void 0?s[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];r.children=s}return{$$typeof:Xn,type:e.type,key:l,ref:i,props:r,_owner:o}};L.createContext=function(e){return e={$$typeof:vc,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:hc,_context:e},e.Consumer=e};L.createElement=ru;L.createFactory=function(e){var t=ru.bind(null,e);return t.type=e,t};L.createRef=function(){return{current:null}};L.forwardRef=function(e){return{$$typeof:gc,render:e}};L.isValidElement=Wi;L.lazy=function(e){return{$$typeof:wc,_payload:{_status:-1,_result:e},_init:jc}};L.memo=function(e,t){return{$$typeof:xc,type:e,compare:t===void 0?null:t}};L.startTransition=function(e){var t=kr.transition;kr.transition={};try{e()}finally{kr.transition=t}};L.unstable_act=lu;L.useCallback=function(e,t){return ae.current.useCallback(e,t)};L.useContext=function(e){return ae.current.useContext(e)};L.useDebugValue=function(){};L.useDeferredValue=function(e){return ae.current.useDeferredValue(e)};L.useEffect=function(e,t){return ae.current.useEffect(e,t)};L.useId=function(){return ae.current.useId()};L.useImperativeHandle=function(e,t,n){return ae.current.useImperativeHandle(e,t,n)};L.useInsertionEffect=function(e,t){return ae.current.useInsertionEffect(e,t)};L.useLayoutEffect=function(e,t){return ae.current.useLayoutEffect(e,t)};L.useMemo=function(e,t){return ae.current.useMemo(e,t)};L.useReducer=function(e,t,n){return ae.current.useReducer(e,t,n)};L.useRef=function(e){return ae.current.useRef(e)};L.useState=function(e){return ae.current.useState(e)};L.useSyncExternalStore=function(e,t,n){return ae.current.useSyncExternalStore(e,t,n)};L.useTransition=function(){return ae.current.useTransition()};L.version="18.3.1";Zs.exports=L;var se=Zs.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cc=se,_c=Symbol.for("react.element"),Pc=Symbol.for("react.fragment"),Mc=Object.prototype.hasOwnProperty,zc=Cc.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Tc={key:!0,ref:!0,__self:!0,__source:!0};function iu(e,t,n){var r,l={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Mc.call(t,r)&&!Tc.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:_c,type:e,key:i,ref:o,props:l,_owner:zc.current}}nl.Fragment=Pc;nl.jsx=iu;nl.jsxs=iu;Xs.exports=nl;var a=Xs.exports,ou={exports:{}},ke={},su={exports:{}},uu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(C,z){var T=C.length;C.push(z);e:for(;0<T;){var B=T-1>>>1,X=C[B];if(0<l(X,z))C[B]=z,C[T]=X,T=B;else break e}}function n(C){return C.length===0?null:C[0]}function r(C){if(C.length===0)return null;var z=C[0],T=C.pop();if(T!==z){C[0]=T;e:for(var B=0,X=C.length,tr=X>>>1;B<tr;){var xt=2*(B+1)-1,wl=C[xt],wt=xt+1,nr=C[wt];if(0>l(wl,T))wt<X&&0>l(nr,wl)?(C[B]=nr,C[wt]=T,B=wt):(C[B]=wl,C[xt]=T,B=xt);else if(wt<X&&0>l(nr,T))C[B]=nr,C[wt]=T,B=wt;else break e}}return z}function l(C,z){var T=C.sortIndex-z.sortIndex;return T!==0?T:C.id-z.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,s=o.now();e.unstable_now=function(){return o.now()-s}}var u=[],c=[],m=1,h=null,p=3,g=!1,y=!1,w=!1,P=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(C){for(var z=n(c);z!==null;){if(z.callback===null)r(c);else if(z.startTime<=C)r(c),z.sortIndex=z.expirationTime,t(u,z);else break;z=n(c)}}function x(C){if(w=!1,v(C),!y)if(n(u)!==null)y=!0,yl(S);else{var z=n(c);z!==null&&xl(x,z.startTime-C)}}function S(C,z){y=!1,w&&(w=!1,f(j),j=-1),g=!0;var T=p;try{for(v(z),h=n(u);h!==null&&(!(h.expirationTime>z)||C&&!q());){var B=h.callback;if(typeof B=="function"){h.callback=null,p=h.priorityLevel;var X=B(h.expirationTime<=z);z=e.unstable_now(),typeof X=="function"?h.callback=X:h===n(u)&&r(u),v(z)}else r(u);h=n(u)}if(h!==null)var tr=!0;else{var xt=n(c);xt!==null&&xl(x,xt.startTime-z),tr=!1}return tr}finally{h=null,p=T,g=!1}}var _=!1,N=null,j=-1,D=5,M=-1;function q(){return!(e.unstable_now()-M<D)}function de(){if(N!==null){var C=e.unstable_now();M=C;var z=!0;try{z=N(!0,C)}finally{z?yt():(_=!1,N=null)}}else _=!1}var yt;if(typeof d=="function")yt=function(){d(de)};else if(typeof MessageChannel<"u"){var er=new MessageChannel,cc=er.port2;er.port1.onmessage=de,yt=function(){cc.postMessage(null)}}else yt=function(){P(de,0)};function yl(C){N=C,_||(_=!0,yt())}function xl(C,z){j=P(function(){C(e.unstable_now())},z)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(C){C.callback=null},e.unstable_continueExecution=function(){y||g||(y=!0,yl(S))},e.unstable_forceFrameRate=function(C){0>C||125<C?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<C?Math.floor(1e3/C):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(C){switch(p){case 1:case 2:case 3:var z=3;break;default:z=p}var T=p;p=z;try{return C()}finally{p=T}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(C,z){switch(C){case 1:case 2:case 3:case 4:case 5:break;default:C=3}var T=p;p=C;try{return z()}finally{p=T}},e.unstable_scheduleCallback=function(C,z,T){var B=e.unstable_now();switch(typeof T=="object"&&T!==null?(T=T.delay,T=typeof T=="number"&&0<T?B+T:B):T=B,C){case 1:var X=-1;break;case 2:X=250;break;case 5:X=**********;break;case 4:X=1e4;break;default:X=5e3}return X=T+X,C={id:m++,callback:z,priorityLevel:C,startTime:T,expirationTime:X,sortIndex:-1},T>B?(C.sortIndex=T,t(c,C),n(u)===null&&C===n(c)&&(w?(f(j),j=-1):w=!0,xl(x,T-B))):(C.sortIndex=X,t(u,C),y||g||(y=!0,yl(S))),C},e.unstable_shouldYield=q,e.unstable_wrapCallback=function(C){var z=p;return function(){var T=p;p=z;try{return C.apply(this,arguments)}finally{p=T}}}})(uu);su.exports=uu;var Lc=su.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rc=se,we=Lc;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var au=new Set,Ln={};function Rt(e,t){en(e,t),en(e+"Capture",t)}function en(e,t){for(Ln[e]=t,e=0;e<t.length;e++)au.add(t[e])}var Ke=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Yl=Object.prototype.hasOwnProperty,Dc=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Uo={},$o={};function Ic(e){return Yl.call($o,e)?!0:Yl.call(Uo,e)?!1:Dc.test(e)?$o[e]=!0:(Uo[e]=!0,!1)}function Fc(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Oc(e,t,n,r){if(t===null||typeof t>"u"||Fc(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ce(e,t,n,r,l,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var te={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){te[e]=new ce(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];te[t]=new ce(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){te[e]=new ce(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){te[e]=new ce(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){te[e]=new ce(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){te[e]=new ce(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){te[e]=new ce(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){te[e]=new ce(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){te[e]=new ce(e,5,!1,e.toLowerCase(),null,!1,!1)});var Bi=/[\-:]([a-z])/g;function Qi(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Bi,Qi);te[t]=new ce(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Bi,Qi);te[t]=new ce(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Bi,Qi);te[t]=new ce(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){te[e]=new ce(e,1,!1,e.toLowerCase(),null,!1,!1)});te.xlinkHref=new ce("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){te[e]=new ce(e,1,!1,e.toLowerCase(),null,!0,!0)});function Gi(e,t,n,r){var l=te.hasOwnProperty(t)?te[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Oc(t,n,l,r)&&(n=null),r||l===null?Ic(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Je=Rc.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,lr=Symbol.for("react.element"),Ft=Symbol.for("react.portal"),Ot=Symbol.for("react.fragment"),Ki=Symbol.for("react.strict_mode"),Xl=Symbol.for("react.profiler"),cu=Symbol.for("react.provider"),du=Symbol.for("react.context"),Yi=Symbol.for("react.forward_ref"),Zl=Symbol.for("react.suspense"),Jl=Symbol.for("react.suspense_list"),Xi=Symbol.for("react.memo"),be=Symbol.for("react.lazy"),fu=Symbol.for("react.offscreen"),Vo=Symbol.iterator;function dn(e){return e===null||typeof e!="object"?null:(e=Vo&&e[Vo]||e["@@iterator"],typeof e=="function"?e:null)}var H=Object.assign,Sl;function xn(e){if(Sl===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Sl=t&&t[1]||""}return`
`+Sl+e}var Nl=!1;function jl(e,t){if(!e||Nl)return"";Nl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var l=c.stack.split(`
`),i=r.stack.split(`
`),o=l.length-1,s=i.length-1;1<=o&&0<=s&&l[o]!==i[s];)s--;for(;1<=o&&0<=s;o--,s--)if(l[o]!==i[s]){if(o!==1||s!==1)do if(o--,s--,0>s||l[o]!==i[s]){var u=`
`+l[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=s);break}}}finally{Nl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?xn(e):""}function Ac(e){switch(e.tag){case 5:return xn(e.type);case 16:return xn("Lazy");case 13:return xn("Suspense");case 19:return xn("SuspenseList");case 0:case 2:case 15:return e=jl(e.type,!1),e;case 11:return e=jl(e.type.render,!1),e;case 1:return e=jl(e.type,!0),e;default:return""}}function ql(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Ot:return"Fragment";case Ft:return"Portal";case Xl:return"Profiler";case Ki:return"StrictMode";case Zl:return"Suspense";case Jl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case du:return(e.displayName||"Context")+".Consumer";case cu:return(e._context.displayName||"Context")+".Provider";case Yi:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Xi:return t=e.displayName||null,t!==null?t:ql(e.type)||"Memo";case be:t=e._payload,e=e._init;try{return ql(e(t))}catch{}}return null}function Uc(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ql(t);case 8:return t===Ki?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function pt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function pu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function $c(e){var t=pu(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ir(e){e._valueTracker||(e._valueTracker=$c(e))}function mu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=pu(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Lr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function bl(e,t){var n=t.checked;return H({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ho(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=pt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function hu(e,t){t=t.checked,t!=null&&Gi(e,"checked",t,!1)}function ei(e,t){hu(e,t);var n=pt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ti(e,t.type,n):t.hasOwnProperty("defaultValue")&&ti(e,t.type,pt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Wo(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ti(e,t,n){(t!=="number"||Lr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var wn=Array.isArray;function Yt(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+pt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function ni(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return H({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Bo(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(wn(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:pt(n)}}function vu(e,t){var n=pt(t.value),r=pt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Qo(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function gu(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ri(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?gu(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var or,yu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(or=or||document.createElement("div"),or.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=or.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Rn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Nn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Vc=["Webkit","ms","Moz","O"];Object.keys(Nn).forEach(function(e){Vc.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Nn[t]=Nn[e]})});function xu(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Nn.hasOwnProperty(e)&&Nn[e]?(""+t).trim():t+"px"}function wu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=xu(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Hc=H({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function li(e,t){if(t){if(Hc[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function ii(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var oi=null;function Zi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var si=null,Xt=null,Zt=null;function Go(e){if(e=qn(e)){if(typeof si!="function")throw Error(k(280));var t=e.stateNode;t&&(t=sl(t),si(e.stateNode,e.type,t))}}function ku(e){Xt?Zt?Zt.push(e):Zt=[e]:Xt=e}function Su(){if(Xt){var e=Xt,t=Zt;if(Zt=Xt=null,Go(e),t)for(e=0;e<t.length;e++)Go(t[e])}}function Nu(e,t){return e(t)}function ju(){}var El=!1;function Eu(e,t,n){if(El)return e(t,n);El=!0;try{return Nu(e,t,n)}finally{El=!1,(Xt!==null||Zt!==null)&&(ju(),Su())}}function Dn(e,t){var n=e.stateNode;if(n===null)return null;var r=sl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var ui=!1;if(Ke)try{var fn={};Object.defineProperty(fn,"passive",{get:function(){ui=!0}}),window.addEventListener("test",fn,fn),window.removeEventListener("test",fn,fn)}catch{ui=!1}function Wc(e,t,n,r,l,i,o,s,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(m){this.onError(m)}}var jn=!1,Rr=null,Dr=!1,ai=null,Bc={onError:function(e){jn=!0,Rr=e}};function Qc(e,t,n,r,l,i,o,s,u){jn=!1,Rr=null,Wc.apply(Bc,arguments)}function Gc(e,t,n,r,l,i,o,s,u){if(Qc.apply(this,arguments),jn){if(jn){var c=Rr;jn=!1,Rr=null}else throw Error(k(198));Dr||(Dr=!0,ai=c)}}function Dt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Cu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ko(e){if(Dt(e)!==e)throw Error(k(188))}function Kc(e){var t=e.alternate;if(!t){if(t=Dt(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var i=l.alternate;if(i===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===i.child){for(i=l.child;i;){if(i===n)return Ko(l),e;if(i===r)return Ko(l),t;i=i.sibling}throw Error(k(188))}if(n.return!==r.return)n=l,r=i;else{for(var o=!1,s=l.child;s;){if(s===n){o=!0,n=l,r=i;break}if(s===r){o=!0,r=l,n=i;break}s=s.sibling}if(!o){for(s=i.child;s;){if(s===n){o=!0,n=i,r=l;break}if(s===r){o=!0,r=i,n=l;break}s=s.sibling}if(!o)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function _u(e){return e=Kc(e),e!==null?Pu(e):null}function Pu(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Pu(e);if(t!==null)return t;e=e.sibling}return null}var Mu=we.unstable_scheduleCallback,Yo=we.unstable_cancelCallback,Yc=we.unstable_shouldYield,Xc=we.unstable_requestPaint,Q=we.unstable_now,Zc=we.unstable_getCurrentPriorityLevel,Ji=we.unstable_ImmediatePriority,zu=we.unstable_UserBlockingPriority,Ir=we.unstable_NormalPriority,Jc=we.unstable_LowPriority,Tu=we.unstable_IdlePriority,rl=null,$e=null;function qc(e){if($e&&typeof $e.onCommitFiberRoot=="function")try{$e.onCommitFiberRoot(rl,e,void 0,(e.current.flags&128)===128)}catch{}}var De=Math.clz32?Math.clz32:td,bc=Math.log,ed=Math.LN2;function td(e){return e>>>=0,e===0?32:31-(bc(e)/ed|0)|0}var sr=64,ur=4194304;function kn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Fr(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var s=o&~l;s!==0?r=kn(s):(i&=o,i!==0&&(r=kn(i)))}else o=n&~l,o!==0?r=kn(o):i!==0&&(r=kn(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,i=t&-t,l>=i||l===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-De(t),l=1<<n,r|=e[n],t&=~l;return r}function nd(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function rd(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-De(i),s=1<<o,u=l[o];u===-1?(!(s&n)||s&r)&&(l[o]=nd(s,t)):u<=t&&(e.expiredLanes|=s),i&=~s}}function ci(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Lu(){var e=sr;return sr<<=1,!(sr&4194240)&&(sr=64),e}function Cl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Zn(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-De(t),e[t]=n}function ld(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-De(n),i=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~i}}function qi(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-De(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var I=0;function Ru(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Du,bi,Iu,Fu,Ou,di=!1,ar=[],it=null,ot=null,st=null,In=new Map,Fn=new Map,tt=[],id="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Xo(e,t){switch(e){case"focusin":case"focusout":it=null;break;case"dragenter":case"dragleave":ot=null;break;case"mouseover":case"mouseout":st=null;break;case"pointerover":case"pointerout":In.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Fn.delete(t.pointerId)}}function pn(e,t,n,r,l,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[l]},t!==null&&(t=qn(t),t!==null&&bi(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function od(e,t,n,r,l){switch(t){case"focusin":return it=pn(it,e,t,n,r,l),!0;case"dragenter":return ot=pn(ot,e,t,n,r,l),!0;case"mouseover":return st=pn(st,e,t,n,r,l),!0;case"pointerover":var i=l.pointerId;return In.set(i,pn(In.get(i)||null,e,t,n,r,l)),!0;case"gotpointercapture":return i=l.pointerId,Fn.set(i,pn(Fn.get(i)||null,e,t,n,r,l)),!0}return!1}function Au(e){var t=Nt(e.target);if(t!==null){var n=Dt(t);if(n!==null){if(t=n.tag,t===13){if(t=Cu(n),t!==null){e.blockedOn=t,Ou(e.priority,function(){Iu(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Sr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=fi(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);oi=r,n.target.dispatchEvent(r),oi=null}else return t=qn(n),t!==null&&bi(t),e.blockedOn=n,!1;t.shift()}return!0}function Zo(e,t,n){Sr(e)&&n.delete(t)}function sd(){di=!1,it!==null&&Sr(it)&&(it=null),ot!==null&&Sr(ot)&&(ot=null),st!==null&&Sr(st)&&(st=null),In.forEach(Zo),Fn.forEach(Zo)}function mn(e,t){e.blockedOn===t&&(e.blockedOn=null,di||(di=!0,we.unstable_scheduleCallback(we.unstable_NormalPriority,sd)))}function On(e){function t(l){return mn(l,e)}if(0<ar.length){mn(ar[0],e);for(var n=1;n<ar.length;n++){var r=ar[n];r.blockedOn===e&&(r.blockedOn=null)}}for(it!==null&&mn(it,e),ot!==null&&mn(ot,e),st!==null&&mn(st,e),In.forEach(t),Fn.forEach(t),n=0;n<tt.length;n++)r=tt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<tt.length&&(n=tt[0],n.blockedOn===null);)Au(n),n.blockedOn===null&&tt.shift()}var Jt=Je.ReactCurrentBatchConfig,Or=!0;function ud(e,t,n,r){var l=I,i=Jt.transition;Jt.transition=null;try{I=1,eo(e,t,n,r)}finally{I=l,Jt.transition=i}}function ad(e,t,n,r){var l=I,i=Jt.transition;Jt.transition=null;try{I=4,eo(e,t,n,r)}finally{I=l,Jt.transition=i}}function eo(e,t,n,r){if(Or){var l=fi(e,t,n,r);if(l===null)Fl(e,t,r,Ar,n),Xo(e,r);else if(od(l,e,t,n,r))r.stopPropagation();else if(Xo(e,r),t&4&&-1<id.indexOf(e)){for(;l!==null;){var i=qn(l);if(i!==null&&Du(i),i=fi(e,t,n,r),i===null&&Fl(e,t,r,Ar,n),i===l)break;l=i}l!==null&&r.stopPropagation()}else Fl(e,t,r,null,n)}}var Ar=null;function fi(e,t,n,r){if(Ar=null,e=Zi(r),e=Nt(e),e!==null)if(t=Dt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Cu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ar=e,null}function Uu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Zc()){case Ji:return 1;case zu:return 4;case Ir:case Jc:return 16;case Tu:return 536870912;default:return 16}default:return 16}}var rt=null,to=null,Nr=null;function $u(){if(Nr)return Nr;var e,t=to,n=t.length,r,l="value"in rt?rt.value:rt.textContent,i=l.length;for(e=0;e<n&&t[e]===l[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===l[i-r];r++);return Nr=l.slice(e,1<r?1-r:void 0)}function jr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function cr(){return!0}function Jo(){return!1}function Se(e){function t(n,r,l,i,o){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var s in e)e.hasOwnProperty(s)&&(n=e[s],this[s]=n?n(i):i[s]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?cr:Jo,this.isPropagationStopped=Jo,this}return H(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=cr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=cr)},persist:function(){},isPersistent:cr}),t}var an={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},no=Se(an),Jn=H({},an,{view:0,detail:0}),cd=Se(Jn),_l,Pl,hn,ll=H({},Jn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ro,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==hn&&(hn&&e.type==="mousemove"?(_l=e.screenX-hn.screenX,Pl=e.screenY-hn.screenY):Pl=_l=0,hn=e),_l)},movementY:function(e){return"movementY"in e?e.movementY:Pl}}),qo=Se(ll),dd=H({},ll,{dataTransfer:0}),fd=Se(dd),pd=H({},Jn,{relatedTarget:0}),Ml=Se(pd),md=H({},an,{animationName:0,elapsedTime:0,pseudoElement:0}),hd=Se(md),vd=H({},an,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),gd=Se(vd),yd=H({},an,{data:0}),bo=Se(yd),xd={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},wd={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kd={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sd(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=kd[e])?!!t[e]:!1}function ro(){return Sd}var Nd=H({},Jn,{key:function(e){if(e.key){var t=xd[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=jr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?wd[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ro,charCode:function(e){return e.type==="keypress"?jr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?jr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),jd=Se(Nd),Ed=H({},ll,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),es=Se(Ed),Cd=H({},Jn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ro}),_d=Se(Cd),Pd=H({},an,{propertyName:0,elapsedTime:0,pseudoElement:0}),Md=Se(Pd),zd=H({},ll,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Td=Se(zd),Ld=[9,13,27,32],lo=Ke&&"CompositionEvent"in window,En=null;Ke&&"documentMode"in document&&(En=document.documentMode);var Rd=Ke&&"TextEvent"in window&&!En,Vu=Ke&&(!lo||En&&8<En&&11>=En),ts=" ",ns=!1;function Hu(e,t){switch(e){case"keyup":return Ld.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Wu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var At=!1;function Dd(e,t){switch(e){case"compositionend":return Wu(t);case"keypress":return t.which!==32?null:(ns=!0,ts);case"textInput":return e=t.data,e===ts&&ns?null:e;default:return null}}function Id(e,t){if(At)return e==="compositionend"||!lo&&Hu(e,t)?(e=$u(),Nr=to=rt=null,At=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Vu&&t.locale!=="ko"?null:t.data;default:return null}}var Fd={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function rs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Fd[e.type]:t==="textarea"}function Bu(e,t,n,r){ku(r),t=Ur(t,"onChange"),0<t.length&&(n=new no("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Cn=null,An=null;function Od(e){ta(e,0)}function il(e){var t=Vt(e);if(mu(t))return e}function Ad(e,t){if(e==="change")return t}var Qu=!1;if(Ke){var zl;if(Ke){var Tl="oninput"in document;if(!Tl){var ls=document.createElement("div");ls.setAttribute("oninput","return;"),Tl=typeof ls.oninput=="function"}zl=Tl}else zl=!1;Qu=zl&&(!document.documentMode||9<document.documentMode)}function is(){Cn&&(Cn.detachEvent("onpropertychange",Gu),An=Cn=null)}function Gu(e){if(e.propertyName==="value"&&il(An)){var t=[];Bu(t,An,e,Zi(e)),Eu(Od,t)}}function Ud(e,t,n){e==="focusin"?(is(),Cn=t,An=n,Cn.attachEvent("onpropertychange",Gu)):e==="focusout"&&is()}function $d(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return il(An)}function Vd(e,t){if(e==="click")return il(t)}function Hd(e,t){if(e==="input"||e==="change")return il(t)}function Wd(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Fe=typeof Object.is=="function"?Object.is:Wd;function Un(e,t){if(Fe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!Yl.call(t,l)||!Fe(e[l],t[l]))return!1}return!0}function os(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ss(e,t){var n=os(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=os(n)}}function Ku(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ku(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Yu(){for(var e=window,t=Lr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Lr(e.document)}return t}function io(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Bd(e){var t=Yu(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ku(n.ownerDocument.documentElement,n)){if(r!==null&&io(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,i=Math.min(r.start,l);r=r.end===void 0?i:Math.min(r.end,l),!e.extend&&i>r&&(l=r,r=i,i=l),l=ss(n,i);var o=ss(n,r);l&&o&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Qd=Ke&&"documentMode"in document&&11>=document.documentMode,Ut=null,pi=null,_n=null,mi=!1;function us(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;mi||Ut==null||Ut!==Lr(r)||(r=Ut,"selectionStart"in r&&io(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),_n&&Un(_n,r)||(_n=r,r=Ur(pi,"onSelect"),0<r.length&&(t=new no("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Ut)))}function dr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var $t={animationend:dr("Animation","AnimationEnd"),animationiteration:dr("Animation","AnimationIteration"),animationstart:dr("Animation","AnimationStart"),transitionend:dr("Transition","TransitionEnd")},Ll={},Xu={};Ke&&(Xu=document.createElement("div").style,"AnimationEvent"in window||(delete $t.animationend.animation,delete $t.animationiteration.animation,delete $t.animationstart.animation),"TransitionEvent"in window||delete $t.transitionend.transition);function ol(e){if(Ll[e])return Ll[e];if(!$t[e])return e;var t=$t[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Xu)return Ll[e]=t[n];return e}var Zu=ol("animationend"),Ju=ol("animationiteration"),qu=ol("animationstart"),bu=ol("transitionend"),ea=new Map,as="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function ht(e,t){ea.set(e,t),Rt(t,[e])}for(var Rl=0;Rl<as.length;Rl++){var Dl=as[Rl],Gd=Dl.toLowerCase(),Kd=Dl[0].toUpperCase()+Dl.slice(1);ht(Gd,"on"+Kd)}ht(Zu,"onAnimationEnd");ht(Ju,"onAnimationIteration");ht(qu,"onAnimationStart");ht("dblclick","onDoubleClick");ht("focusin","onFocus");ht("focusout","onBlur");ht(bu,"onTransitionEnd");en("onMouseEnter",["mouseout","mouseover"]);en("onMouseLeave",["mouseout","mouseover"]);en("onPointerEnter",["pointerout","pointerover"]);en("onPointerLeave",["pointerout","pointerover"]);Rt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Rt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Rt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Rt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Rt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Rt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Sn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Yd=new Set("cancel close invalid load scroll toggle".split(" ").concat(Sn));function cs(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Gc(r,t,void 0,e),e.currentTarget=null}function ta(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var s=r[o],u=s.instance,c=s.currentTarget;if(s=s.listener,u!==i&&l.isPropagationStopped())break e;cs(l,s,c),i=u}else for(o=0;o<r.length;o++){if(s=r[o],u=s.instance,c=s.currentTarget,s=s.listener,u!==i&&l.isPropagationStopped())break e;cs(l,s,c),i=u}}}if(Dr)throw e=ai,Dr=!1,ai=null,e}function O(e,t){var n=t[xi];n===void 0&&(n=t[xi]=new Set);var r=e+"__bubble";n.has(r)||(na(t,e,2,!1),n.add(r))}function Il(e,t,n){var r=0;t&&(r|=4),na(n,e,r,t)}var fr="_reactListening"+Math.random().toString(36).slice(2);function $n(e){if(!e[fr]){e[fr]=!0,au.forEach(function(n){n!=="selectionchange"&&(Yd.has(n)||Il(n,!1,e),Il(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[fr]||(t[fr]=!0,Il("selectionchange",!1,t))}}function na(e,t,n,r){switch(Uu(t)){case 1:var l=ud;break;case 4:l=ad;break;default:l=eo}n=l.bind(null,t,n,e),l=void 0,!ui||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function Fl(e,t,n,r,l){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var s=r.stateNode.containerInfo;if(s===l||s.nodeType===8&&s.parentNode===l)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===l||u.nodeType===8&&u.parentNode===l))return;o=o.return}for(;s!==null;){if(o=Nt(s),o===null)return;if(u=o.tag,u===5||u===6){r=i=o;continue e}s=s.parentNode}}r=r.return}Eu(function(){var c=i,m=Zi(n),h=[];e:{var p=ea.get(e);if(p!==void 0){var g=no,y=e;switch(e){case"keypress":if(jr(n)===0)break e;case"keydown":case"keyup":g=jd;break;case"focusin":y="focus",g=Ml;break;case"focusout":y="blur",g=Ml;break;case"beforeblur":case"afterblur":g=Ml;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=qo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=fd;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=_d;break;case Zu:case Ju:case qu:g=hd;break;case bu:g=Md;break;case"scroll":g=cd;break;case"wheel":g=Td;break;case"copy":case"cut":case"paste":g=gd;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=es}var w=(t&4)!==0,P=!w&&e==="scroll",f=w?p!==null?p+"Capture":null:p;w=[];for(var d=c,v;d!==null;){v=d;var x=v.stateNode;if(v.tag===5&&x!==null&&(v=x,f!==null&&(x=Dn(d,f),x!=null&&w.push(Vn(d,x,v)))),P)break;d=d.return}0<w.length&&(p=new g(p,y,null,n,m),h.push({event:p,listeners:w}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",p&&n!==oi&&(y=n.relatedTarget||n.fromElement)&&(Nt(y)||y[Ye]))break e;if((g||p)&&(p=m.window===m?m:(p=m.ownerDocument)?p.defaultView||p.parentWindow:window,g?(y=n.relatedTarget||n.toElement,g=c,y=y?Nt(y):null,y!==null&&(P=Dt(y),y!==P||y.tag!==5&&y.tag!==6)&&(y=null)):(g=null,y=c),g!==y)){if(w=qo,x="onMouseLeave",f="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(w=es,x="onPointerLeave",f="onPointerEnter",d="pointer"),P=g==null?p:Vt(g),v=y==null?p:Vt(y),p=new w(x,d+"leave",g,n,m),p.target=P,p.relatedTarget=v,x=null,Nt(m)===c&&(w=new w(f,d+"enter",y,n,m),w.target=v,w.relatedTarget=P,x=w),P=x,g&&y)t:{for(w=g,f=y,d=0,v=w;v;v=It(v))d++;for(v=0,x=f;x;x=It(x))v++;for(;0<d-v;)w=It(w),d--;for(;0<v-d;)f=It(f),v--;for(;d--;){if(w===f||f!==null&&w===f.alternate)break t;w=It(w),f=It(f)}w=null}else w=null;g!==null&&ds(h,p,g,w,!1),y!==null&&P!==null&&ds(h,P,y,w,!0)}}e:{if(p=c?Vt(c):window,g=p.nodeName&&p.nodeName.toLowerCase(),g==="select"||g==="input"&&p.type==="file")var S=Ad;else if(rs(p))if(Qu)S=Hd;else{S=$d;var _=Ud}else(g=p.nodeName)&&g.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(S=Vd);if(S&&(S=S(e,c))){Bu(h,S,n,m);break e}_&&_(e,p,c),e==="focusout"&&(_=p._wrapperState)&&_.controlled&&p.type==="number"&&ti(p,"number",p.value)}switch(_=c?Vt(c):window,e){case"focusin":(rs(_)||_.contentEditable==="true")&&(Ut=_,pi=c,_n=null);break;case"focusout":_n=pi=Ut=null;break;case"mousedown":mi=!0;break;case"contextmenu":case"mouseup":case"dragend":mi=!1,us(h,n,m);break;case"selectionchange":if(Qd)break;case"keydown":case"keyup":us(h,n,m)}var N;if(lo)e:{switch(e){case"compositionstart":var j="onCompositionStart";break e;case"compositionend":j="onCompositionEnd";break e;case"compositionupdate":j="onCompositionUpdate";break e}j=void 0}else At?Hu(e,n)&&(j="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(j="onCompositionStart");j&&(Vu&&n.locale!=="ko"&&(At||j!=="onCompositionStart"?j==="onCompositionEnd"&&At&&(N=$u()):(rt=m,to="value"in rt?rt.value:rt.textContent,At=!0)),_=Ur(c,j),0<_.length&&(j=new bo(j,e,null,n,m),h.push({event:j,listeners:_}),N?j.data=N:(N=Wu(n),N!==null&&(j.data=N)))),(N=Rd?Dd(e,n):Id(e,n))&&(c=Ur(c,"onBeforeInput"),0<c.length&&(m=new bo("onBeforeInput","beforeinput",null,n,m),h.push({event:m,listeners:c}),m.data=N))}ta(h,t)})}function Vn(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ur(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,i=l.stateNode;l.tag===5&&i!==null&&(l=i,i=Dn(e,n),i!=null&&r.unshift(Vn(e,i,l)),i=Dn(e,t),i!=null&&r.push(Vn(e,i,l))),e=e.return}return r}function It(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function ds(e,t,n,r,l){for(var i=t._reactName,o=[];n!==null&&n!==r;){var s=n,u=s.alternate,c=s.stateNode;if(u!==null&&u===r)break;s.tag===5&&c!==null&&(s=c,l?(u=Dn(n,i),u!=null&&o.unshift(Vn(n,u,s))):l||(u=Dn(n,i),u!=null&&o.push(Vn(n,u,s)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Xd=/\r\n?/g,Zd=/\u0000|\uFFFD/g;function fs(e){return(typeof e=="string"?e:""+e).replace(Xd,`
`).replace(Zd,"")}function pr(e,t,n){if(t=fs(t),fs(e)!==t&&n)throw Error(k(425))}function $r(){}var hi=null,vi=null;function gi(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var yi=typeof setTimeout=="function"?setTimeout:void 0,Jd=typeof clearTimeout=="function"?clearTimeout:void 0,ps=typeof Promise=="function"?Promise:void 0,qd=typeof queueMicrotask=="function"?queueMicrotask:typeof ps<"u"?function(e){return ps.resolve(null).then(e).catch(bd)}:yi;function bd(e){setTimeout(function(){throw e})}function Ol(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),On(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);On(t)}function ut(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ms(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var cn=Math.random().toString(36).slice(2),Ue="__reactFiber$"+cn,Hn="__reactProps$"+cn,Ye="__reactContainer$"+cn,xi="__reactEvents$"+cn,ef="__reactListeners$"+cn,tf="__reactHandles$"+cn;function Nt(e){var t=e[Ue];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ye]||n[Ue]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ms(e);e!==null;){if(n=e[Ue])return n;e=ms(e)}return t}e=n,n=e.parentNode}return null}function qn(e){return e=e[Ue]||e[Ye],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Vt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function sl(e){return e[Hn]||null}var wi=[],Ht=-1;function vt(e){return{current:e}}function A(e){0>Ht||(e.current=wi[Ht],wi[Ht]=null,Ht--)}function F(e,t){Ht++,wi[Ht]=e.current,e.current=t}var mt={},ie=vt(mt),me=vt(!1),Pt=mt;function tn(e,t){var n=e.type.contextTypes;if(!n)return mt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},i;for(i in n)l[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function he(e){return e=e.childContextTypes,e!=null}function Vr(){A(me),A(ie)}function hs(e,t,n){if(ie.current!==mt)throw Error(k(168));F(ie,t),F(me,n)}function ra(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(k(108,Uc(e)||"Unknown",l));return H({},n,r)}function Hr(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||mt,Pt=ie.current,F(ie,e),F(me,me.current),!0}function vs(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=ra(e,t,Pt),r.__reactInternalMemoizedMergedChildContext=e,A(me),A(ie),F(ie,e)):A(me),F(me,n)}var We=null,ul=!1,Al=!1;function la(e){We===null?We=[e]:We.push(e)}function nf(e){ul=!0,la(e)}function gt(){if(!Al&&We!==null){Al=!0;var e=0,t=I;try{var n=We;for(I=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}We=null,ul=!1}catch(l){throw We!==null&&(We=We.slice(e+1)),Mu(Ji,gt),l}finally{I=t,Al=!1}}return null}var Wt=[],Bt=0,Wr=null,Br=0,Ne=[],je=0,Mt=null,Be=1,Qe="";function kt(e,t){Wt[Bt++]=Br,Wt[Bt++]=Wr,Wr=e,Br=t}function ia(e,t,n){Ne[je++]=Be,Ne[je++]=Qe,Ne[je++]=Mt,Mt=e;var r=Be;e=Qe;var l=32-De(r)-1;r&=~(1<<l),n+=1;var i=32-De(t)+l;if(30<i){var o=l-l%5;i=(r&(1<<o)-1).toString(32),r>>=o,l-=o,Be=1<<32-De(t)+l|n<<l|r,Qe=i+e}else Be=1<<i|n<<l|r,Qe=e}function oo(e){e.return!==null&&(kt(e,1),ia(e,1,0))}function so(e){for(;e===Wr;)Wr=Wt[--Bt],Wt[Bt]=null,Br=Wt[--Bt],Wt[Bt]=null;for(;e===Mt;)Mt=Ne[--je],Ne[je]=null,Qe=Ne[--je],Ne[je]=null,Be=Ne[--je],Ne[je]=null}var xe=null,ye=null,U=!1,Re=null;function oa(e,t){var n=Ee(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function gs(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,xe=e,ye=ut(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,xe=e,ye=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Mt!==null?{id:Be,overflow:Qe}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ee(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,xe=e,ye=null,!0):!1;default:return!1}}function ki(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Si(e){if(U){var t=ye;if(t){var n=t;if(!gs(e,t)){if(ki(e))throw Error(k(418));t=ut(n.nextSibling);var r=xe;t&&gs(e,t)?oa(r,n):(e.flags=e.flags&-4097|2,U=!1,xe=e)}}else{if(ki(e))throw Error(k(418));e.flags=e.flags&-4097|2,U=!1,xe=e}}}function ys(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;xe=e}function mr(e){if(e!==xe)return!1;if(!U)return ys(e),U=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!gi(e.type,e.memoizedProps)),t&&(t=ye)){if(ki(e))throw sa(),Error(k(418));for(;t;)oa(e,t),t=ut(t.nextSibling)}if(ys(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){ye=ut(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}ye=null}}else ye=xe?ut(e.stateNode.nextSibling):null;return!0}function sa(){for(var e=ye;e;)e=ut(e.nextSibling)}function nn(){ye=xe=null,U=!1}function uo(e){Re===null?Re=[e]:Re.push(e)}var rf=Je.ReactCurrentBatchConfig;function vn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var l=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var s=l.refs;o===null?delete s[i]:s[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function hr(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function xs(e){var t=e._init;return t(e._payload)}function ua(e){function t(f,d){if(e){var v=f.deletions;v===null?(f.deletions=[d],f.flags|=16):v.push(d)}}function n(f,d){if(!e)return null;for(;d!==null;)t(f,d),d=d.sibling;return null}function r(f,d){for(f=new Map;d!==null;)d.key!==null?f.set(d.key,d):f.set(d.index,d),d=d.sibling;return f}function l(f,d){return f=ft(f,d),f.index=0,f.sibling=null,f}function i(f,d,v){return f.index=v,e?(v=f.alternate,v!==null?(v=v.index,v<d?(f.flags|=2,d):v):(f.flags|=2,d)):(f.flags|=1048576,d)}function o(f){return e&&f.alternate===null&&(f.flags|=2),f}function s(f,d,v,x){return d===null||d.tag!==6?(d=Ql(v,f.mode,x),d.return=f,d):(d=l(d,v),d.return=f,d)}function u(f,d,v,x){var S=v.type;return S===Ot?m(f,d,v.props.children,x,v.key):d!==null&&(d.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===be&&xs(S)===d.type)?(x=l(d,v.props),x.ref=vn(f,d,v),x.return=f,x):(x=Tr(v.type,v.key,v.props,null,f.mode,x),x.ref=vn(f,d,v),x.return=f,x)}function c(f,d,v,x){return d===null||d.tag!==4||d.stateNode.containerInfo!==v.containerInfo||d.stateNode.implementation!==v.implementation?(d=Gl(v,f.mode,x),d.return=f,d):(d=l(d,v.children||[]),d.return=f,d)}function m(f,d,v,x,S){return d===null||d.tag!==7?(d=_t(v,f.mode,x,S),d.return=f,d):(d=l(d,v),d.return=f,d)}function h(f,d,v){if(typeof d=="string"&&d!==""||typeof d=="number")return d=Ql(""+d,f.mode,v),d.return=f,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case lr:return v=Tr(d.type,d.key,d.props,null,f.mode,v),v.ref=vn(f,null,d),v.return=f,v;case Ft:return d=Gl(d,f.mode,v),d.return=f,d;case be:var x=d._init;return h(f,x(d._payload),v)}if(wn(d)||dn(d))return d=_t(d,f.mode,v,null),d.return=f,d;hr(f,d)}return null}function p(f,d,v,x){var S=d!==null?d.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return S!==null?null:s(f,d,""+v,x);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case lr:return v.key===S?u(f,d,v,x):null;case Ft:return v.key===S?c(f,d,v,x):null;case be:return S=v._init,p(f,d,S(v._payload),x)}if(wn(v)||dn(v))return S!==null?null:m(f,d,v,x,null);hr(f,v)}return null}function g(f,d,v,x,S){if(typeof x=="string"&&x!==""||typeof x=="number")return f=f.get(v)||null,s(d,f,""+x,S);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case lr:return f=f.get(x.key===null?v:x.key)||null,u(d,f,x,S);case Ft:return f=f.get(x.key===null?v:x.key)||null,c(d,f,x,S);case be:var _=x._init;return g(f,d,v,_(x._payload),S)}if(wn(x)||dn(x))return f=f.get(v)||null,m(d,f,x,S,null);hr(d,x)}return null}function y(f,d,v,x){for(var S=null,_=null,N=d,j=d=0,D=null;N!==null&&j<v.length;j++){N.index>j?(D=N,N=null):D=N.sibling;var M=p(f,N,v[j],x);if(M===null){N===null&&(N=D);break}e&&N&&M.alternate===null&&t(f,N),d=i(M,d,j),_===null?S=M:_.sibling=M,_=M,N=D}if(j===v.length)return n(f,N),U&&kt(f,j),S;if(N===null){for(;j<v.length;j++)N=h(f,v[j],x),N!==null&&(d=i(N,d,j),_===null?S=N:_.sibling=N,_=N);return U&&kt(f,j),S}for(N=r(f,N);j<v.length;j++)D=g(N,f,j,v[j],x),D!==null&&(e&&D.alternate!==null&&N.delete(D.key===null?j:D.key),d=i(D,d,j),_===null?S=D:_.sibling=D,_=D);return e&&N.forEach(function(q){return t(f,q)}),U&&kt(f,j),S}function w(f,d,v,x){var S=dn(v);if(typeof S!="function")throw Error(k(150));if(v=S.call(v),v==null)throw Error(k(151));for(var _=S=null,N=d,j=d=0,D=null,M=v.next();N!==null&&!M.done;j++,M=v.next()){N.index>j?(D=N,N=null):D=N.sibling;var q=p(f,N,M.value,x);if(q===null){N===null&&(N=D);break}e&&N&&q.alternate===null&&t(f,N),d=i(q,d,j),_===null?S=q:_.sibling=q,_=q,N=D}if(M.done)return n(f,N),U&&kt(f,j),S;if(N===null){for(;!M.done;j++,M=v.next())M=h(f,M.value,x),M!==null&&(d=i(M,d,j),_===null?S=M:_.sibling=M,_=M);return U&&kt(f,j),S}for(N=r(f,N);!M.done;j++,M=v.next())M=g(N,f,j,M.value,x),M!==null&&(e&&M.alternate!==null&&N.delete(M.key===null?j:M.key),d=i(M,d,j),_===null?S=M:_.sibling=M,_=M);return e&&N.forEach(function(de){return t(f,de)}),U&&kt(f,j),S}function P(f,d,v,x){if(typeof v=="object"&&v!==null&&v.type===Ot&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case lr:e:{for(var S=v.key,_=d;_!==null;){if(_.key===S){if(S=v.type,S===Ot){if(_.tag===7){n(f,_.sibling),d=l(_,v.props.children),d.return=f,f=d;break e}}else if(_.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===be&&xs(S)===_.type){n(f,_.sibling),d=l(_,v.props),d.ref=vn(f,_,v),d.return=f,f=d;break e}n(f,_);break}else t(f,_);_=_.sibling}v.type===Ot?(d=_t(v.props.children,f.mode,x,v.key),d.return=f,f=d):(x=Tr(v.type,v.key,v.props,null,f.mode,x),x.ref=vn(f,d,v),x.return=f,f=x)}return o(f);case Ft:e:{for(_=v.key;d!==null;){if(d.key===_)if(d.tag===4&&d.stateNode.containerInfo===v.containerInfo&&d.stateNode.implementation===v.implementation){n(f,d.sibling),d=l(d,v.children||[]),d.return=f,f=d;break e}else{n(f,d);break}else t(f,d);d=d.sibling}d=Gl(v,f.mode,x),d.return=f,f=d}return o(f);case be:return _=v._init,P(f,d,_(v._payload),x)}if(wn(v))return y(f,d,v,x);if(dn(v))return w(f,d,v,x);hr(f,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,d!==null&&d.tag===6?(n(f,d.sibling),d=l(d,v),d.return=f,f=d):(n(f,d),d=Ql(v,f.mode,x),d.return=f,f=d),o(f)):n(f,d)}return P}var rn=ua(!0),aa=ua(!1),Qr=vt(null),Gr=null,Qt=null,ao=null;function co(){ao=Qt=Gr=null}function fo(e){var t=Qr.current;A(Qr),e._currentValue=t}function Ni(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function qt(e,t){Gr=e,ao=Qt=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(pe=!0),e.firstContext=null)}function _e(e){var t=e._currentValue;if(ao!==e)if(e={context:e,memoizedValue:t,next:null},Qt===null){if(Gr===null)throw Error(k(308));Qt=e,Gr.dependencies={lanes:0,firstContext:e}}else Qt=Qt.next=e;return t}var jt=null;function po(e){jt===null?jt=[e]:jt.push(e)}function ca(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,po(t)):(n.next=l.next,l.next=n),t.interleaved=n,Xe(e,r)}function Xe(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var et=!1;function mo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function da(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ge(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function at(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,R&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,Xe(e,n)}return l=r.interleaved,l===null?(t.next=t,po(r)):(t.next=l.next,l.next=t),r.interleaved=t,Xe(e,n)}function Er(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,qi(e,n)}}function ws(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?l=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?l=i=t:i=i.next=t}else l=i=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Kr(e,t,n,r){var l=e.updateQueue;et=!1;var i=l.firstBaseUpdate,o=l.lastBaseUpdate,s=l.shared.pending;if(s!==null){l.shared.pending=null;var u=s,c=u.next;u.next=null,o===null?i=c:o.next=c,o=u;var m=e.alternate;m!==null&&(m=m.updateQueue,s=m.lastBaseUpdate,s!==o&&(s===null?m.firstBaseUpdate=c:s.next=c,m.lastBaseUpdate=u))}if(i!==null){var h=l.baseState;o=0,m=c=u=null,s=i;do{var p=s.lane,g=s.eventTime;if((r&p)===p){m!==null&&(m=m.next={eventTime:g,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var y=e,w=s;switch(p=t,g=n,w.tag){case 1:if(y=w.payload,typeof y=="function"){h=y.call(g,h,p);break e}h=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=w.payload,p=typeof y=="function"?y.call(g,h,p):y,p==null)break e;h=H({},h,p);break e;case 2:et=!0}}s.callback!==null&&s.lane!==0&&(e.flags|=64,p=l.effects,p===null?l.effects=[s]:p.push(s))}else g={eventTime:g,lane:p,tag:s.tag,payload:s.payload,callback:s.callback,next:null},m===null?(c=m=g,u=h):m=m.next=g,o|=p;if(s=s.next,s===null){if(s=l.shared.pending,s===null)break;p=s,s=p.next,p.next=null,l.lastBaseUpdate=p,l.shared.pending=null}}while(!0);if(m===null&&(u=h),l.baseState=u,l.firstBaseUpdate=c,l.lastBaseUpdate=m,t=l.shared.interleaved,t!==null){l=t;do o|=l.lane,l=l.next;while(l!==t)}else i===null&&(l.shared.lanes=0);Tt|=o,e.lanes=o,e.memoizedState=h}}function ks(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(k(191,l));l.call(r)}}}var bn={},Ve=vt(bn),Wn=vt(bn),Bn=vt(bn);function Et(e){if(e===bn)throw Error(k(174));return e}function ho(e,t){switch(F(Bn,t),F(Wn,e),F(Ve,bn),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ri(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ri(t,e)}A(Ve),F(Ve,t)}function ln(){A(Ve),A(Wn),A(Bn)}function fa(e){Et(Bn.current);var t=Et(Ve.current),n=ri(t,e.type);t!==n&&(F(Wn,e),F(Ve,n))}function vo(e){Wn.current===e&&(A(Ve),A(Wn))}var $=vt(0);function Yr(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Ul=[];function go(){for(var e=0;e<Ul.length;e++)Ul[e]._workInProgressVersionPrimary=null;Ul.length=0}var Cr=Je.ReactCurrentDispatcher,$l=Je.ReactCurrentBatchConfig,zt=0,V=null,K=null,Z=null,Xr=!1,Pn=!1,Qn=0,lf=0;function ne(){throw Error(k(321))}function yo(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Fe(e[n],t[n]))return!1;return!0}function xo(e,t,n,r,l,i){if(zt=i,V=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Cr.current=e===null||e.memoizedState===null?af:cf,e=n(r,l),Pn){i=0;do{if(Pn=!1,Qn=0,25<=i)throw Error(k(301));i+=1,Z=K=null,t.updateQueue=null,Cr.current=df,e=n(r,l)}while(Pn)}if(Cr.current=Zr,t=K!==null&&K.next!==null,zt=0,Z=K=V=null,Xr=!1,t)throw Error(k(300));return e}function wo(){var e=Qn!==0;return Qn=0,e}function Ae(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Z===null?V.memoizedState=Z=e:Z=Z.next=e,Z}function Pe(){if(K===null){var e=V.alternate;e=e!==null?e.memoizedState:null}else e=K.next;var t=Z===null?V.memoizedState:Z.next;if(t!==null)Z=t,K=e;else{if(e===null)throw Error(k(310));K=e,e={memoizedState:K.memoizedState,baseState:K.baseState,baseQueue:K.baseQueue,queue:K.queue,next:null},Z===null?V.memoizedState=Z=e:Z=Z.next=e}return Z}function Gn(e,t){return typeof t=="function"?t(e):t}function Vl(e){var t=Pe(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=K,l=r.baseQueue,i=n.pending;if(i!==null){if(l!==null){var o=l.next;l.next=i.next,i.next=o}r.baseQueue=l=i,n.pending=null}if(l!==null){i=l.next,r=r.baseState;var s=o=null,u=null,c=i;do{var m=c.lane;if((zt&m)===m)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var h={lane:m,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(s=u=h,o=r):u=u.next=h,V.lanes|=m,Tt|=m}c=c.next}while(c!==null&&c!==i);u===null?o=r:u.next=s,Fe(r,t.memoizedState)||(pe=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do i=l.lane,V.lanes|=i,Tt|=i,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Hl(e){var t=Pe(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,i=t.memoizedState;if(l!==null){n.pending=null;var o=l=l.next;do i=e(i,o.action),o=o.next;while(o!==l);Fe(i,t.memoizedState)||(pe=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function pa(){}function ma(e,t){var n=V,r=Pe(),l=t(),i=!Fe(r.memoizedState,l);if(i&&(r.memoizedState=l,pe=!0),r=r.queue,ko(ga.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Z!==null&&Z.memoizedState.tag&1){if(n.flags|=2048,Kn(9,va.bind(null,n,r,l,t),void 0,null),J===null)throw Error(k(349));zt&30||ha(n,t,l)}return l}function ha(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=V.updateQueue,t===null?(t={lastEffect:null,stores:null},V.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function va(e,t,n,r){t.value=n,t.getSnapshot=r,ya(t)&&xa(e)}function ga(e,t,n){return n(function(){ya(t)&&xa(e)})}function ya(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Fe(e,n)}catch{return!0}}function xa(e){var t=Xe(e,1);t!==null&&Ie(t,e,1,-1)}function Ss(e){var t=Ae();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Gn,lastRenderedState:e},t.queue=e,e=e.dispatch=uf.bind(null,V,e),[t.memoizedState,e]}function Kn(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=V.updateQueue,t===null?(t={lastEffect:null,stores:null},V.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function wa(){return Pe().memoizedState}function _r(e,t,n,r){var l=Ae();V.flags|=e,l.memoizedState=Kn(1|t,n,void 0,r===void 0?null:r)}function al(e,t,n,r){var l=Pe();r=r===void 0?null:r;var i=void 0;if(K!==null){var o=K.memoizedState;if(i=o.destroy,r!==null&&yo(r,o.deps)){l.memoizedState=Kn(t,n,i,r);return}}V.flags|=e,l.memoizedState=Kn(1|t,n,i,r)}function Ns(e,t){return _r(8390656,8,e,t)}function ko(e,t){return al(2048,8,e,t)}function ka(e,t){return al(4,2,e,t)}function Sa(e,t){return al(4,4,e,t)}function Na(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function ja(e,t,n){return n=n!=null?n.concat([e]):null,al(4,4,Na.bind(null,t,e),n)}function So(){}function Ea(e,t){var n=Pe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&yo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ca(e,t){var n=Pe();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&yo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function _a(e,t,n){return zt&21?(Fe(n,t)||(n=Lu(),V.lanes|=n,Tt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,pe=!0),e.memoizedState=n)}function of(e,t){var n=I;I=n!==0&&4>n?n:4,e(!0);var r=$l.transition;$l.transition={};try{e(!1),t()}finally{I=n,$l.transition=r}}function Pa(){return Pe().memoizedState}function sf(e,t,n){var r=dt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ma(e))za(t,n);else if(n=ca(e,t,n,r),n!==null){var l=ue();Ie(n,e,r,l),Ta(n,t,r)}}function uf(e,t,n){var r=dt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ma(e))za(t,l);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,s=i(o,n);if(l.hasEagerState=!0,l.eagerState=s,Fe(s,o)){var u=t.interleaved;u===null?(l.next=l,po(t)):(l.next=u.next,u.next=l),t.interleaved=l;return}}catch{}finally{}n=ca(e,t,l,r),n!==null&&(l=ue(),Ie(n,e,r,l),Ta(n,t,r))}}function Ma(e){var t=e.alternate;return e===V||t!==null&&t===V}function za(e,t){Pn=Xr=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ta(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,qi(e,n)}}var Zr={readContext:_e,useCallback:ne,useContext:ne,useEffect:ne,useImperativeHandle:ne,useInsertionEffect:ne,useLayoutEffect:ne,useMemo:ne,useReducer:ne,useRef:ne,useState:ne,useDebugValue:ne,useDeferredValue:ne,useTransition:ne,useMutableSource:ne,useSyncExternalStore:ne,useId:ne,unstable_isNewReconciler:!1},af={readContext:_e,useCallback:function(e,t){return Ae().memoizedState=[e,t===void 0?null:t],e},useContext:_e,useEffect:Ns,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,_r(4194308,4,Na.bind(null,t,e),n)},useLayoutEffect:function(e,t){return _r(4194308,4,e,t)},useInsertionEffect:function(e,t){return _r(4,2,e,t)},useMemo:function(e,t){var n=Ae();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ae();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=sf.bind(null,V,e),[r.memoizedState,e]},useRef:function(e){var t=Ae();return e={current:e},t.memoizedState=e},useState:Ss,useDebugValue:So,useDeferredValue:function(e){return Ae().memoizedState=e},useTransition:function(){var e=Ss(!1),t=e[0];return e=of.bind(null,e[1]),Ae().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=V,l=Ae();if(U){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),J===null)throw Error(k(349));zt&30||ha(r,t,n)}l.memoizedState=n;var i={value:n,getSnapshot:t};return l.queue=i,Ns(ga.bind(null,r,i,e),[e]),r.flags|=2048,Kn(9,va.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Ae(),t=J.identifierPrefix;if(U){var n=Qe,r=Be;n=(r&~(1<<32-De(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Qn++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=lf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},cf={readContext:_e,useCallback:Ea,useContext:_e,useEffect:ko,useImperativeHandle:ja,useInsertionEffect:ka,useLayoutEffect:Sa,useMemo:Ca,useReducer:Vl,useRef:wa,useState:function(){return Vl(Gn)},useDebugValue:So,useDeferredValue:function(e){var t=Pe();return _a(t,K.memoizedState,e)},useTransition:function(){var e=Vl(Gn)[0],t=Pe().memoizedState;return[e,t]},useMutableSource:pa,useSyncExternalStore:ma,useId:Pa,unstable_isNewReconciler:!1},df={readContext:_e,useCallback:Ea,useContext:_e,useEffect:ko,useImperativeHandle:ja,useInsertionEffect:ka,useLayoutEffect:Sa,useMemo:Ca,useReducer:Hl,useRef:wa,useState:function(){return Hl(Gn)},useDebugValue:So,useDeferredValue:function(e){var t=Pe();return K===null?t.memoizedState=e:_a(t,K.memoizedState,e)},useTransition:function(){var e=Hl(Gn)[0],t=Pe().memoizedState;return[e,t]},useMutableSource:pa,useSyncExternalStore:ma,useId:Pa,unstable_isNewReconciler:!1};function Te(e,t){if(e&&e.defaultProps){t=H({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ji(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:H({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var cl={isMounted:function(e){return(e=e._reactInternals)?Dt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ue(),l=dt(e),i=Ge(r,l);i.payload=t,n!=null&&(i.callback=n),t=at(e,i,l),t!==null&&(Ie(t,e,l,r),Er(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ue(),l=dt(e),i=Ge(r,l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=at(e,i,l),t!==null&&(Ie(t,e,l,r),Er(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ue(),r=dt(e),l=Ge(n,r);l.tag=2,t!=null&&(l.callback=t),t=at(e,l,r),t!==null&&(Ie(t,e,r,n),Er(t,e,r))}};function js(e,t,n,r,l,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!Un(n,r)||!Un(l,i):!0}function La(e,t,n){var r=!1,l=mt,i=t.contextType;return typeof i=="object"&&i!==null?i=_e(i):(l=he(t)?Pt:ie.current,r=t.contextTypes,i=(r=r!=null)?tn(e,l):mt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=cl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=i),t}function Es(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&cl.enqueueReplaceState(t,t.state,null)}function Ei(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},mo(e);var i=t.contextType;typeof i=="object"&&i!==null?l.context=_e(i):(i=he(t)?Pt:ie.current,l.context=tn(e,i)),l.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(ji(e,t,i,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&cl.enqueueReplaceState(l,l.state,null),Kr(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function on(e,t){try{var n="",r=t;do n+=Ac(r),r=r.return;while(r);var l=n}catch(i){l=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:l,digest:null}}function Wl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Ci(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var ff=typeof WeakMap=="function"?WeakMap:Map;function Ra(e,t,n){n=Ge(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){qr||(qr=!0,Fi=r),Ci(e,t)},n}function Da(e,t,n){n=Ge(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){Ci(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Ci(e,t),typeof r!="function"&&(ct===null?ct=new Set([this]):ct.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Cs(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new ff;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=Cf.bind(null,e,t,n),t.then(e,e))}function _s(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ps(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ge(-1,1),t.tag=2,at(n,t,1))),n.lanes|=1),e)}var pf=Je.ReactCurrentOwner,pe=!1;function oe(e,t,n,r){t.child=e===null?aa(t,null,n,r):rn(t,e.child,n,r)}function Ms(e,t,n,r,l){n=n.render;var i=t.ref;return qt(t,l),r=xo(e,t,n,r,i,l),n=wo(),e!==null&&!pe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Ze(e,t,l)):(U&&n&&oo(t),t.flags|=1,oe(e,t,r,l),t.child)}function zs(e,t,n,r,l){if(e===null){var i=n.type;return typeof i=="function"&&!zo(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Ia(e,t,i,r,l)):(e=Tr(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&l)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:Un,n(o,r)&&e.ref===t.ref)return Ze(e,t,l)}return t.flags|=1,e=ft(i,r),e.ref=t.ref,e.return=t,t.child=e}function Ia(e,t,n,r,l){if(e!==null){var i=e.memoizedProps;if(Un(i,r)&&e.ref===t.ref)if(pe=!1,t.pendingProps=r=i,(e.lanes&l)!==0)e.flags&131072&&(pe=!0);else return t.lanes=e.lanes,Ze(e,t,l)}return _i(e,t,n,r,l)}function Fa(e,t,n){var r=t.pendingProps,l=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},F(Kt,ge),ge|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,F(Kt,ge),ge|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,F(Kt,ge),ge|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,F(Kt,ge),ge|=r;return oe(e,t,l,n),t.child}function Oa(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function _i(e,t,n,r,l){var i=he(n)?Pt:ie.current;return i=tn(t,i),qt(t,l),n=xo(e,t,n,r,i,l),r=wo(),e!==null&&!pe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Ze(e,t,l)):(U&&r&&oo(t),t.flags|=1,oe(e,t,n,l),t.child)}function Ts(e,t,n,r,l){if(he(n)){var i=!0;Hr(t)}else i=!1;if(qt(t,l),t.stateNode===null)Pr(e,t),La(t,n,r),Ei(t,n,r,l),r=!0;else if(e===null){var o=t.stateNode,s=t.memoizedProps;o.props=s;var u=o.context,c=n.contextType;typeof c=="object"&&c!==null?c=_e(c):(c=he(n)?Pt:ie.current,c=tn(t,c));var m=n.getDerivedStateFromProps,h=typeof m=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(s!==r||u!==c)&&Es(t,o,r,c),et=!1;var p=t.memoizedState;o.state=p,Kr(t,r,o,l),u=t.memoizedState,s!==r||p!==u||me.current||et?(typeof m=="function"&&(ji(t,n,m,r),u=t.memoizedState),(s=et||js(t,n,s,r,p,u,c))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=c,r=s):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,da(e,t),s=t.memoizedProps,c=t.type===t.elementType?s:Te(t.type,s),o.props=c,h=t.pendingProps,p=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=_e(u):(u=he(n)?Pt:ie.current,u=tn(t,u));var g=n.getDerivedStateFromProps;(m=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(s!==h||p!==u)&&Es(t,o,r,u),et=!1,p=t.memoizedState,o.state=p,Kr(t,r,o,l);var y=t.memoizedState;s!==h||p!==y||me.current||et?(typeof g=="function"&&(ji(t,n,g,r),y=t.memoizedState),(c=et||js(t,n,c,r,p,y,u)||!1)?(m||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,y,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,y,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=y),o.props=r,o.state=y,o.context=u,r=c):(typeof o.componentDidUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||s===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return Pi(e,t,n,r,i,l)}function Pi(e,t,n,r,l,i){Oa(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return l&&vs(t,n,!1),Ze(e,t,i);r=t.stateNode,pf.current=t;var s=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=rn(t,e.child,null,i),t.child=rn(t,null,s,i)):oe(e,t,s,i),t.memoizedState=r.state,l&&vs(t,n,!0),t.child}function Aa(e){var t=e.stateNode;t.pendingContext?hs(e,t.pendingContext,t.pendingContext!==t.context):t.context&&hs(e,t.context,!1),ho(e,t.containerInfo)}function Ls(e,t,n,r,l){return nn(),uo(l),t.flags|=256,oe(e,t,n,r),t.child}var Mi={dehydrated:null,treeContext:null,retryLane:0};function zi(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ua(e,t,n){var r=t.pendingProps,l=$.current,i=!1,o=(t.flags&128)!==0,s;if((s=o)||(s=e!==null&&e.memoizedState===null?!1:(l&2)!==0),s?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),F($,l&1),e===null)return Si(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=pl(o,r,0,null),e=_t(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=zi(n),t.memoizedState=Mi,e):No(t,o));if(l=e.memoizedState,l!==null&&(s=l.dehydrated,s!==null))return mf(e,t,o,r,s,l,n);if(i){i=r.fallback,o=t.mode,l=e.child,s=l.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=ft(l,u),r.subtreeFlags=l.subtreeFlags&14680064),s!==null?i=ft(s,i):(i=_t(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?zi(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=Mi,r}return i=e.child,e=i.sibling,r=ft(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function No(e,t){return t=pl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function vr(e,t,n,r){return r!==null&&uo(r),rn(t,e.child,null,n),e=No(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function mf(e,t,n,r,l,i,o){if(n)return t.flags&256?(t.flags&=-257,r=Wl(Error(k(422))),vr(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,l=t.mode,r=pl({mode:"visible",children:r.children},l,0,null),i=_t(i,l,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&rn(t,e.child,null,o),t.child.memoizedState=zi(o),t.memoizedState=Mi,i);if(!(t.mode&1))return vr(e,t,o,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var s=r.dgst;return r=s,i=Error(k(419)),r=Wl(i,r,void 0),vr(e,t,o,r)}if(s=(o&e.childLanes)!==0,pe||s){if(r=J,r!==null){switch(o&-o){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|o)?0:l,l!==0&&l!==i.retryLane&&(i.retryLane=l,Xe(e,l),Ie(r,e,l,-1))}return Mo(),r=Wl(Error(k(421))),vr(e,t,o,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=_f.bind(null,e),l._reactRetry=t,null):(e=i.treeContext,ye=ut(l.nextSibling),xe=t,U=!0,Re=null,e!==null&&(Ne[je++]=Be,Ne[je++]=Qe,Ne[je++]=Mt,Be=e.id,Qe=e.overflow,Mt=t),t=No(t,r.children),t.flags|=4096,t)}function Rs(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ni(e.return,t,n)}function Bl(e,t,n,r,l){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=l)}function $a(e,t,n){var r=t.pendingProps,l=r.revealOrder,i=r.tail;if(oe(e,t,r.children,n),r=$.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Rs(e,n,t);else if(e.tag===19)Rs(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(F($,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&Yr(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),Bl(t,!1,l,n,i);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Yr(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}Bl(t,!0,n,null,i);break;case"together":Bl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Pr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ze(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Tt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=ft(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=ft(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function hf(e,t,n){switch(t.tag){case 3:Aa(t),nn();break;case 5:fa(t);break;case 1:he(t.type)&&Hr(t);break;case 4:ho(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;F(Qr,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(F($,$.current&1),t.flags|=128,null):n&t.child.childLanes?Ua(e,t,n):(F($,$.current&1),e=Ze(e,t,n),e!==null?e.sibling:null);F($,$.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return $a(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),F($,$.current),r)break;return null;case 22:case 23:return t.lanes=0,Fa(e,t,n)}return Ze(e,t,n)}var Va,Ti,Ha,Wa;Va=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ti=function(){};Ha=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,Et(Ve.current);var i=null;switch(n){case"input":l=bl(e,l),r=bl(e,r),i=[];break;case"select":l=H({},l,{value:void 0}),r=H({},r,{value:void 0}),i=[];break;case"textarea":l=ni(e,l),r=ni(e,r),i=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=$r)}li(n,r);var o;n=null;for(c in l)if(!r.hasOwnProperty(c)&&l.hasOwnProperty(c)&&l[c]!=null)if(c==="style"){var s=l[c];for(o in s)s.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Ln.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(s=l!=null?l[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(u!=null||s!=null))if(c==="style")if(s){for(o in s)!s.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&s[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(i||(i=[]),i.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,s=s?s.__html:void 0,u!=null&&s!==u&&(i=i||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Ln.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&O("scroll",e),i||s===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};Wa=function(e,t,n,r){n!==r&&(t.flags|=4)};function gn(e,t){if(!U)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function re(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function vf(e,t,n){var r=t.pendingProps;switch(so(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return re(t),null;case 1:return he(t.type)&&Vr(),re(t),null;case 3:return r=t.stateNode,ln(),A(me),A(ie),go(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(mr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Re!==null&&(Ui(Re),Re=null))),Ti(e,t),re(t),null;case 5:vo(t);var l=Et(Bn.current);if(n=t.type,e!==null&&t.stateNode!=null)Ha(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return re(t),null}if(e=Et(Ve.current),mr(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Ue]=t,r[Hn]=i,e=(t.mode&1)!==0,n){case"dialog":O("cancel",r),O("close",r);break;case"iframe":case"object":case"embed":O("load",r);break;case"video":case"audio":for(l=0;l<Sn.length;l++)O(Sn[l],r);break;case"source":O("error",r);break;case"img":case"image":case"link":O("error",r),O("load",r);break;case"details":O("toggle",r);break;case"input":Ho(r,i),O("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},O("invalid",r);break;case"textarea":Bo(r,i),O("invalid",r)}li(n,i),l=null;for(var o in i)if(i.hasOwnProperty(o)){var s=i[o];o==="children"?typeof s=="string"?r.textContent!==s&&(i.suppressHydrationWarning!==!0&&pr(r.textContent,s,e),l=["children",s]):typeof s=="number"&&r.textContent!==""+s&&(i.suppressHydrationWarning!==!0&&pr(r.textContent,s,e),l=["children",""+s]):Ln.hasOwnProperty(o)&&s!=null&&o==="onScroll"&&O("scroll",r)}switch(n){case"input":ir(r),Wo(r,i,!0);break;case"textarea":ir(r),Qo(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=$r)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=gu(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Ue]=t,e[Hn]=r,Va(e,t,!1,!1),t.stateNode=e;e:{switch(o=ii(n,r),n){case"dialog":O("cancel",e),O("close",e),l=r;break;case"iframe":case"object":case"embed":O("load",e),l=r;break;case"video":case"audio":for(l=0;l<Sn.length;l++)O(Sn[l],e);l=r;break;case"source":O("error",e),l=r;break;case"img":case"image":case"link":O("error",e),O("load",e),l=r;break;case"details":O("toggle",e),l=r;break;case"input":Ho(e,r),l=bl(e,r),O("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=H({},r,{value:void 0}),O("invalid",e);break;case"textarea":Bo(e,r),l=ni(e,r),O("invalid",e);break;default:l=r}li(n,l),s=l;for(i in s)if(s.hasOwnProperty(i)){var u=s[i];i==="style"?wu(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&yu(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Rn(e,u):typeof u=="number"&&Rn(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Ln.hasOwnProperty(i)?u!=null&&i==="onScroll"&&O("scroll",e):u!=null&&Gi(e,i,u,o))}switch(n){case"input":ir(e),Wo(e,r,!1);break;case"textarea":ir(e),Qo(e);break;case"option":r.value!=null&&e.setAttribute("value",""+pt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Yt(e,!!r.multiple,i,!1):r.defaultValue!=null&&Yt(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=$r)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return re(t),null;case 6:if(e&&t.stateNode!=null)Wa(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=Et(Bn.current),Et(Ve.current),mr(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ue]=t,(i=r.nodeValue!==n)&&(e=xe,e!==null))switch(e.tag){case 3:pr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&pr(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ue]=t,t.stateNode=r}return re(t),null;case 13:if(A($),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(U&&ye!==null&&t.mode&1&&!(t.flags&128))sa(),nn(),t.flags|=98560,i=!1;else if(i=mr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(k(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(k(317));i[Ue]=t}else nn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;re(t),i=!1}else Re!==null&&(Ui(Re),Re=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||$.current&1?Y===0&&(Y=3):Mo())),t.updateQueue!==null&&(t.flags|=4),re(t),null);case 4:return ln(),Ti(e,t),e===null&&$n(t.stateNode.containerInfo),re(t),null;case 10:return fo(t.type._context),re(t),null;case 17:return he(t.type)&&Vr(),re(t),null;case 19:if(A($),i=t.memoizedState,i===null)return re(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)gn(i,!1);else{if(Y!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Yr(e),o!==null){for(t.flags|=128,gn(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return F($,$.current&1|2),t.child}e=e.sibling}i.tail!==null&&Q()>sn&&(t.flags|=128,r=!0,gn(i,!1),t.lanes=4194304)}else{if(!r)if(e=Yr(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),gn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!U)return re(t),null}else 2*Q()-i.renderingStartTime>sn&&n!==1073741824&&(t.flags|=128,r=!0,gn(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Q(),t.sibling=null,n=$.current,F($,r?n&1|2:n&1),t):(re(t),null);case 22:case 23:return Po(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?ge&1073741824&&(re(t),t.subtreeFlags&6&&(t.flags|=8192)):re(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function gf(e,t){switch(so(t),t.tag){case 1:return he(t.type)&&Vr(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ln(),A(me),A(ie),go(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return vo(t),null;case 13:if(A($),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));nn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return A($),null;case 4:return ln(),null;case 10:return fo(t.type._context),null;case 22:case 23:return Po(),null;case 24:return null;default:return null}}var gr=!1,le=!1,yf=typeof WeakSet=="function"?WeakSet:Set,E=null;function Gt(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){W(e,t,r)}else n.current=null}function Li(e,t,n){try{n()}catch(r){W(e,t,r)}}var Ds=!1;function xf(e,t){if(hi=Or,e=Yu(),io(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,s=-1,u=-1,c=0,m=0,h=e,p=null;t:for(;;){for(var g;h!==n||l!==0&&h.nodeType!==3||(s=o+l),h!==i||r!==0&&h.nodeType!==3||(u=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(g=h.firstChild)!==null;)p=h,h=g;for(;;){if(h===e)break t;if(p===n&&++c===l&&(s=o),p===i&&++m===r&&(u=o),(g=h.nextSibling)!==null)break;h=p,p=h.parentNode}h=g}n=s===-1||u===-1?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(vi={focusedElem:e,selectionRange:n},Or=!1,E=t;E!==null;)if(t=E,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,E=e;else for(;E!==null;){t=E;try{var y=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var w=y.memoizedProps,P=y.memoizedState,f=t.stateNode,d=f.getSnapshotBeforeUpdate(t.elementType===t.type?w:Te(t.type,w),P);f.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(x){W(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,E=e;break}E=t.return}return y=Ds,Ds=!1,y}function Mn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var i=l.destroy;l.destroy=void 0,i!==void 0&&Li(t,n,i)}l=l.next}while(l!==r)}}function dl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ri(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ba(e){var t=e.alternate;t!==null&&(e.alternate=null,Ba(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ue],delete t[Hn],delete t[xi],delete t[ef],delete t[tf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Qa(e){return e.tag===5||e.tag===3||e.tag===4}function Is(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Qa(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Di(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=$r));else if(r!==4&&(e=e.child,e!==null))for(Di(e,t,n),e=e.sibling;e!==null;)Di(e,t,n),e=e.sibling}function Ii(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Ii(e,t,n),e=e.sibling;e!==null;)Ii(e,t,n),e=e.sibling}var b=null,Le=!1;function qe(e,t,n){for(n=n.child;n!==null;)Ga(e,t,n),n=n.sibling}function Ga(e,t,n){if($e&&typeof $e.onCommitFiberUnmount=="function")try{$e.onCommitFiberUnmount(rl,n)}catch{}switch(n.tag){case 5:le||Gt(n,t);case 6:var r=b,l=Le;b=null,qe(e,t,n),b=r,Le=l,b!==null&&(Le?(e=b,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):b.removeChild(n.stateNode));break;case 18:b!==null&&(Le?(e=b,n=n.stateNode,e.nodeType===8?Ol(e.parentNode,n):e.nodeType===1&&Ol(e,n),On(e)):Ol(b,n.stateNode));break;case 4:r=b,l=Le,b=n.stateNode.containerInfo,Le=!0,qe(e,t,n),b=r,Le=l;break;case 0:case 11:case 14:case 15:if(!le&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var i=l,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&Li(n,t,o),l=l.next}while(l!==r)}qe(e,t,n);break;case 1:if(!le&&(Gt(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){W(n,t,s)}qe(e,t,n);break;case 21:qe(e,t,n);break;case 22:n.mode&1?(le=(r=le)||n.memoizedState!==null,qe(e,t,n),le=r):qe(e,t,n);break;default:qe(e,t,n)}}function Fs(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new yf),t.forEach(function(r){var l=Pf.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function ze(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var i=e,o=t,s=o;e:for(;s!==null;){switch(s.tag){case 5:b=s.stateNode,Le=!1;break e;case 3:b=s.stateNode.containerInfo,Le=!0;break e;case 4:b=s.stateNode.containerInfo,Le=!0;break e}s=s.return}if(b===null)throw Error(k(160));Ga(i,o,l),b=null,Le=!1;var u=l.alternate;u!==null&&(u.return=null),l.return=null}catch(c){W(l,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Ka(t,e),t=t.sibling}function Ka(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ze(t,e),Oe(e),r&4){try{Mn(3,e,e.return),dl(3,e)}catch(w){W(e,e.return,w)}try{Mn(5,e,e.return)}catch(w){W(e,e.return,w)}}break;case 1:ze(t,e),Oe(e),r&512&&n!==null&&Gt(n,n.return);break;case 5:if(ze(t,e),Oe(e),r&512&&n!==null&&Gt(n,n.return),e.flags&32){var l=e.stateNode;try{Rn(l,"")}catch(w){W(e,e.return,w)}}if(r&4&&(l=e.stateNode,l!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,s=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{s==="input"&&i.type==="radio"&&i.name!=null&&hu(l,i),ii(s,o);var c=ii(s,i);for(o=0;o<u.length;o+=2){var m=u[o],h=u[o+1];m==="style"?wu(l,h):m==="dangerouslySetInnerHTML"?yu(l,h):m==="children"?Rn(l,h):Gi(l,m,h,c)}switch(s){case"input":ei(l,i);break;case"textarea":vu(l,i);break;case"select":var p=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Yt(l,!!i.multiple,g,!1):p!==!!i.multiple&&(i.defaultValue!=null?Yt(l,!!i.multiple,i.defaultValue,!0):Yt(l,!!i.multiple,i.multiple?[]:"",!1))}l[Hn]=i}catch(w){W(e,e.return,w)}}break;case 6:if(ze(t,e),Oe(e),r&4){if(e.stateNode===null)throw Error(k(162));l=e.stateNode,i=e.memoizedProps;try{l.nodeValue=i}catch(w){W(e,e.return,w)}}break;case 3:if(ze(t,e),Oe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{On(t.containerInfo)}catch(w){W(e,e.return,w)}break;case 4:ze(t,e),Oe(e);break;case 13:ze(t,e),Oe(e),l=e.child,l.flags&8192&&(i=l.memoizedState!==null,l.stateNode.isHidden=i,!i||l.alternate!==null&&l.alternate.memoizedState!==null||(Co=Q())),r&4&&Fs(e);break;case 22:if(m=n!==null&&n.memoizedState!==null,e.mode&1?(le=(c=le)||m,ze(t,e),le=c):ze(t,e),Oe(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!m&&e.mode&1)for(E=e,m=e.child;m!==null;){for(h=E=m;E!==null;){switch(p=E,g=p.child,p.tag){case 0:case 11:case 14:case 15:Mn(4,p,p.return);break;case 1:Gt(p,p.return);var y=p.stateNode;if(typeof y.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,y.props=t.memoizedProps,y.state=t.memoizedState,y.componentWillUnmount()}catch(w){W(r,n,w)}}break;case 5:Gt(p,p.return);break;case 22:if(p.memoizedState!==null){As(h);continue}}g!==null?(g.return=p,E=g):As(h)}m=m.sibling}e:for(m=null,h=e;;){if(h.tag===5){if(m===null){m=h;try{l=h.stateNode,c?(i=l.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(s=h.stateNode,u=h.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,s.style.display=xu("display",o))}catch(w){W(e,e.return,w)}}}else if(h.tag===6){if(m===null)try{h.stateNode.nodeValue=c?"":h.memoizedProps}catch(w){W(e,e.return,w)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;m===h&&(m=null),h=h.return}m===h&&(m=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:ze(t,e),Oe(e),r&4&&Fs(e);break;case 21:break;default:ze(t,e),Oe(e)}}function Oe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Qa(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(Rn(l,""),r.flags&=-33);var i=Is(e);Ii(e,i,l);break;case 3:case 4:var o=r.stateNode.containerInfo,s=Is(e);Di(e,s,o);break;default:throw Error(k(161))}}catch(u){W(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function wf(e,t,n){E=e,Ya(e)}function Ya(e,t,n){for(var r=(e.mode&1)!==0;E!==null;){var l=E,i=l.child;if(l.tag===22&&r){var o=l.memoizedState!==null||gr;if(!o){var s=l.alternate,u=s!==null&&s.memoizedState!==null||le;s=gr;var c=le;if(gr=o,(le=u)&&!c)for(E=l;E!==null;)o=E,u=o.child,o.tag===22&&o.memoizedState!==null?Us(l):u!==null?(u.return=o,E=u):Us(l);for(;i!==null;)E=i,Ya(i),i=i.sibling;E=l,gr=s,le=c}Os(e)}else l.subtreeFlags&8772&&i!==null?(i.return=l,E=i):Os(e)}}function Os(e){for(;E!==null;){var t=E;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:le||dl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!le)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:Te(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&ks(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ks(t,o,n)}break;case 5:var s=t.stateNode;if(n===null&&t.flags&4){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var m=c.memoizedState;if(m!==null){var h=m.dehydrated;h!==null&&On(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}le||t.flags&512&&Ri(t)}catch(p){W(t,t.return,p)}}if(t===e){E=null;break}if(n=t.sibling,n!==null){n.return=t.return,E=n;break}E=t.return}}function As(e){for(;E!==null;){var t=E;if(t===e){E=null;break}var n=t.sibling;if(n!==null){n.return=t.return,E=n;break}E=t.return}}function Us(e){for(;E!==null;){var t=E;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{dl(4,t)}catch(u){W(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(u){W(t,l,u)}}var i=t.return;try{Ri(t)}catch(u){W(t,i,u)}break;case 5:var o=t.return;try{Ri(t)}catch(u){W(t,o,u)}}}catch(u){W(t,t.return,u)}if(t===e){E=null;break}var s=t.sibling;if(s!==null){s.return=t.return,E=s;break}E=t.return}}var kf=Math.ceil,Jr=Je.ReactCurrentDispatcher,jo=Je.ReactCurrentOwner,Ce=Je.ReactCurrentBatchConfig,R=0,J=null,G=null,ee=0,ge=0,Kt=vt(0),Y=0,Yn=null,Tt=0,fl=0,Eo=0,zn=null,fe=null,Co=0,sn=1/0,He=null,qr=!1,Fi=null,ct=null,yr=!1,lt=null,br=0,Tn=0,Oi=null,Mr=-1,zr=0;function ue(){return R&6?Q():Mr!==-1?Mr:Mr=Q()}function dt(e){return e.mode&1?R&2&&ee!==0?ee&-ee:rf.transition!==null?(zr===0&&(zr=Lu()),zr):(e=I,e!==0||(e=window.event,e=e===void 0?16:Uu(e.type)),e):1}function Ie(e,t,n,r){if(50<Tn)throw Tn=0,Oi=null,Error(k(185));Zn(e,n,r),(!(R&2)||e!==J)&&(e===J&&(!(R&2)&&(fl|=n),Y===4&&nt(e,ee)),ve(e,r),n===1&&R===0&&!(t.mode&1)&&(sn=Q()+500,ul&&gt()))}function ve(e,t){var n=e.callbackNode;rd(e,t);var r=Fr(e,e===J?ee:0);if(r===0)n!==null&&Yo(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Yo(n),t===1)e.tag===0?nf($s.bind(null,e)):la($s.bind(null,e)),qd(function(){!(R&6)&&gt()}),n=null;else{switch(Ru(r)){case 1:n=Ji;break;case 4:n=zu;break;case 16:n=Ir;break;case 536870912:n=Tu;break;default:n=Ir}n=nc(n,Xa.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Xa(e,t){if(Mr=-1,zr=0,R&6)throw Error(k(327));var n=e.callbackNode;if(bt()&&e.callbackNode!==n)return null;var r=Fr(e,e===J?ee:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=el(e,r);else{t=r;var l=R;R|=2;var i=Ja();(J!==e||ee!==t)&&(He=null,sn=Q()+500,Ct(e,t));do try{jf();break}catch(s){Za(e,s)}while(!0);co(),Jr.current=i,R=l,G!==null?t=0:(J=null,ee=0,t=Y)}if(t!==0){if(t===2&&(l=ci(e),l!==0&&(r=l,t=Ai(e,l))),t===1)throw n=Yn,Ct(e,0),nt(e,r),ve(e,Q()),n;if(t===6)nt(e,r);else{if(l=e.current.alternate,!(r&30)&&!Sf(l)&&(t=el(e,r),t===2&&(i=ci(e),i!==0&&(r=i,t=Ai(e,i))),t===1))throw n=Yn,Ct(e,0),nt(e,r),ve(e,Q()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:St(e,fe,He);break;case 3:if(nt(e,r),(r&130023424)===r&&(t=Co+500-Q(),10<t)){if(Fr(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){ue(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=yi(St.bind(null,e,fe,He),t);break}St(e,fe,He);break;case 4:if(nt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var o=31-De(r);i=1<<o,o=t[o],o>l&&(l=o),r&=~i}if(r=l,r=Q()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*kf(r/1960))-r,10<r){e.timeoutHandle=yi(St.bind(null,e,fe,He),r);break}St(e,fe,He);break;case 5:St(e,fe,He);break;default:throw Error(k(329))}}}return ve(e,Q()),e.callbackNode===n?Xa.bind(null,e):null}function Ai(e,t){var n=zn;return e.current.memoizedState.isDehydrated&&(Ct(e,t).flags|=256),e=el(e,t),e!==2&&(t=fe,fe=n,t!==null&&Ui(t)),e}function Ui(e){fe===null?fe=e:fe.push.apply(fe,e)}function Sf(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],i=l.getSnapshot;l=l.value;try{if(!Fe(i(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function nt(e,t){for(t&=~Eo,t&=~fl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-De(t),r=1<<n;e[n]=-1,t&=~r}}function $s(e){if(R&6)throw Error(k(327));bt();var t=Fr(e,0);if(!(t&1))return ve(e,Q()),null;var n=el(e,t);if(e.tag!==0&&n===2){var r=ci(e);r!==0&&(t=r,n=Ai(e,r))}if(n===1)throw n=Yn,Ct(e,0),nt(e,t),ve(e,Q()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,St(e,fe,He),ve(e,Q()),null}function _o(e,t){var n=R;R|=1;try{return e(t)}finally{R=n,R===0&&(sn=Q()+500,ul&&gt())}}function Lt(e){lt!==null&&lt.tag===0&&!(R&6)&&bt();var t=R;R|=1;var n=Ce.transition,r=I;try{if(Ce.transition=null,I=1,e)return e()}finally{I=r,Ce.transition=n,R=t,!(R&6)&&gt()}}function Po(){ge=Kt.current,A(Kt)}function Ct(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Jd(n)),G!==null)for(n=G.return;n!==null;){var r=n;switch(so(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Vr();break;case 3:ln(),A(me),A(ie),go();break;case 5:vo(r);break;case 4:ln();break;case 13:A($);break;case 19:A($);break;case 10:fo(r.type._context);break;case 22:case 23:Po()}n=n.return}if(J=e,G=e=ft(e.current,null),ee=ge=t,Y=0,Yn=null,Eo=fl=Tt=0,fe=zn=null,jt!==null){for(t=0;t<jt.length;t++)if(n=jt[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=l,r.next=o}n.pending=r}jt=null}return e}function Za(e,t){do{var n=G;try{if(co(),Cr.current=Zr,Xr){for(var r=V.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}Xr=!1}if(zt=0,Z=K=V=null,Pn=!1,Qn=0,jo.current=null,n===null||n.return===null){Y=1,Yn=t,G=null;break}e:{var i=e,o=n.return,s=n,u=t;if(t=ee,s.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,m=s,h=m.tag;if(!(m.mode&1)&&(h===0||h===11||h===15)){var p=m.alternate;p?(m.updateQueue=p.updateQueue,m.memoizedState=p.memoizedState,m.lanes=p.lanes):(m.updateQueue=null,m.memoizedState=null)}var g=_s(o);if(g!==null){g.flags&=-257,Ps(g,o,s,i,t),g.mode&1&&Cs(i,c,t),t=g,u=c;var y=t.updateQueue;if(y===null){var w=new Set;w.add(u),t.updateQueue=w}else y.add(u);break e}else{if(!(t&1)){Cs(i,c,t),Mo();break e}u=Error(k(426))}}else if(U&&s.mode&1){var P=_s(o);if(P!==null){!(P.flags&65536)&&(P.flags|=256),Ps(P,o,s,i,t),uo(on(u,s));break e}}i=u=on(u,s),Y!==4&&(Y=2),zn===null?zn=[i]:zn.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var f=Ra(i,u,t);ws(i,f);break e;case 1:s=u;var d=i.type,v=i.stateNode;if(!(i.flags&128)&&(typeof d.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(ct===null||!ct.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=Da(i,s,t);ws(i,x);break e}}i=i.return}while(i!==null)}ba(n)}catch(S){t=S,G===n&&n!==null&&(G=n=n.return);continue}break}while(!0)}function Ja(){var e=Jr.current;return Jr.current=Zr,e===null?Zr:e}function Mo(){(Y===0||Y===3||Y===2)&&(Y=4),J===null||!(Tt&268435455)&&!(fl&268435455)||nt(J,ee)}function el(e,t){var n=R;R|=2;var r=Ja();(J!==e||ee!==t)&&(He=null,Ct(e,t));do try{Nf();break}catch(l){Za(e,l)}while(!0);if(co(),R=n,Jr.current=r,G!==null)throw Error(k(261));return J=null,ee=0,Y}function Nf(){for(;G!==null;)qa(G)}function jf(){for(;G!==null&&!Yc();)qa(G)}function qa(e){var t=tc(e.alternate,e,ge);e.memoizedProps=e.pendingProps,t===null?ba(e):G=t,jo.current=null}function ba(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=gf(n,t),n!==null){n.flags&=32767,G=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Y=6,G=null;return}}else if(n=vf(n,t,ge),n!==null){G=n;return}if(t=t.sibling,t!==null){G=t;return}G=t=e}while(t!==null);Y===0&&(Y=5)}function St(e,t,n){var r=I,l=Ce.transition;try{Ce.transition=null,I=1,Ef(e,t,n,r)}finally{Ce.transition=l,I=r}return null}function Ef(e,t,n,r){do bt();while(lt!==null);if(R&6)throw Error(k(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(ld(e,i),e===J&&(G=J=null,ee=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||yr||(yr=!0,nc(Ir,function(){return bt(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ce.transition,Ce.transition=null;var o=I;I=1;var s=R;R|=4,jo.current=null,xf(e,n),Ka(n,e),Bd(vi),Or=!!hi,vi=hi=null,e.current=n,wf(n),Xc(),R=s,I=o,Ce.transition=i}else e.current=n;if(yr&&(yr=!1,lt=e,br=l),i=e.pendingLanes,i===0&&(ct=null),qc(n.stateNode),ve(e,Q()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(qr)throw qr=!1,e=Fi,Fi=null,e;return br&1&&e.tag!==0&&bt(),i=e.pendingLanes,i&1?e===Oi?Tn++:(Tn=0,Oi=e):Tn=0,gt(),null}function bt(){if(lt!==null){var e=Ru(br),t=Ce.transition,n=I;try{if(Ce.transition=null,I=16>e?16:e,lt===null)var r=!1;else{if(e=lt,lt=null,br=0,R&6)throw Error(k(331));var l=R;for(R|=4,E=e.current;E!==null;){var i=E,o=i.child;if(E.flags&16){var s=i.deletions;if(s!==null){for(var u=0;u<s.length;u++){var c=s[u];for(E=c;E!==null;){var m=E;switch(m.tag){case 0:case 11:case 15:Mn(8,m,i)}var h=m.child;if(h!==null)h.return=m,E=h;else for(;E!==null;){m=E;var p=m.sibling,g=m.return;if(Ba(m),m===c){E=null;break}if(p!==null){p.return=g,E=p;break}E=g}}}var y=i.alternate;if(y!==null){var w=y.child;if(w!==null){y.child=null;do{var P=w.sibling;w.sibling=null,w=P}while(w!==null)}}E=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,E=o;else e:for(;E!==null;){if(i=E,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Mn(9,i,i.return)}var f=i.sibling;if(f!==null){f.return=i.return,E=f;break e}E=i.return}}var d=e.current;for(E=d;E!==null;){o=E;var v=o.child;if(o.subtreeFlags&2064&&v!==null)v.return=o,E=v;else e:for(o=d;E!==null;){if(s=E,s.flags&2048)try{switch(s.tag){case 0:case 11:case 15:dl(9,s)}}catch(S){W(s,s.return,S)}if(s===o){E=null;break e}var x=s.sibling;if(x!==null){x.return=s.return,E=x;break e}E=s.return}}if(R=l,gt(),$e&&typeof $e.onPostCommitFiberRoot=="function")try{$e.onPostCommitFiberRoot(rl,e)}catch{}r=!0}return r}finally{I=n,Ce.transition=t}}return!1}function Vs(e,t,n){t=on(n,t),t=Ra(e,t,1),e=at(e,t,1),t=ue(),e!==null&&(Zn(e,1,t),ve(e,t))}function W(e,t,n){if(e.tag===3)Vs(e,e,n);else for(;t!==null;){if(t.tag===3){Vs(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(ct===null||!ct.has(r))){e=on(n,e),e=Da(t,e,1),t=at(t,e,1),e=ue(),t!==null&&(Zn(t,1,e),ve(t,e));break}}t=t.return}}function Cf(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ue(),e.pingedLanes|=e.suspendedLanes&n,J===e&&(ee&n)===n&&(Y===4||Y===3&&(ee&130023424)===ee&&500>Q()-Co?Ct(e,0):Eo|=n),ve(e,t)}function ec(e,t){t===0&&(e.mode&1?(t=ur,ur<<=1,!(ur&130023424)&&(ur=4194304)):t=1);var n=ue();e=Xe(e,t),e!==null&&(Zn(e,t,n),ve(e,n))}function _f(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),ec(e,n)}function Pf(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),ec(e,n)}var tc;tc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||me.current)pe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return pe=!1,hf(e,t,n);pe=!!(e.flags&131072)}else pe=!1,U&&t.flags&1048576&&ia(t,Br,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Pr(e,t),e=t.pendingProps;var l=tn(t,ie.current);qt(t,n),l=xo(null,t,r,e,l,n);var i=wo();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,he(r)?(i=!0,Hr(t)):i=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,mo(t),l.updater=cl,t.stateNode=l,l._reactInternals=t,Ei(t,r,e,n),t=Pi(null,t,r,!0,i,n)):(t.tag=0,U&&i&&oo(t),oe(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Pr(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=zf(r),e=Te(r,e),l){case 0:t=_i(null,t,r,e,n);break e;case 1:t=Ts(null,t,r,e,n);break e;case 11:t=Ms(null,t,r,e,n);break e;case 14:t=zs(null,t,r,Te(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Te(r,l),_i(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Te(r,l),Ts(e,t,r,l,n);case 3:e:{if(Aa(t),e===null)throw Error(k(387));r=t.pendingProps,i=t.memoizedState,l=i.element,da(e,t),Kr(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){l=on(Error(k(423)),t),t=Ls(e,t,r,n,l);break e}else if(r!==l){l=on(Error(k(424)),t),t=Ls(e,t,r,n,l);break e}else for(ye=ut(t.stateNode.containerInfo.firstChild),xe=t,U=!0,Re=null,n=aa(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(nn(),r===l){t=Ze(e,t,n);break e}oe(e,t,r,n)}t=t.child}return t;case 5:return fa(t),e===null&&Si(t),r=t.type,l=t.pendingProps,i=e!==null?e.memoizedProps:null,o=l.children,gi(r,l)?o=null:i!==null&&gi(r,i)&&(t.flags|=32),Oa(e,t),oe(e,t,o,n),t.child;case 6:return e===null&&Si(t),null;case 13:return Ua(e,t,n);case 4:return ho(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=rn(t,null,r,n):oe(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Te(r,l),Ms(e,t,r,l,n);case 7:return oe(e,t,t.pendingProps,n),t.child;case 8:return oe(e,t,t.pendingProps.children,n),t.child;case 12:return oe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,i=t.memoizedProps,o=l.value,F(Qr,r._currentValue),r._currentValue=o,i!==null)if(Fe(i.value,o)){if(i.children===l.children&&!me.current){t=Ze(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var s=i.dependencies;if(s!==null){o=i.child;for(var u=s.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=Ge(-1,n&-n),u.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var m=c.pending;m===null?u.next=u:(u.next=m.next,m.next=u),c.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),Ni(i.return,n,t),s.lanes|=n;break}u=u.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(k(341));o.lanes|=n,s=o.alternate,s!==null&&(s.lanes|=n),Ni(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}oe(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,qt(t,n),l=_e(l),r=r(l),t.flags|=1,oe(e,t,r,n),t.child;case 14:return r=t.type,l=Te(r,t.pendingProps),l=Te(r.type,l),zs(e,t,r,l,n);case 15:return Ia(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:Te(r,l),Pr(e,t),t.tag=1,he(r)?(e=!0,Hr(t)):e=!1,qt(t,n),La(t,r,l),Ei(t,r,l,n),Pi(null,t,r,!0,e,n);case 19:return $a(e,t,n);case 22:return Fa(e,t,n)}throw Error(k(156,t.tag))};function nc(e,t){return Mu(e,t)}function Mf(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ee(e,t,n,r){return new Mf(e,t,n,r)}function zo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function zf(e){if(typeof e=="function")return zo(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Yi)return 11;if(e===Xi)return 14}return 2}function ft(e,t){var n=e.alternate;return n===null?(n=Ee(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Tr(e,t,n,r,l,i){var o=2;if(r=e,typeof e=="function")zo(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Ot:return _t(n.children,l,i,t);case Ki:o=8,l|=8;break;case Xl:return e=Ee(12,n,t,l|2),e.elementType=Xl,e.lanes=i,e;case Zl:return e=Ee(13,n,t,l),e.elementType=Zl,e.lanes=i,e;case Jl:return e=Ee(19,n,t,l),e.elementType=Jl,e.lanes=i,e;case fu:return pl(n,l,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case cu:o=10;break e;case du:o=9;break e;case Yi:o=11;break e;case Xi:o=14;break e;case be:o=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=Ee(o,n,t,l),t.elementType=e,t.type=r,t.lanes=i,t}function _t(e,t,n,r){return e=Ee(7,e,r,t),e.lanes=n,e}function pl(e,t,n,r){return e=Ee(22,e,r,t),e.elementType=fu,e.lanes=n,e.stateNode={isHidden:!1},e}function Ql(e,t,n){return e=Ee(6,e,null,t),e.lanes=n,e}function Gl(e,t,n){return t=Ee(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Tf(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Cl(0),this.expirationTimes=Cl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Cl(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function To(e,t,n,r,l,i,o,s,u){return e=new Tf(e,t,n,s,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ee(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},mo(i),e}function Lf(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ft,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function rc(e){if(!e)return mt;e=e._reactInternals;e:{if(Dt(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(he(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(he(n))return ra(e,n,t)}return t}function lc(e,t,n,r,l,i,o,s,u){return e=To(n,r,!0,e,l,i,o,s,u),e.context=rc(null),n=e.current,r=ue(),l=dt(n),i=Ge(r,l),i.callback=t??null,at(n,i,l),e.current.lanes=l,Zn(e,l,r),ve(e,r),e}function ml(e,t,n,r){var l=t.current,i=ue(),o=dt(l);return n=rc(n),t.context===null?t.context=n:t.pendingContext=n,t=Ge(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=at(l,t,o),e!==null&&(Ie(e,l,o,i),Er(e,l,o)),o}function tl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Hs(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Lo(e,t){Hs(e,t),(e=e.alternate)&&Hs(e,t)}function Rf(){return null}var ic=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ro(e){this._internalRoot=e}hl.prototype.render=Ro.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));ml(e,t,null,null)};hl.prototype.unmount=Ro.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Lt(function(){ml(null,e,null,null)}),t[Ye]=null}};function hl(e){this._internalRoot=e}hl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Fu();e={blockedOn:null,target:e,priority:t};for(var n=0;n<tt.length&&t!==0&&t<tt[n].priority;n++);tt.splice(n,0,e),n===0&&Au(e)}};function Do(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function vl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ws(){}function Df(e,t,n,r,l){if(l){if(typeof r=="function"){var i=r;r=function(){var c=tl(o);i.call(c)}}var o=lc(t,r,e,0,null,!1,!1,"",Ws);return e._reactRootContainer=o,e[Ye]=o.current,$n(e.nodeType===8?e.parentNode:e),Lt(),o}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var s=r;r=function(){var c=tl(u);s.call(c)}}var u=To(e,0,!1,null,null,!1,!1,"",Ws);return e._reactRootContainer=u,e[Ye]=u.current,$n(e.nodeType===8?e.parentNode:e),Lt(function(){ml(t,u,n,r)}),u}function gl(e,t,n,r,l){var i=n._reactRootContainer;if(i){var o=i;if(typeof l=="function"){var s=l;l=function(){var u=tl(o);s.call(u)}}ml(t,o,e,l)}else o=Df(n,t,e,l,r);return tl(o)}Du=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=kn(t.pendingLanes);n!==0&&(qi(t,n|1),ve(t,Q()),!(R&6)&&(sn=Q()+500,gt()))}break;case 13:Lt(function(){var r=Xe(e,1);if(r!==null){var l=ue();Ie(r,e,1,l)}}),Lo(e,1)}};bi=function(e){if(e.tag===13){var t=Xe(e,134217728);if(t!==null){var n=ue();Ie(t,e,134217728,n)}Lo(e,134217728)}};Iu=function(e){if(e.tag===13){var t=dt(e),n=Xe(e,t);if(n!==null){var r=ue();Ie(n,e,t,r)}Lo(e,t)}};Fu=function(){return I};Ou=function(e,t){var n=I;try{return I=e,t()}finally{I=n}};si=function(e,t,n){switch(t){case"input":if(ei(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=sl(r);if(!l)throw Error(k(90));mu(r),ei(r,l)}}}break;case"textarea":vu(e,n);break;case"select":t=n.value,t!=null&&Yt(e,!!n.multiple,t,!1)}};Nu=_o;ju=Lt;var If={usingClientEntryPoint:!1,Events:[qn,Vt,sl,ku,Su,_o]},yn={findFiberByHostInstance:Nt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Ff={bundleType:yn.bundleType,version:yn.version,rendererPackageName:yn.rendererPackageName,rendererConfig:yn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Je.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=_u(e),e===null?null:e.stateNode},findFiberByHostInstance:yn.findFiberByHostInstance||Rf,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var xr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!xr.isDisabled&&xr.supportsFiber)try{rl=xr.inject(Ff),$e=xr}catch{}}ke.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=If;ke.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Do(t))throw Error(k(200));return Lf(e,t,null,n)};ke.createRoot=function(e,t){if(!Do(e))throw Error(k(299));var n=!1,r="",l=ic;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=To(e,1,!1,null,null,n,!1,r,l),e[Ye]=t.current,$n(e.nodeType===8?e.parentNode:e),new Ro(t)};ke.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=_u(t),e=e===null?null:e.stateNode,e};ke.flushSync=function(e){return Lt(e)};ke.hydrate=function(e,t,n){if(!vl(t))throw Error(k(200));return gl(null,e,t,!0,n)};ke.hydrateRoot=function(e,t,n){if(!Do(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,l=!1,i="",o=ic;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=lc(t,null,e,1,n??null,l,!1,i,o),e[Ye]=t.current,$n(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new hl(t)};ke.render=function(e,t,n){if(!vl(t))throw Error(k(200));return gl(null,e,t,!1,n)};ke.unmountComponentAtNode=function(e){if(!vl(e))throw Error(k(40));return e._reactRootContainer?(Lt(function(){gl(null,null,e,!1,function(){e._reactRootContainer=null,e[Ye]=null})}),!0):!1};ke.unstable_batchedUpdates=_o;ke.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!vl(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return gl(e,t,n,!1,r)};ke.version="18.3.1-next-f1338f8080-20240426";function oc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(oc)}catch(e){console.error(e)}}oc(),ou.exports=ke;var Of=ou.exports,sc,Bs=Of;sc=Bs.createRoot,Bs.hydrateRoot;/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Af={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uf=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),Me=(e,t)=>{const n=se.forwardRef(({color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:s="",children:u,...c},m)=>se.createElement("svg",{ref:m,...Af,width:l,height:l,stroke:r,strokeWidth:o?Number(i)*24/Number(l):i,className:["lucide",`lucide-${Uf(e)}`,s].join(" "),...c},[...t.map(([h,p])=>se.createElement(h,p)),...Array.isArray(u)?u:[u]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Io=Me("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kl=Me("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $f=Me("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qs=Me("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uc=Me("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ac=Me("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vf=Me("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hf=Me("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wf=Me("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bf=Me("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gs=Me("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qf=Me("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),Gf=({data:e,rPeaks:t=[],filteredSignal:n,morphologicalPoints:r,showFiltered:l=!1,height:i=300})=>{const{pathData:o,peakMarkers:s,morphologicalMarkers:u,dimensions:c}=se.useMemo(()=>{const p=i-80,g=800-2*40,y=l&&n?n:e.samples,w=Math.min(...y),P=Math.max(...y),f=P-w,d=Math.min(...e.timestamps),x=Math.max(...e.timestamps)-d;let S="";for(let j=0;j<y.length;j++){const D=40+(e.timestamps[j]-d)/x*g,M=40+(P-y[j])/f*p;j===0?S+=`M ${D} ${M}`:S+=` L ${D} ${M}`}const _=t.map(j=>{const D=40+(j.time-d)/x*g,M=40+(P-y[j.index])/f*p;return{x:D,y:M,peak:j}}),N=[];if(r){const j=(D,M,q)=>{D.forEach(de=>{if(de<e.timestamps.length){const yt=40+(e.timestamps[de]-d)/x*g,er=40+(P-y[de])/f*p;N.push({x:yt,y:er,type:M,color:q,index:de})}})};r.P_locs&&j(r.P_locs,"P","#8b5cf6"),r.Q_locs&&j(r.Q_locs,"Q","#10b981"),r.S_locs&&j(r.S_locs,"S","#3b82f6"),r.T_locs&&j(r.T_locs,"T","#f59e0b")}return{pathData:S,peakMarkers:_,morphologicalMarkers:N,dimensions:{width:800,height:i,padding:40,chartWidth:g,chartHeight:p}}},[e,t,n,r,l,i]);return a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-4",children:[a.jsxs("div",{className:"flex justify-between items-center mb-4",children:[a.jsxs("h3",{className:"text-lg font-semibold text-gray-800",children:["ECG Signal ",l?"(Filtered)":"(Raw)"]}),a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-3 h-0.5 bg-blue-600 mr-2"}),a.jsx("span",{className:"text-sm text-gray-600",children:"ECG Signal"})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm text-gray-600",children:"R-Peaks"})]}),r&&a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-3 h-3 bg-purple-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm text-gray-600",children:"P"})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm text-gray-600",children:"Q"})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm text-gray-600",children:"S"})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("div",{className:"w-3 h-3 bg-yellow-500 rounded-full mr-2"}),a.jsx("span",{className:"text-sm text-gray-600",children:"T"})]})]})]})]}),a.jsx("div",{className:"overflow-x-auto",children:a.jsxs("svg",{width:c.width,height:c.height,className:"border rounded",children:[a.jsx("defs",{children:a.jsx("pattern",{id:"grid",width:"50",height:"50",patternUnits:"userSpaceOnUse",children:a.jsx("path",{d:"M 50 0 L 0 0 0 50",fill:"none",stroke:"#e5e7eb",strokeWidth:"1"})})}),a.jsx("rect",{width:"100%",height:"100%",fill:"url(#grid)"}),a.jsx("line",{x1:c.padding,y1:c.height-c.padding,x2:c.width-c.padding,y2:c.height-c.padding,stroke:"#374151",strokeWidth:"2"}),a.jsx("line",{x1:c.padding,y1:c.padding,x2:c.padding,y2:c.height-c.padding,stroke:"#374151",strokeWidth:"2"}),a.jsx("path",{d:o,fill:"none",stroke:"#2563eb",strokeWidth:"1.5",opacity:"0.8"}),s.map((m,h)=>a.jsxs("g",{children:[a.jsx("circle",{cx:m.x,cy:m.y,r:"4",fill:"#dc2626",stroke:"#fff",strokeWidth:"1"}),a.jsx("text",{x:m.x,y:m.y-10,textAnchor:"middle",fontSize:"10",fill:"#dc2626",fontWeight:"bold",children:"R"})]},h)),u.map((m,h)=>a.jsxs("g",{children:[a.jsx("circle",{cx:m.x,cy:m.y,r:"3",fill:m.color,stroke:"#fff",strokeWidth:"1"}),a.jsx("text",{x:m.x,y:m.y-8,textAnchor:"middle",fontSize:"9",fill:m.color,fontWeight:"bold",children:m.type})]},`morph-${h}`)),a.jsx("text",{x:c.width/2,y:c.height-10,textAnchor:"middle",fontSize:"12",fill:"#6b7280",children:"Time (ms)"}),a.jsx("text",{x:15,y:c.height/2,textAnchor:"middle",fontSize:"12",fill:"#6b7280",transform:`rotate(-90, 15, ${c.height/2})`,children:"Amplitude (mV)"})]})})]})},Kf=({metrics:e})=>{const t=(r,l=1)=>r.toFixed(l),n=(r,l)=>{const o={sdnn:{excellent:50,good:30,fair:20},rmssd:{excellent:40,good:25,fair:15},pnn50:{excellent:10,good:5,fair:2}}[l];return r>=o.excellent?"text-green-600":r>=o.good?"text-yellow-600":r>=o.fair?"text-orange-600":"text-red-600"};return a.jsxs("div",{className:"space-y-6",children:[a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[a.jsxs("div",{className:"flex items-center mb-4",children:[a.jsx(ac,{className:"w-6 h-6 text-red-500 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Time Domain Metrics"})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[a.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-blue-600 font-medium",children:"Mean Heart Rate"}),a.jsxs("div",{className:"text-2xl font-bold text-blue-800",children:[t(e.meanHR)," ",a.jsx("span",{className:"text-sm font-normal",children:"BPM"})]})]}),a.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-green-600 font-medium",children:"Mean RR Interval"}),a.jsxs("div",{className:"text-2xl font-bold text-green-800",children:[t(e.meanRR)," ",a.jsx("span",{className:"text-sm font-normal",children:"ms"})]})]}),a.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-purple-600 font-medium",children:"SDNN"}),a.jsxs("div",{className:`text-2xl font-bold ${n(e.sdnn,"sdnn")}`,children:[t(e.sdnn)," ",a.jsx("span",{className:"text-sm font-normal",children:"ms"})]}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Standard deviation of NN intervals"})]}),a.jsxs("div",{className:"bg-teal-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-teal-600 font-medium",children:"RMSSD"}),a.jsxs("div",{className:`text-2xl font-bold ${n(e.rmssd,"rmssd")}`,children:[t(e.rmssd)," ",a.jsx("span",{className:"text-sm font-normal",children:"ms"})]}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Root mean square of successive differences"})]}),a.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-yellow-600 font-medium",children:"pNN50"}),a.jsxs("div",{className:`text-2xl font-bold ${n(e.pnn50,"pnn50")}`,children:[t(e.pnn50)," ",a.jsx("span",{className:"text-sm font-normal",children:"%"})]}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Percentage of adjacent NN intervals > 50ms"})]}),a.jsxs("div",{className:"bg-indigo-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-indigo-600 font-medium",children:"Triangular Index"}),a.jsx("div",{className:"text-2xl font-bold text-indigo-800",children:t(e.triangularIndex)}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"HRV triangular index"})]})]})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[a.jsxs("div",{className:"flex items-center mb-4",children:[a.jsx($f,{className:"w-6 h-6 text-blue-500 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Frequency Domain Metrics"})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[a.jsxs("div",{className:"bg-red-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-red-600 font-medium",children:"Total Power"}),a.jsxs("div",{className:"text-2xl font-bold text-red-800",children:[t(e.totalPower,0)," ",a.jsx("span",{className:"text-sm font-normal",children:"ms²"})]})]}),a.jsxs("div",{className:"bg-orange-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-orange-600 font-medium",children:"VLF Power"}),a.jsxs("div",{className:"text-2xl font-bold text-orange-800",children:[t(e.vlf,0)," ",a.jsx("span",{className:"text-sm font-normal",children:"ms²"})]}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"0.003-0.04 Hz"})]}),a.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-yellow-600 font-medium",children:"LF Power"}),a.jsxs("div",{className:"text-2xl font-bold text-yellow-800",children:[t(e.lf,0)," ",a.jsx("span",{className:"text-sm font-normal",children:"ms²"})]}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"0.04-0.15 Hz"})]}),a.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-green-600 font-medium",children:"HF Power"}),a.jsxs("div",{className:"text-2xl font-bold text-green-800",children:[t(e.hf,0)," ",a.jsx("span",{className:"text-sm font-normal",children:"ms²"})]}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"0.15-0.4 Hz"})]}),a.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-purple-600 font-medium",children:"LF/HF Ratio"}),a.jsx("div",{className:"text-2xl font-bold text-purple-800",children:t(e.lfhfRatio,2)}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Sympathovagal balance"})]}),a.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-blue-600 font-medium",children:"NN Count"}),a.jsx("div",{className:"text-2xl font-bold text-blue-800",children:e.nnCount}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Number of NN intervals"})]})]})]}),a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[a.jsxs("div",{className:"flex items-center mb-4",children:[a.jsx(Io,{className:"w-6 h-6 text-green-500 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Clinical Interpretation"})]}),a.jsxs("div",{className:"space-y-3",children:[a.jsxs("div",{className:"p-3 bg-blue-50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-blue-800",children:"Autonomic Balance"}),a.jsx("div",{className:"text-sm text-blue-700",children:e.lfhfRatio>2?"Sympathetic dominance":e.lfhfRatio<.5?"Parasympathetic dominance":"Balanced autonomic activity"})]}),a.jsxs("div",{className:"p-3 bg-green-50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-green-800",children:"HRV Assessment"}),a.jsx("div",{className:"text-sm text-green-700",children:e.sdnn>50?"Excellent HRV":e.sdnn>30?"Good HRV":e.sdnn>20?"Fair HRV":"Poor HRV"})]}),a.jsxs("div",{className:"p-3 bg-purple-50 rounded-lg",children:[a.jsx("div",{className:"font-medium text-purple-800",children:"Stress Level"}),a.jsx("div",{className:"text-sm text-purple-700",children:e.rmssd>40?"Low stress indication":e.rmssd>25?"Moderate stress":"High stress indication"})]})]})]})]})},Yf=({config:e,onConfigChange:t,onAnalyze:n,onFileUpload:r,isAnalyzing:l})=>{const i=o=>{var u;const s=(u=o.target.files)==null?void 0:u[0];s&&r(s)};return a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[a.jsxs("div",{className:"flex items-center mb-4",children:[a.jsx(Hf,{className:"w-6 h-6 text-blue-500 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Analysis Controls"})]}),a.jsxs("div",{className:"space-y-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Upload ECG Data"}),a.jsx("div",{className:"flex items-center justify-center w-full",children:a.jsxs("label",{className:"flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100",children:[a.jsxs("div",{className:"flex flex-col items-center justify-center pt-5 pb-6",children:[a.jsx(Bf,{className:"w-8 h-8 mb-4 text-gray-500"}),a.jsxs("p",{className:"mb-2 text-sm text-gray-500",children:[a.jsx("span",{className:"font-semibold",children:"Click to upload"})," ECG file"]}),a.jsx("p",{className:"text-xs text-gray-500",children:"CSV, TXT, or JSON files"})]}),a.jsx("input",{type:"file",className:"hidden",accept:".csv,.txt,.json",onChange:i})]})})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Min Peak Distance (samples)"}),a.jsx("input",{type:"number",value:e.minPeakDistance,onChange:o=>t({...e,minPeakDistance:parseInt(o.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",min:"10",max:"1000"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Threshold Factor"}),a.jsx("input",{type:"number",value:e.thresholdFactor,onChange:o=>t({...e,thresholdFactor:parseFloat(o.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",min:"0.1",max:"2.0",step:"0.1"})]}),a.jsxs("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Window Size (samples)"}),a.jsx("input",{type:"number",value:e.windowSize,onChange:o=>t({...e,windowSize:parseInt(o.target.value)}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",min:"10",max:"1000"})]}),a.jsxs("div",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",id:"adaptiveThreshold",checked:e.adaptiveThreshold,onChange:o=>t({...e,adaptiveThreshold:o.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),a.jsx("label",{htmlFor:"adaptiveThreshold",className:"ml-2 block text-sm text-gray-700",children:"Adaptive Threshold"})]})]}),a.jsx("button",{onClick:n,disabled:l,className:`w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${l?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}`,children:l?a.jsxs(a.Fragment,{children:[a.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Analyzing..."]}):a.jsxs(a.Fragment,{children:[a.jsx(Vf,{className:"w-4 h-4 mr-2"}),"Analyze ECG"]})})]})]})},Xf=({result:e})=>{const t=r=>{switch(r){case"excellent":return a.jsx(Qs,{className:"w-5 h-5 text-green-500"});case"good":return a.jsx(Qs,{className:"w-5 h-5 text-blue-500"});case"fair":return a.jsx(Kl,{className:"w-5 h-5 text-yellow-500"});case"poor":return a.jsx(Gs,{className:"w-5 h-5 text-red-500"});default:return a.jsx(Kl,{className:"w-5 h-5 text-gray-500"})}},n=r=>{switch(r){case"excellent":return"text-green-600 bg-green-50";case"good":return"text-blue-600 bg-blue-50";case"fair":return"text-yellow-600 bg-yellow-50";case"poor":return"text-red-600 bg-red-50";default:return"text-gray-600 bg-gray-50"}};return a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Analysis Summary"}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[a.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-blue-600 font-medium",children:"R-Peaks Detected"}),a.jsx("div",{className:"text-2xl font-bold text-blue-800",children:e.rPeaks.length})]}),a.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-green-600 font-medium",children:"RR Intervals"}),a.jsx("div",{className:"text-2xl font-bold text-green-800",children:e.rrIntervals.length})]}),a.jsxs("div",{className:`p-4 rounded-lg ${n(e.quality)}`,children:[a.jsxs("div",{className:"flex items-center mb-2",children:[t(e.quality),a.jsx("span",{className:"ml-2 text-sm font-medium",children:"Signal Quality"})]}),a.jsx("div",{className:"text-lg font-bold capitalize",children:e.quality})]}),a.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[a.jsxs("div",{className:"flex items-center mb-2",children:[a.jsx(uc,{className:"w-4 h-4 text-purple-600"}),a.jsx("span",{className:"ml-2 text-sm text-purple-600 font-medium",children:"Processing Time"})]}),a.jsxs("div",{className:"text-lg font-bold text-purple-800",children:[e.processingTime.toFixed(1),"ms"]})]})]}),a.jsxs("div",{className:"mb-6",children:[a.jsx("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Peak Confidence Distribution"}),a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-sm text-gray-600",children:"Low"}),a.jsx("div",{className:"flex-1 h-2 bg-gray-200 rounded-full overflow-hidden",children:a.jsx("div",{className:"h-full bg-gradient-to-r from-red-500 via-yellow-500 to-green-500",style:{width:`${e.rPeaks.length>0?e.rPeaks.reduce((r,l)=>r+l.confidence,0)/e.rPeaks.length*100:0}%`}})}),a.jsx("span",{className:"text-sm text-gray-600",children:"High"})]}),a.jsxs("div",{className:"text-sm text-gray-500 mt-1",children:["Average confidence: ",e.rPeaks.length>0?(e.rPeaks.reduce((r,l)=>r+l.confidence,0)/e.rPeaks.length*100).toFixed(1):0,"%"]})]}),e.warnings.length>0&&a.jsxs("div",{className:"mb-4",children:[a.jsxs("h4",{className:"text-md font-medium text-yellow-600 mb-2 flex items-center",children:[a.jsx(Kl,{className:"w-4 h-4 mr-2"}),"Warnings"]}),a.jsx("ul",{className:"space-y-1",children:e.warnings.map((r,l)=>a.jsx("li",{className:"text-sm text-yellow-700 bg-yellow-50 p-2 rounded",children:r},l))})]}),e.errors.length>0&&a.jsxs("div",{className:"mb-4",children:[a.jsxs("h4",{className:"text-md font-medium text-red-600 mb-2 flex items-center",children:[a.jsx(Gs,{className:"w-4 h-4 mr-2"}),"Errors"]}),a.jsx("ul",{className:"space-y-1",children:e.errors.map((r,l)=>a.jsx("li",{className:"text-sm text-red-700 bg-red-50 p-2 rounded",children:r},l))})]})]})},Zf=({features:e})=>{const t=s=>`${(s*1e3).toFixed(1)} ms`,n=s=>`${s.toFixed(3)} mV`,r=s=>s<.08?{color:"text-green-600",label:"Normal"}:s<.12?{color:"text-yellow-600",label:"Borderline"}:{color:"text-red-600",label:"Prolonged"},l=s=>s>=60&&s<=100?{color:"text-green-600",label:"Normal"}:s<60?{color:"text-blue-600",label:"Bradycardia"}:{color:"text-red-600",label:"Tachycardia"},i=r(e.QRS_duration),o=l(e.BPM);return a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[a.jsxs("div",{className:"flex items-center mb-4",children:[a.jsx(Qf,{className:"w-6 h-6 text-purple-500 mr-3"}),a.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Morphological Features"})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:[a.jsxs("div",{className:"bg-red-50 p-4 rounded-lg",children:[a.jsxs("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("div",{className:"text-sm text-red-600 font-medium",children:"Heart Rate"}),a.jsx(Io,{className:"w-4 h-4 text-red-500"})]}),a.jsxs("div",{className:"text-2xl font-bold text-red-800",children:[e.BPM," ",a.jsx("span",{className:"text-sm font-normal",children:"BPM"})]}),a.jsx("div",{className:`text-xs mt-1 ${o.color}`,children:o.label})]}),a.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg",children:[a.jsxs("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("div",{className:"text-sm text-blue-600 font-medium",children:"QRS Duration"}),a.jsx(uc,{className:"w-4 h-4 text-blue-500"})]}),a.jsx("div",{className:"text-2xl font-bold text-blue-800",children:t(e.QRS_duration)}),a.jsx("div",{className:`text-xs mt-1 ${i.color}`,children:i.label})]}),a.jsxs("div",{className:"bg-green-50 p-4 rounded-lg",children:[a.jsxs("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("div",{className:"text-sm text-green-600 font-medium",children:"T Wave Amplitude"}),a.jsx(Wf,{className:"w-4 h-4 text-green-500"})]}),a.jsx("div",{className:"text-2xl font-bold text-green-800",children:n(e.T_amplitude)}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Repolarization"})]}),a.jsxs("div",{className:"bg-purple-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-purple-600 font-medium",children:"P Wave Amplitude"}),a.jsx("div",{className:"text-2xl font-bold text-purple-800",children:n(e.P_amplitude)}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Atrial depolarization"})]}),a.jsxs("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-yellow-600 font-medium",children:"PR Interval"}),a.jsx("div",{className:"text-2xl font-bold text-yellow-800",children:t(e.PR_interval)}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"AV conduction time"})]}),a.jsxs("div",{className:"bg-indigo-50 p-4 rounded-lg",children:[a.jsx("div",{className:"text-sm text-indigo-600 font-medium",children:"QT Interval"}),a.jsx("div",{className:"text-2xl font-bold text-indigo-800",children:t(e.QT_interval)}),a.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"Ventricular activity"})]})]}),a.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg",children:[a.jsx("h4",{className:"text-md font-medium text-gray-800 mb-3",children:"Detection Summary"}),a.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm",children:[a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-bold text-purple-600",children:e.detectedPoints.P_locs.length}),a.jsx("div",{className:"text-gray-600",children:"P waves"})]}),a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-bold text-green-600",children:e.detectedPoints.Q_locs.length}),a.jsx("div",{className:"text-gray-600",children:"Q points"})]}),a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-bold text-red-600",children:e.detectedPoints.R_locs.length}),a.jsx("div",{className:"text-gray-600",children:"R peaks"})]}),a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-bold text-blue-600",children:e.detectedPoints.S_locs.length}),a.jsx("div",{className:"text-gray-600",children:"S points"})]}),a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"text-lg font-bold text-orange-600",children:e.detectedPoints.T_locs.length}),a.jsx("div",{className:"text-gray-600",children:"T waves"})]})]})]})]})};class Jf{static detect(t){const{samples:n,sampleRate:r}=t,l=this.bandpassFilter(n,r,5,15),o=this.differentiate(l).map(m=>m*m),s=Math.round(.15*r),u=this.movingWindowIntegration(o,s),c=this.adaptivePeakDetection(u,r);return this.refinePeaks(n,c,t.timestamps,r)}static bandpassFilter(t,n,r,l){const i=n/2,o=r/i,s=l/i;let u=[...t];const c=Math.exp(-2*Math.PI*o);for(let h=1;h<u.length;h++)u[h]=c*(u[h-1]+t[h]-t[h-1]);const m=Math.exp(-2*Math.PI*s);for(let h=1;h<u.length;h++)u[h]=m*u[h-1]+(1-m)*u[h];return u}static differentiate(t){const n=new Array(t.length);n[0]=0;for(let r=1;r<t.length-1;r++)n[r]=(2*t[r+1]+t[r]-t[r-1]-2*t[r-1])/8;return n[t.length-1]=0,n}static movingWindowIntegration(t,n){const r=new Array(t.length),l=Math.floor(n/2);for(let i=0;i<t.length;i++){const o=Math.max(0,i-l),s=Math.min(t.length,i+l+1);let u=0;for(let c=o;c<s;c++)u+=t[c];r[i]=u/(s-o)}return r}static adaptivePeakDetection(t,n){const r=Math.round(.2*n),l=this.findPeaks(t,r);if(l.length===0)return[];const i=l.slice(0,Math.min(5,l.length));let o=Math.max(...i.map(m=>t[m])),s=i.reduce((m,h)=>m+t[h],0)/i.length*.5,u=s+.25*(o-s);const c=[];for(const m of l){const h=t[m];h>u?(c.push(m),o=.125*h+.875*o):s=.125*h+.875*s,u=s+.25*(o-s)}return c}static findPeaks(t,n){const r=[];for(let l=1;l<t.length-1;l++)t[l]>t[l-1]&&t[l]>t[l+1]&&(r.length===0||l-r[r.length-1]>=n)&&r.push(l);return r}static refinePeaks(t,n,r,l){const i=[],o=Math.round(.075*l);for(const s of n){const u=Math.max(0,s-o),c=Math.min(t.length,s+o);let m=s,h=t[s];for(let g=u;g<c;g++)t[g]>h&&(h=t[g],m=g);const p=this.calculatePeakConfidence(t,m,o);i.push({index:m,time:r[m],amplitude:h,confidence:p})}return i}static calculatePeakConfidence(t,n,r){const l=Math.max(0,n-r),i=Math.min(t.length,n+r),o=t[n];let s=0,u=0;for(let h=l;h<i;h++)h!==n&&(s+=t[h],u++);const c=u>0?s/u:0,m=c!==0?o/Math.abs(c):1;return Math.min(1,Math.max(0,(m-1)/10))}}class qf{static analyze(t,n){if(t.length<10)throw new Error("Insufficient R-peaks for HRV analysis (minimum 10 required)");const r=this.calculateRRIntervals(t),l=this.calculateTimeDomainMetrics(r),i=this.calculateFrequencyDomainMetrics(r,t);return{...l,...i,nnCount:r.length}}static calculateRRIntervals(t){const n=[];for(let r=1;r<t.length;r++){const l=t[r].time-t[r-1].time;n.push(l)}return n}static calculateTimeDomainMetrics(t){const n=t.map(g=>g>10?g:g*1e3),r=n.reduce((g,y)=>g+y,0)/n.length,l=6e4/r,i=n.reduce((g,y)=>g+Math.pow(y-r,2),0)/(n.length-1),o=Math.sqrt(i),s=[];for(let g=1;g<n.length;g++)s.push(n[g]-n[g-1]);const u=Math.sqrt(s.reduce((g,y)=>g+y*y,0)/s.length),m=s.filter(g=>Math.abs(g)>50).length/s.length*100,h=this.calculateTriangularIndex(n),p=this.calculateTINN(n);return{meanHR:l,meanRR:r,sdnn:o,rmssd:u,pnn50:m,triangularIndex:h,tinn:p}}static calculateFrequencyDomainMetrics(t,n){if(t.length<20)return{totalPower:0,vlf:0,lf:0,hf:0,lfhfRatio:0};const r=t.map(f=>f>10?f:f*1e3),l=n.slice(1).map(f=>f.time/1e3),i=r.reduce((f,d)=>f+d,0)/r.length,o=r.map(f=>f-i),{frequencies:s,psd:u}=this.lombScarglePeriodogram(o,l),c=[.003,.04],m=[.04,.15],h=[.15,.4],p=this.integratePowerInBand(u,s,c),g=this.integratePowerInBand(u,s,m),y=this.integratePowerInBand(u,s,h),w=p+g+y,P=y>0?g/y:0;return{totalPower:w,vlf:p,lf:g,hf:y,lfhfRatio:P}}static lombScarglePeriodogram(t,n){const r=t.length;(n[n.length-1]-n[0])/(r-1);const l=.5,i=1/(n[n.length-1]-n[0]),o=[],s=[];for(let u=i;u<=l;u+=i)o.push(u);for(const u of o){const c=2*Math.PI*u;let m=0,h=0;for(let d=0;d<r;d++)m+=Math.sin(2*c*n[d]),h+=Math.cos(2*c*n[d]);const p=Math.atan2(m,h)/(2*c);let g=0,y=0,w=0,P=0;for(let d=0;d<r;d++){const v=c*(n[d]-p),x=c*(n[d]-p);g+=t[d]*Math.cos(v),y+=Math.cos(v)*Math.cos(v),w+=t[d]*Math.sin(x),P+=Math.sin(x)*Math.sin(x)}const f=.5*(g*g/y+w*w/P);s.push(f)}return{frequencies:o,psd:s}}static integratePowerInBand(t,n,r){let l=0;const i=n.length>1?n[1]-n[0]:1;for(let o=0;o<n.length;o++)n[o]>=r[0]&&n[o]<=r[1]&&(l+=t[o]*i);return l}static calculateTriangularIndex(t){const r=this.createHistogram(t,7.8125),l=Math.max(...r);return t.length/(l>0?l:1)}static calculateTINN(t){const r=this.createHistogram(t,7.8125),l=r.indexOf(Math.max(...r));let i=0,o=r.length-1;for(let s=l;s>=0;s--)if(r[s]===0){i=s;break}for(let s=l;s<r.length;s++)if(r[s]===0){o=s;break}return(o-i)*7.8125}static createHistogram(t,n){const r=Math.min(...t),l=Math.max(...t),i=Math.ceil((l-r)/n),o=new Array(i).fill(0);for(const s of t){const u=Math.floor((s-r)/n);u>=0&&u<i&&o[u]++}return o}}class Ks{static denoise(t){const n=this.swtDecomposition(t,3),r=this.calculateAdaptiveThresholds(n.details,t.length),l=n.details.map((i,o)=>this.softThreshold(i,r[o]));return this.swtReconstruction(n.approximation,l)}static swtDecomposition(t,n){const r=[.4829629131,.8365163037,.224143868,-.1294095226],l=[.1294095226,.224143868,-.8365163037,.4829629131];let i=[...t];const o=[];for(let s=0;s<n;s++){const{approx:u,detail:c}=this.swtStep(i,r,l,s);o.push(c),i=u}return{approximation:i,details:o}}static swtStep(t,n,r,l){const i=t.length,o=new Array(i),s=new Array(i),u=Math.pow(2,l);for(let c=0;c<i;c++){let m=0,h=0;for(let p=0;p<n.length;p++){const g=(c-p*u+i)%i;m+=n[p]*t[g],h+=r[p]*t[g]}o[c]=m,s[c]=h}return{approx:o,detail:s}}static calculateAdaptiveThresholds(t,n){const r=[];for(let l=0;l<t.length;l++){const i=t[l],o=this.median(i),s=i.map(h=>Math.abs(h-o)),m=this.median(s)/.6745*Math.sqrt(2*Math.log(n))/Math.log(l+2);r.push(m)}return r}static softThreshold(t,n){return t.map(r=>Math.abs(r)<=n?0:Math.sign(r)*(Math.abs(r)-n))}static swtReconstruction(t,n){const r=[.4829629131,.8365163037,.224143868,-.1294095226],l=[.1294095226,.224143868,-.8365163037,.4829629131];let i=[...t];for(let o=n.length-1;o>=0;o--)i=this.swtReconstructionStep(i,n[o],r,l,o);return i}static swtReconstructionStep(t,n,r,l,i){const o=t.length,s=new Array(o).fill(0),u=Math.pow(2,i);for(let c=0;c<o;c++)for(let m=0;m<r.length;m++){const h=(c+m*u)%o;s[h]+=r[m]*t[c]+l[m]*n[c]}return s}static median(t){const n=[...t].sort((l,i)=>l-i),r=Math.floor(n.length/2);return n.length%2===0?(n[r-1]+n[r])/2:n[r]}static extractWaveletFeatures(t,n=9){const r=this.swtDecomposition(t,n),l=[];for(let i=0;i<r.details.length;i++){const o=r.details[i],s=o.reduce((h,p)=>h+p,0)/o.length,u=this.median(o),c=Math.sqrt(o.reduce((h,p)=>h+Math.pow(p-s,2),0)/o.length),m=this.calculateLogEnergyEntropy(o);l.push(s,u,c,m)}return l}static calculateLogEnergyEntropy(t){const n=t.map(o=>o*o),r=n.reduce((o,s)=>o+s,0);if(r===0)return 0;const l=n.map(o=>o/r);let i=0;for(const o of l)o>0&&(i-=o*Math.log(o));return i}}class bf{static analyze(t,n,r){const l=n.map(m=>m.index);if(l.length<2)return this.getDefaultFeatures(l);const i=this.detectQPeaks(t,l,r),o=this.detectSPeaks(t,l,r),s=this.detectPWaves(t,l,r),u=this.detectTWaves(t,l,r);return{...this.calculateMorphologicalFeatures(t,{P_locs:s,Q_locs:i,R_locs:l,S_locs:o,T_locs:u},r),detectedPoints:{P_locs:s,Q_locs:i,R_locs:l,S_locs:o,T_locs:u}}}static detectQPeaks(t,n,r){const l=[],i=Math.round(.1*r),o=Math.round(.01*r);for(const s of n){const u=Math.max(0,s-i),c=Math.max(0,s-o);if(u>=c)continue;let m=t[u],h=u;for(let p=u;p<c;p++)t[p]<m&&(m=t[p],h=p);l.push(h)}return l}static detectSPeaks(t,n,r){const l=[],i=Math.round(.01*r),o=Math.round(.1*r);for(const s of n){const u=Math.min(t.length-1,s+i),c=Math.min(t.length,s+o);if(u>=c)continue;let m=t[u],h=u;for(let p=u;p<c;p++)t[p]<m&&(m=t[p],h=p);l.push(h)}return l}static detectPWaves(t,n,r){const l=[],i=Math.round(.2*r),o=Math.round(.12*r);for(const s of n){const u=Math.max(0,s-i),c=Math.max(0,s-o);if(u>=c)continue;let m=t[u],h=u;for(let p=u;p<c;p++)t[p]>m&&(m=t[p],h=p);m<t[s]*.5&&l.push(h)}return l}static detectTWaves(t,n,r){const l=[],i=Math.round(.1*r),o=Math.round(.4*r);for(const s of n){const u=Math.min(t.length-1,s+i),c=Math.min(t.length,s+o);if(u>=c)continue;let m=t[u],h=u;for(let p=u;p<c;p++)t[p]>m&&(m=t[p],h=p);l.push(h)}return l}static calculateMorphologicalFeatures(t,n,r){const{P_locs:l,Q_locs:i,R_locs:o,S_locs:s,T_locs:u}=n;let c=0;if(o.length>=2){const w=o.slice(1).reduce((P,f,d)=>P+(f-o[d]),0)/(o.length-1);c=Math.round(60*r/w)}let m=0;i.length===s.length&&i.length>0&&(m=i.reduce((P,f,d)=>P+(s[d]-f),0)/i.length/r);let h=0;u.length>0&&(h=u.reduce((w,P)=>w+t[P],0)/u.length);let p=0;l.length>0&&(p=l.reduce((w,P)=>w+t[P],0)/l.length);let g=0;if(l.length>0&&o.length>0){const w=l.filter((P,f)=>f<o.length);w.length>0&&(g=w.reduce((f,d,v)=>f+(o[v]-d),0)/w.length/r)}let y=0;if(i.length>0&&u.length>0){const w=i.filter((P,f)=>f<u.length);w.length>0&&(y=w.reduce((f,d,v)=>f+(u[v]-d),0)/w.length/r)}return{BPM:c,QRS_duration:m,T_amplitude:h,P_amplitude:p,PR_interval:g,QT_interval:y}}static getDefaultFeatures(t){return{BPM:0,QRS_duration:0,T_amplitude:0,P_amplitude:0,PR_interval:0,QT_interval:0,detectedPoints:{P_locs:[],Q_locs:[],R_locs:t,S_locs:[],T_locs:[]}}}}class Ys{static generateSampleECG(t=10,n=250,r=72){const l=[],i=[],o=t*n,s=60/r;for(let u=0;u<o;u++){const c=u/n;i.push(c*1e3);let m=0;const h=c%s/s;if(h>.3&&h<.4){const p=(h-.3)/.1;m+=2*Math.sin(p*Math.PI)}if(h>.1&&h<.2){const p=(h-.1)/.1;m+=.3*Math.sin(p*Math.PI)}if(h>.5&&h<.7){const p=(h-.5)/.2;m+=.4*Math.sin(p*Math.PI)}m+=.05*(Math.random()-.5),m+=.1*Math.sin(2*Math.PI*.1*c),l.push(m)}return{samples:l,timestamps:i,sampleRate:n}}static generateArrhythmicECG(t=10,n=250){const r=[],l=[],i=t*n,o=75;let s=0;for(let u=0;u<i;u++){const c=u/n;l.push(c*1e3);let m=0;if(c>=s){const g=.2+.3*Math.random(),y=o*(.8+.4*Math.random());s=c+60/y*g}const p=(c-(s-60/o))/(60/o);if(p>=0&&p<=1){if(p>.3&&p<.4){const g=(p-.3)/.1;m+=2*Math.sin(g*Math.PI)}if(p>.1&&p<.2){const g=(p-.1)/.1;m+=.3*Math.sin(g*Math.PI)}if(p>.5&&p<.7){const g=(p-.5)/.2;m+=.4*Math.sin(g*Math.PI)}}m+=.1*(Math.random()-.5),m+=.05*Math.sin(2*Math.PI*.5*c),r.push(m)}return{samples:r,timestamps:l,sampleRate:n}}}function ep(){var g;const[e,t]=se.useState(null),[n,r]=se.useState(null),[l,i]=se.useState(!1),[o,s]=se.useState(!1),[u,c]=se.useState({minPeakDistance:100,adaptiveThreshold:!0,thresholdFactor:.6,windowSize:250}),m=se.useCallback(async()=>{if(!e)return;i(!0);const y=performance.now();try{const w=Ks.denoise(e.samples),P={...e,samples:w},f=Jf.detect(P),d=qf.analyze(f,e.sampleRate),v=f.slice(1).map((q,de)=>q.time-f[de].time),x=bf.analyze(w,f,e.sampleRate),S=Ks.extractWaveletFeatures(w),_=performance.now()-y,N=f.reduce((q,de)=>q+de.confidence,0)/f.length,j=N>.8?"excellent":N>.6?"good":N>.4?"fair":"poor",D=[],M=[];f.length<10&&D.push("Low number of R-peaks detected. Results may be unreliable."),N<.5&&D.push("Low average peak confidence. Consider adjusting detection parameters."),v.length<3&&M.push("Insufficient RR intervals for HRV analysis."),r({rPeaks:f,hrvMetrics:d,rrIntervals:v,filteredSignal:w,denoisedSignal:w,morphologicalFeatures:x,waveletFeatures:S,processingTime:_,quality:j,errors:M,warnings:D})}catch(w){console.error("Analysis error:",w),r({rPeaks:[],hrvMetrics:{},rrIntervals:[],filteredSignal:[],denoisedSignal:[],morphologicalFeatures:null,waveletFeatures:[],processingTime:performance.now()-y,quality:"poor",errors:[w instanceof Error?w.message:"Unknown error occurred"],warnings:[]})}finally{i(!1)}},[e,u]),h=se.useCallback(y=>{const w=new FileReader;w.onload=P=>{var f;try{const x=((f=P.target)==null?void 0:f.result).split(`
`).filter(N=>N.trim()).map(N=>parseFloat(N.split(",")[0])).filter(N=>!isNaN(N));if(x.length===0){alert("No valid data found in file");return}const S=250,_=x.map((N,j)=>j/S*1e3);t({samples:x,timestamps:_,sampleRate:S}),r(null)}catch(d){alert("Error reading file: "+(d instanceof Error?d.message:"Unknown error"))}},w.readAsText(y)},[]),p=se.useCallback(y=>{const w=y==="normal"?Ys.generateSampleECG(30,250,72):Ys.generateArrhythmicECG(30,250);t(w),r(null)},[]);return a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:a.jsxs("div",{className:"flex justify-between items-center py-4",children:[a.jsxs("div",{className:"flex items-center",children:[a.jsx(ac,{className:"w-8 h-8 text-red-500 mr-3"}),a.jsxs("div",{children:[a.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"ECG HRV Analyzer"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Advanced R-peak detection and heart rate variability analysis"})]})]}),a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsx("button",{onClick:()=>p("normal"),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Load Normal ECG"}),a.jsx("button",{onClick:()=>p("arrhythmic"),className:"px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors",children:"Load Arrhythmic ECG"})]})]})})}),a.jsx("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[a.jsxs("div",{className:"lg:col-span-1 space-y-6",children:[a.jsx(Yf,{config:u,onConfigChange:c,onAnalyze:m,onFileUpload:h,isAnalyzing:l}),n&&a.jsx(Xf,{result:n})]}),a.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e&&a.jsxs(a.Fragment,{children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"ECG Signal Analysis"}),a.jsx("div",{className:"flex items-center space-x-4",children:a.jsxs("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:o,onChange:y=>s(y.target.checked),className:"mr-2"}),a.jsx("span",{className:"text-sm text-gray-600",children:"Show Filtered Signal"})]})})]}),a.jsx(Gf,{data:e,rPeaks:(n==null?void 0:n.rPeaks)||[],filteredSignal:n==null?void 0:n.filteredSignal,morphologicalPoints:(g=n==null?void 0:n.morphologicalFeatures)==null?void 0:g.detectedPoints,showFiltered:o,height:400})]}),n&&n.hrvMetrics&&a.jsx(Kf,{metrics:n.hrvMetrics}),n&&n.morphologicalFeatures&&a.jsx(Zf,{features:n.morphologicalFeatures}),!e&&a.jsxs("div",{className:"bg-white rounded-lg shadow-md p-12 text-center",children:[a.jsx(Io,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No ECG Data Loaded"}),a.jsx("p",{className:"text-gray-500 mb-6",children:"Upload an ECG file or load sample data to begin analysis"}),a.jsx("div",{className:"flex justify-center space-x-4",children:a.jsx("button",{onClick:()=>p("normal"),className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Load Sample ECG"})})]})]})]})})]})}sc(document.getElementById("root")).render(a.jsx(se.StrictMode,{children:a.jsx(ep,{})}));
