"""
Example usage of the ECG Processing Platform
Demonstrates R-peak detection and HRV analysis with synthetic and real data
"""

import numpy as np
import matplotlib.pyplot as plt
from ecg_processor import ECGProcessor, HRVAnalyzer
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def generate_synthetic_ecg(duration: float = 30.0, 
                          sampling_rate: float = 360.0,
                          heart_rate: float = 72.0,
                          noise_level: float = 0.1) -> np.ndarray:
    """
    Generate synthetic ECG signal for testing
    
    Args:
        duration: Signal duration in seconds
        sampling_rate: Sampling rate in Hz
        heart_rate: Heart rate in bpm
        noise_level: Noise level (0-1)
        
    Returns:
        Synthetic ECG signal
    """
    t = np.arange(0, duration, 1/sampling_rate)
    
    # Calculate R-R interval
    rr_interval = 60.0 / heart_rate  # seconds
    
    # Generate R-peaks with some variability
    r_peak_times = []
    current_time = 0.5  # Start after 0.5 seconds
    
    while current_time < duration - 0.5:
        r_peak_times.append(current_time)
        # Add some heart rate variability
        hrv = np.random.normal(0, 0.05)  # 50ms std deviation
        current_time += rr_interval + hrv
    
    # Generate ECG signal
    ecg_signal = np.zeros_like(t)
    
    for r_time in r_peak_times:
        # Find closest sample
        r_idx = int(r_time * sampling_rate)
        
        if r_idx < len(t):
            # Add R-peak (QRS complex)
            for i in range(max(0, r_idx-10), min(len(t), r_idx+10)):
                # Simplified QRS shape
                offset = i - r_idx
                if abs(offset) <= 10:
                    if abs(offset) <= 2:  # R-peak
                        ecg_signal[i] += 1.0 * np.exp(-(offset**2) / 2)
                    elif abs(offset) <= 5:  # Q and S waves
                        ecg_signal[i] += -0.3 * np.exp(-(offset**2) / 8)
            
            # Add P-wave (before R-peak)
            p_idx = r_idx - int(0.15 * sampling_rate)
            if p_idx >= 0 and p_idx < len(t):
                for i in range(max(0, p_idx-5), min(len(t), p_idx+5)):
                    offset = i - p_idx
                    if abs(offset) <= 5:
                        ecg_signal[i] += 0.2 * np.exp(-(offset**2) / 10)
            
            # Add T-wave (after R-peak)
            t_idx = r_idx + int(0.25 * sampling_rate)
            if t_idx < len(t):
                for i in range(max(0, t_idx-8), min(len(t), t_idx+8)):
                    offset = i - t_idx
                    if abs(offset) <= 8:
                        ecg_signal[i] += 0.3 * np.exp(-(offset**2) / 20)
    
    # Add noise
    noise = np.random.normal(0, noise_level, len(t))
    ecg_signal += noise
    
    # Add baseline drift
    baseline_drift = 0.1 * np.sin(2 * np.pi * 0.1 * t)
    ecg_signal += baseline_drift
    
    return ecg_signal

def demonstrate_r_peak_detection():
    """
    Demonstrate R-peak detection with synthetic ECG
    """
    print("\n" + "="*50)
    print("R-PEAK DETECTION DEMONSTRATION")
    print("="*50)
    
    # Generate synthetic ECG
    sampling_rate = 360.0
    duration = 30.0
    
    print(f"Generating synthetic ECG: {duration}s at {sampling_rate}Hz")
    ecg_signal = generate_synthetic_ecg(duration=duration, 
                                       sampling_rate=sampling_rate,
                                       heart_rate=75.0,
                                       noise_level=0.1)
    
    # Initialize ECG processor
    processor = ECGProcessor(sampling_rate=sampling_rate)
    
    # Detect R-peaks
    print("Detecting R-peaks...")
    r_peaks, processing_info = processor.detect_r_peaks(ecg_signal, plot_steps=True)
    
    # Validate R-peaks
    print("Validating R-peaks...")
    validated_r_peaks, validation_info = processor.validate_r_peaks(r_peaks, len(ecg_signal))
    
    print(f"Detection Results:")
    print(f"  - Original peaks: {validation_info['original_count']}")
    print(f"  - Validated peaks: {validation_info['final_count']}")
    print(f"  - Removed peaks: {len(validation_info['removed_peaks'])}")
    
    if len(validated_r_peaks) > 1:
        rr_intervals = np.diff(validated_r_peaks) / sampling_rate
        mean_hr = 60.0 / np.mean(rr_intervals)
        print(f"  - Mean heart rate: {mean_hr:.1f} bpm")
    
    return validated_r_peaks, ecg_signal

def demonstrate_hrv_analysis(r_peaks, ecg_signal):
    """
    Demonstrate HRV analysis
    """
    print("\n" + "="*50)
    print("HRV ANALYSIS DEMONSTRATION")
    print("="*50)
    
    # Initialize HRV analyzer
    hrv_analyzer = HRVAnalyzer(sampling_rate=360.0)
    
    # Perform comprehensive HRV analysis
    print("Performing HRV analysis...")
    analysis_results = hrv_analyzer.analyze_hrv(r_peaks, len(ecg_signal))
    
    # Display results
    if analysis_results['status'] == 'success':
        print(f"HRV Analysis Results:")
        print(f"  - Status: {analysis_results['status']}")
        print(f"  - R-peaks: {analysis_results['n_peaks']}")
        print(f"  - Valid RR intervals: {analysis_results['n_valid_rr']}")
        print(f"  - Signal duration: {analysis_results['signal_duration']:.1f}s")
        
        # Time domain features
        td_features = analysis_results['time_domain']
        print(f"\nTime Domain Features:")
        print(f"  - SDNN: {td_features.get('sdnn', 0):.1f} ms")
        print(f"  - RMSSD: {td_features.get('rmssd', 0):.1f} ms")
        print(f"  - pNN50: {td_features.get('pnn50', 0):.1f}%")
        print(f"  - Mean HR: {td_features.get('mean_hr', 0):.1f} bpm")
        print(f"  - CV: {td_features.get('cv', 0):.1f}%")
        
        # Frequency domain features
        fd_features = analysis_results['frequency_domain']
        if fd_features:
            print(f"\nFrequency Domain Features:")
            print(f"  - VLF Power: {fd_features.get('vlf_power', 0):.1f} ms²")
            print(f"  - LF Power: {fd_features.get('lf_power', 0):.1f} ms²")
            print(f"  - HF Power: {fd_features.get('hf_power', 0):.1f} ms²")
            print(f"  - LF/HF Ratio: {fd_features.get('lf_hf_ratio', 0):.2f}")
            print(f"  - Total Power: {fd_features.get('total_power', 0):.1f} ms²")
        
        # Quality metrics
        quality = analysis_results['quality_metrics']
        print(f"\nQuality Metrics:")
        print(f"  - Data Completeness: {quality.get('data_completeness', 0)*100:.1f}%")
        print(f"  - Quality Score: {quality.get('quality_score', 0):.1f}/100")
        print(f"  - Outlier Percentage: {quality.get('outlier_percentage', 0):.1f}%")
        
        # Plot comprehensive HRV analysis
        print("\nGenerating HRV analysis plots...")
        hrv_analyzer.plot_hrv_analysis(analysis_results, r_peaks, ecg_signal)
        
    else:
        print(f"HRV Analysis Failed: {analysis_results.get('error', 'Unknown error')}")
    
    return analysis_results

def demonstrate_error_handling():
    """
    Demonstrate robust error handling
    """
    print("\n" + "="*50)
    print("ERROR HANDLING DEMONSTRATION")
    print("="*50)
    
    processor = ECGProcessor(sampling_rate=360.0)
    hrv_analyzer = HRVAnalyzer(sampling_rate=360.0)
    
    # Test with empty signal
    print("Testing with empty signal...")
    empty_signal = np.array([])
    r_peaks, _ = processor.detect_r_peaks(empty_signal)
    print(f"Empty signal result: {len(r_peaks)} peaks detected")
    
    # Test with very short signal
    print("\nTesting with very short signal...")
    short_signal = np.random.normal(0, 1, 100)  # 100 samples
    r_peaks, _ = processor.detect_r_peaks(short_signal)
    print(f"Short signal result: {len(r_peaks)} peaks detected")
    
    # Test HRV analysis with insufficient peaks
    print("\nTesting HRV analysis with insufficient peaks...")
    insufficient_peaks = np.array([100, 200])  # Only 2 peaks
    analysis_results = hrv_analyzer.analyze_hrv(insufficient_peaks, 1000)
    print(f"Insufficient peaks result: {analysis_results['status']}")
    print(f"Error message: {analysis_results.get('error', 'None')}")
    
    # Test with noisy signal
    print("\nTesting with very noisy signal...")
    noisy_signal = generate_synthetic_ecg(duration=10.0, 
                                         sampling_rate=360.0,
                                         heart_rate=80.0,
                                         noise_level=0.5)  # High noise
    r_peaks, _ = processor.detect_r_peaks(noisy_signal)
    validated_r_peaks, validation_info = processor.validate_r_peaks(r_peaks, len(noisy_signal))
    print(f"Noisy signal: {validation_info['original_count']} -> {validation_info['final_count']} peaks")
    
    if len(validated_r_peaks) > 2:
        analysis_results = hrv_analyzer.analyze_hrv(validated_r_peaks, len(noisy_signal))
        print(f"HRV analysis status: {analysis_results['status']}")
        if analysis_results['status'] == 'success':
            quality_score = analysis_results['quality_metrics'].get('quality_score', 0)
            print(f"Signal quality score: {quality_score:.1f}/100")

def compare_different_conditions():
    """
    Compare HRV analysis results under different physiological conditions
    """
    print("\n" + "="*50)
    print("COMPARISON OF DIFFERENT CONDITIONS")
    print("="*50)
    
    conditions = [
        {"name": "Resting", "heart_rate": 65, "noise": 0.05, "hrv_level": 0.08},
        {"name": "Stress", "heart_rate": 85, "noise": 0.1, "hrv_level": 0.03},
        {"name": "Athletic", "heart_rate": 55, "noise": 0.05, "hrv_level": 0.12},
        {"name": "Elderly", "heart_rate": 70, "noise": 0.08, "hrv_level": 0.05}
    ]
    
    processor = ECGProcessor(sampling_rate=360.0)
    hrv_analyzer = HRVAnalyzer(sampling_rate=360.0)
    
    results_summary = []
    
    for condition in conditions:
        print(f"\nAnalyzing {condition['name']} condition...")
        
        # Generate ECG with specific characteristics
        ecg_signal = generate_synthetic_ecg(
            duration=60.0,  # 1 minute
            sampling_rate=360.0,
            heart_rate=condition['heart_rate'],
            noise_level=condition['noise']
        )
        
        # Detect and validate R-peaks
        r_peaks, _ = processor.detect_r_peaks(ecg_signal)
        validated_r_peaks, _ = processor.validate_r_peaks(r_peaks, len(ecg_signal))
        
        # Perform HRV analysis
        analysis_results = hrv_analyzer.analyze_hrv(validated_r_peaks, len(ecg_signal))
        
        if analysis_results['status'] == 'success':
            td_features = analysis_results['time_domain']
            fd_features = analysis_results['frequency_domain']
            
            summary = {
                'condition': condition['name'],
                'mean_hr': td_features.get('mean_hr', 0),
                'sdnn': td_features.get('sdnn', 0),
                'rmssd': td_features.get('rmssd', 0),
                'pnn50': td_features.get('pnn50', 0),
                'lf_hf_ratio': fd_features.get('lf_hf_ratio', 0) if fd_features else 0,
                'quality_score': analysis_results['quality_metrics'].get('quality_score', 0)
            }
            
            results_summary.append(summary)
            
            print(f"  - Mean HR: {summary['mean_hr']:.1f} bpm")
            print(f"  - SDNN: {summary['sdnn']:.1f} ms")
            print(f"  - RMSSD: {summary['rmssd']:.1f} ms")
            print(f"  - pNN50: {summary['pnn50']:.1f}%")
            print(f"  - LF/HF: {summary['lf_hf_ratio']:.2f}")
            print(f"  - Quality: {summary['quality_score']:.1f}/100")
    
    # Create comparison plot
    if results_summary:
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        conditions_names = [r['condition'] for r in results_summary]
        
        # Plot comparisons
        metrics = [
            ('mean_hr', 'Mean Heart Rate (bpm)', axes[0, 0]),
            ('sdnn', 'SDNN (ms)', axes[0, 1]),
            ('rmssd', 'RMSSD (ms)', axes[0, 2]),
            ('pnn50', 'pNN50 (%)', axes[1, 0]),
            ('lf_hf_ratio', 'LF/HF Ratio', axes[1, 1]),
            ('quality_score', 'Quality Score', axes[1, 2])
        ]
        
        for metric, title, ax in metrics:
            values = [r[metric] for r in results_summary]
            bars = ax.bar(conditions_names, values, alpha=0.7)
            ax.set_title(title)
            ax.set_ylabel('Value')
            ax.tick_params(axis='x', rotation=45)
            
            # Add value labels on bars
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.1f}', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.show()

def main():
    """
    Main demonstration function
    """
    print("ECG SIGNAL PROCESSING PLATFORM")
    print("Advanced R-peak Detection and HRV Analysis")
    print("="*60)
    
    try:
        # Demonstrate R-peak detection
        r_peaks, ecg_signal = demonstrate_r_peak_detection()
        
        # Demonstrate HRV analysis
        if len(r_peaks) > 2:
            demonstrate_hrv_analysis(r_peaks, ecg_signal)
        
        # Demonstrate error handling
        demonstrate_error_handling()
        
        # Compare different conditions
        compare_different_conditions()
        
        print("\n" + "="*60)
        print("DEMONSTRATION COMPLETED SUCCESSFULLY")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Error in demonstration: {e}")
        print(f"Demonstration failed: {e}")

if __name__ == "__main__":
    main()