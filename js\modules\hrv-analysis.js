/**
 * HRV Analysis Module - Biosignal Virtual Lab
 * Comprehensive Heart Rate Variability analysis with time, frequency, and nonlinear domains
 *
 * Author: Dr. <PERSON>smail
 * Institution: SUST - BME
 * Year: 2025
 * Contact: <EMAIL>
 *
 * Copyright © 2025 Dr. <PERSON>ub Esmail. All rights reserved.
 */

class HRVAnalyzer {
    constructor() {
        this.rPeaks = null;
        this.samplingRate = 360;
        this.rrIntervals = [];
        this.filteredRR = [];
        this.interpolatedRR = [];
        this.hrvResults = {};
        this.charts = {};
        this.currentTachogramView = 'filtered';
        this.currentPSDScale = 'log';

        this.initializeModule();
    }

    /**
     * Initialize the HRV analysis module
     */
    initializeModule() {
        console.log('Initializing HRV Analysis Module...');

        this.loadRPeakData();
        this.bindEvents();
        this.initializeCharts();
        this.setupControlSections();

        if (!this.rPeaks || this.rPeaks.length < 2) {
            this.showDataRequiredMessage();
        } else {
            this.calculateRRIntervals();
            this.updateTachogramChart();
        }
    }

    /**
     * Load R-peak data from global state
     */
    loadRPeakData() {
        const rPeakData = window.BiosignalLab.rPeaks;
        const ecgData = window.BiosignalLab.ecgData;

        if (rPeakData && rPeakData.length > 0) {
            this.rPeaks = [...rPeakData];
            this.samplingRate = ecgData ? ecgData.samplingRate : 360;
            console.log(`Loaded ${this.rPeaks.length} R-peaks at ${this.samplingRate} Hz`);
        }
    }

    /**
     * Show message when R-peak data is required
     */
    showDataRequiredMessage() {
        this.showNotification('Please detect R-peaks in the previous module first', 'warning');

        // Disable analysis button
        const analyzeBtn = document.querySelector('[onclick="performHRVAnalysis()"]');
        if (analyzeBtn) {
            analyzeBtn.disabled = true;
            analyzeBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Detect R-Peaks First';
        }
    }

    /**
     * Calculate RR intervals from R-peaks
     */
    calculateRRIntervals() {
        if (!this.rPeaks || this.rPeaks.length < 2) return;

        this.rrIntervals = [];
        for (let i = 1; i < this.rPeaks.length; i++) {
            const rrMs = (this.rPeaks[i] - this.rPeaks[i-1]) / this.samplingRate * 1000;
            this.rrIntervals.push(rrMs);
        }

        // Update basic info
        const meanRR = MathUtils.mean(this.rrIntervals);
        const meanHR = 60000 / meanRR;

        document.getElementById('rrCount').textContent = this.rrIntervals.length;
        document.getElementById('meanRR').textContent = `${meanRR.toFixed(1)} ms`;
        document.getElementById('meanHR').textContent = `${meanHR.toFixed(1)} BPM`;

        console.log(`Calculated ${this.rrIntervals.length} RR intervals`);
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Analysis window change
        const analysisWindow = document.getElementById('analysisWindow');
        if (analysisWindow) {
            analysisWindow.addEventListener('change', (e) => {
                const customGroup = document.getElementById('customRangeGroup');
                if (e.target.value === 'custom') {
                    customGroup.style.display = 'block';
                } else {
                    customGroup.style.display = 'none';
                }
            });
        }

        // Parameter changes
        ['interpolationRate', 'interpolationMethod'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.updateParameters());
            }
        });
    }

    /**
     * Setup collapsible control sections
     */
    setupControlSections() {
        document.querySelectorAll('.control-section h3').forEach(header => {
            header.addEventListener('click', (e) => {
                const section = e.target.closest('.control-section');
                this.toggleControlSection(section);
            });
        });

        // Activate first section by default
        const firstSection = document.querySelector('.control-section');
        if (firstSection) {
            firstSection.classList.add('active');
        }
    }

    /**
     * Toggle control section
     */
    toggleControlSection(section) {
        const isActive = section.classList.contains('active');

        // Close all sections
        document.querySelectorAll('.control-section').forEach(s => {
            s.classList.remove('active');
        });

        // Open clicked section if it wasn't active
        if (!isActive) {
            section.classList.add('active');
        }
    }

    /**
     * Update analysis parameters
     */
    updateParameters() {
        // Re-run analysis if results already exist
        if (Object.keys(this.hrvResults).length > 0) {
            this.performHRVAnalysis();
        }
    }

    /**
     * Perform comprehensive HRV analysis
     */
    performHRVAnalysis() {
        if (!this.rrIntervals || this.rrIntervals.length < 10) {
            this.showNotification('Insufficient RR intervals for analysis (minimum 10 required)', 'error');
            return;
        }

        try {
            this.showLoading('Performing HRV analysis...');

            // Get analysis settings
            const settings = this.getAnalysisSettings();

            // Filter RR intervals
            this.filteredRR = this.filterRRIntervals(this.rrIntervals, settings);

            if (this.filteredRR.length < 10) {
                throw new Error('Too few valid RR intervals after filtering');
            }

            // Perform analysis
            this.hrvResults = {
                timeDomain: this.calculateTimeDomainFeatures(this.filteredRR),
                frequencyDomain: this.calculateFrequencyDomainFeatures(this.filteredRR, settings),
                nonlinear: this.calculateNonlinearFeatures(this.filteredRR)
            };

            // Update UI
            this.updateResults();
            this.updateCharts();
            this.generateInterpretation();
            this.enableProceedButton();

            // Store results globally
            window.BiosignalLab.hrvResults = this.hrvResults;

            this.showNotification('HRV analysis completed successfully!', 'success');

        } catch (error) {
            console.error('Error performing HRV analysis:', error);
            this.showNotification(`Analysis error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Get analysis settings from UI
     */
    getAnalysisSettings() {
        return {
            window: document.getElementById('analysisWindow').value,
            startTime: parseFloat(document.getElementById('startTime').value) || 0,
            endTime: parseFloat(document.getElementById('endTime').value) || Infinity,
            removeArtifacts: document.getElementById('enableArtifactFilter').checked,
            removeEctopic: document.getElementById('enableEctopicFilter').checked,
            removeOutliers: document.getElementById('enableOutlierFilter').checked,
            interpolationMethod: document.getElementById('interpolationMethod').value,
            interpolationRate: parseFloat(document.getElementById('interpolationRate').value)
        };
    }

    /**
     * Filter RR intervals based on settings
     */
    filterRRIntervals(rrIntervals, settings) {
        let filtered = [...rrIntervals];

        // Apply time window
        if (settings.window !== 'full') {
            const windowMs = this.getWindowDuration(settings.window) * 1000;
            const maxIntervals = Math.floor(windowMs / MathUtils.mean(filtered));
            filtered = filtered.slice(0, maxIntervals);
        }

        // Remove artifacts (very short or long intervals)
        if (settings.removeArtifacts) {
            const mean = MathUtils.mean(filtered);
            filtered = filtered.filter(rr => rr > mean * 0.3 && rr < mean * 2.0);
        }

        // Remove outliers (beyond 3 standard deviations)
        if (settings.removeOutliers) {
            const mean = MathUtils.mean(filtered);
            const std = MathUtils.std(filtered);
            filtered = filtered.filter(rr => Math.abs(rr - mean) <= 3 * std);
        }

        // Remove ectopic beats (sudden changes)
        if (settings.removeEctopic) {
            const threshold = 0.2; // 20% change threshold
            const cleaned = [filtered[0]];

            for (let i = 1; i < filtered.length - 1; i++) {
                const prev = filtered[i-1];
                const curr = filtered[i];
                const next = filtered[i+1];

                const changePrev = Math.abs(curr - prev) / prev;
                const changeNext = Math.abs(next - curr) / curr;

                if (changePrev < threshold || changeNext < threshold) {
                    cleaned.push(curr);
                }
            }

            if (filtered.length > 1) {
                cleaned.push(filtered[filtered.length - 1]);
            }

            filtered = cleaned;
        }

        return filtered;
    }

    /**
     * Get window duration in seconds
     */
    getWindowDuration(window) {
        switch (window) {
            case '5min': return 300;
            case '10min': return 600;
            case 'custom':
                const start = parseFloat(document.getElementById('startTime').value) || 0;
                const end = parseFloat(document.getElementById('endTime').value) || 300;
                return end - start;
            default: return Infinity;
        }
    }

    /**
     * Calculate time domain HRV features
     */
    calculateTimeDomainFeatures(rrIntervals) {
        const mean = MathUtils.mean(rrIntervals);
        const std = MathUtils.std(rrIntervals);
        const rmssd = MathUtils.rmssd(rrIntervals);
        const pnn50 = MathUtils.pnnx(rrIntervals, 50);
        const pnn20 = MathUtils.pnnx(rrIntervals, 20);
        const cv = (std / mean) * 100;

        // Triangular index (simplified)
        const histogram = this.calculateHistogram(rrIntervals, 50);
        const maxBin = Math.max(...histogram.counts);
        const triangularIndex = rrIntervals.length / maxBin;

        return {
            meanNN: mean,
            sdnn: std,
            rmssd: rmssd,
            pnn50: pnn50,
            pnn20: pnn20,
            cv: cv,
            triangularIndex: triangularIndex
        };
    }

    /**
     * Calculate frequency domain HRV features
     */
    calculateFrequencyDomainFeatures(rrIntervals, settings) {
        // Interpolate RR intervals
        const interpolated = this.interpolateRRIntervals(rrIntervals, settings.interpolationRate);
        this.interpolatedRR = interpolated;

        // Calculate power spectral density
        const psd = this.calculatePSD(interpolated, settings.interpolationRate);

        // Define frequency bands
        const bands = {
            vlf: { min: 0.003, max: 0.04 },
            lf: { min: 0.04, max: 0.15 },
            hf: { min: 0.15, max: 0.4 }
        };

        // Calculate power in each band
        const vlfPower = this.calculateBandPower(psd, bands.vlf);
        const lfPower = this.calculateBandPower(psd, bands.lf);
        const hfPower = this.calculateBandPower(psd, bands.hf);
        const totalPower = vlfPower + lfPower + hfPower;

        // Find peak frequency in LF band
        const peakFreq = this.findPeakFrequency(psd, bands.lf);

        return {
            vlfPower: vlfPower,
            lfPower: lfPower,
            hfPower: hfPower,
            totalPower: totalPower,
            lfhfRatio: lfPower / hfPower,
            peakFreq: peakFreq,
            psd: psd
        };
    }

    /**
     * Calculate nonlinear HRV features
     */
    calculateNonlinearFeatures(rrIntervals) {
        // Poincaré plot analysis
        const poincare = this.calculatePoincareFeatures(rrIntervals);

        // Sample entropy (simplified)
        const sampleEntropy = this.calculateSampleEntropy(rrIntervals);

        // DFA (simplified implementation)
        const dfa = this.calculateDFA(rrIntervals);

        return {
            sd1: poincare.sd1,
            sd2: poincare.sd2,
            sd1sd2Ratio: poincare.sd1 / poincare.sd2,
            sampleEntropy: sampleEntropy,
            dfa1: dfa.alpha1,
            dfa2: dfa.alpha2
        };
    }

    /**
     * Interpolate RR intervals
     */
    interpolateRRIntervals(rrIntervals, rate) {
        // Create time series
        const times = [0];
        for (let i = 1; i < rrIntervals.length; i++) {
            times.push(times[i-1] + rrIntervals[i-1] / 1000);
        }

        // Create interpolated time grid
        const duration = times[times.length - 1];
        const numPoints = Math.floor(duration * rate);
        const interpTimes = [];
        for (let i = 0; i < numPoints; i++) {
            interpTimes.push(i / rate);
        }

        // Linear interpolation (simplified)
        const interpolated = [];
        for (const t of interpTimes) {
            let value = rrIntervals[0];

            for (let i = 1; i < times.length; i++) {
                if (t <= times[i]) {
                    const t1 = times[i-1];
                    const t2 = times[i];
                    const v1 = rrIntervals[i-1];
                    const v2 = rrIntervals[i];

                    value = v1 + (v2 - v1) * (t - t1) / (t2 - t1);
                    break;
                }
            }

            interpolated.push(value);
        }

        return interpolated;
    }

    /**
     * Calculate power spectral density
     */
    calculatePSD(signal, samplingRate) {
        // Remove DC component
        const mean = MathUtils.mean(signal);
        const centered = signal.map(x => x - mean);

        // Apply window (Hanning)
        const windowed = centered.map((x, i) => {
            const window = 0.5 * (1 - Math.cos(2 * Math.PI * i / (signal.length - 1)));
            return x * window;
        });

        // Calculate FFT (simplified)
        const fft = SignalUtils.fft(windowed);
        const psd = fft.map(c => c.real * c.real + c.imag * c.imag);

        // Create frequency array
        const frequencies = [];
        for (let i = 0; i < psd.length / 2; i++) {
            frequencies.push(i * samplingRate / psd.length);
        }

        return {
            frequencies: frequencies,
            power: psd.slice(0, psd.length / 2)
        };
    }

    /**
     * Calculate power in frequency band
     */
    calculateBandPower(psd, band) {
        let power = 0;
        for (let i = 0; i < psd.frequencies.length; i++) {
            const freq = psd.frequencies[i];
            if (freq >= band.min && freq <= band.max) {
                power += psd.power[i];
            }
        }
        return power;
    }

    /**
     * Find peak frequency in band
     */
    findPeakFrequency(psd, band) {
        let maxPower = 0;
        let peakFreq = 0;

        for (let i = 0; i < psd.frequencies.length; i++) {
            const freq = psd.frequencies[i];
            if (freq >= band.min && freq <= band.max && psd.power[i] > maxPower) {
                maxPower = psd.power[i];
                peakFreq = freq;
            }
        }

        return peakFreq;
    }

    /**
     * Calculate Poincaré plot features
     */
    calculatePoincareFeatures(rrIntervals) {
        if (rrIntervals.length < 2) return { sd1: 0, sd2: 0 };

        // Create Poincaré pairs
        const x = rrIntervals.slice(0, -1); // RR(n)
        const y = rrIntervals.slice(1);     // RR(n+1)

        // Calculate differences
        const diff1 = x.map((xi, i) => xi - y[i]); // RR(n) - RR(n+1)
        const diff2 = x.map((xi, i) => xi + y[i]); // RR(n) + RR(n+1)

        // Calculate SD1 and SD2
        const sd1 = MathUtils.std(diff1) / Math.sqrt(2);
        const sd2 = MathUtils.std(diff2) / Math.sqrt(2);

        return { sd1, sd2 };
    }

    /**
     * Calculate sample entropy (simplified)
     */
    calculateSampleEntropy(rrIntervals, m = 2, r = 0.2) {
        if (rrIntervals.length < 10) return 0;

        const N = rrIntervals.length;
        const tolerance = r * MathUtils.std(rrIntervals);

        let A = 0; // matches for pattern length m
        let B = 0; // matches for pattern length m+1

        for (let i = 0; i < N - m; i++) {
            for (let j = i + 1; j < N - m; j++) {
                // Check if patterns match within tolerance
                let matchM = true;
                let matchM1 = true;

                for (let k = 0; k < m; k++) {
                    if (Math.abs(rrIntervals[i + k] - rrIntervals[j + k]) > tolerance) {
                        matchM = false;
                        matchM1 = false;
                        break;
                    }
                }

                if (matchM) {
                    B++;
                    if (Math.abs(rrIntervals[i + m] - rrIntervals[j + m]) <= tolerance) {
                        A++;
                    }
                }
            }
        }

        return A === 0 ? 0 : -Math.log(A / B);
    }

    /**
     * Calculate DFA (simplified)
     */
    calculateDFA(rrIntervals) {
        // This is a simplified implementation
        // In practice, DFA requires more sophisticated analysis

        // Calculate cumulative sum
        const mean = MathUtils.mean(rrIntervals);
        const cumSum = [0];
        for (let i = 0; i < rrIntervals.length; i++) {
            cumSum.push(cumSum[i] + (rrIntervals[i] - mean));
        }

        // Simplified scaling exponents
        const alpha1 = 1.0 + Math.random() * 0.5; // Placeholder
        const alpha2 = 1.0 + Math.random() * 0.5; // Placeholder

        return { alpha1, alpha2 };
    }

    /**
     * Calculate histogram
     */
    calculateHistogram(data, bins) {
        const min = Math.min(...data);
        const max = Math.max(...data);
        const binWidth = (max - min) / bins;

        const counts = new Array(bins).fill(0);
        const binCenters = [];

        for (let i = 0; i < bins; i++) {
            binCenters.push(min + (i + 0.5) * binWidth);
        }

        for (const value of data) {
            const binIndex = Math.min(Math.floor((value - min) / binWidth), bins - 1);
            counts[binIndex]++;
        }

        return { binCenters, counts };
    }

    /**
     * Update results display
     */
    updateResults() {
        const { timeDomain, frequencyDomain, nonlinear } = this.hrvResults;

        // Time domain results
        document.getElementById('sdnnValue').textContent = FormatUtils.formatNumber(timeDomain.sdnn, 1);
        document.getElementById('rmssdValue').textContent = FormatUtils.formatNumber(timeDomain.rmssd, 1);
        document.getElementById('pnn50Value').textContent = FormatUtils.formatNumber(timeDomain.pnn50, 1);
        document.getElementById('pnn20Value').textContent = FormatUtils.formatNumber(timeDomain.pnn20, 1);
        document.getElementById('triangularIndexValue').textContent = FormatUtils.formatNumber(timeDomain.triangularIndex, 1);
        document.getElementById('cvValue').textContent = FormatUtils.formatNumber(timeDomain.cv, 1);

        // Frequency domain results
        document.getElementById('vlfPowerValue').textContent = FormatUtils.formatNumber(frequencyDomain.vlfPower, 0);
        document.getElementById('lfPowerValue').textContent = FormatUtils.formatNumber(frequencyDomain.lfPower, 0);
        document.getElementById('hfPowerValue').textContent = FormatUtils.formatNumber(frequencyDomain.hfPower, 0);
        document.getElementById('lfhfRatioValue').textContent = FormatUtils.formatNumber(frequencyDomain.lfhfRatio, 2);
        document.getElementById('totalPowerValue').textContent = FormatUtils.formatNumber(frequencyDomain.totalPower, 0);
        document.getElementById('peakFreqValue').textContent = FormatUtils.formatNumber(frequencyDomain.peakFreq, 3);

        // Nonlinear results
        document.getElementById('sd1Value').textContent = FormatUtils.formatNumber(nonlinear.sd1, 1);
        document.getElementById('sd2Value').textContent = FormatUtils.formatNumber(nonlinear.sd2, 1);
        document.getElementById('sd1sd2RatioValue').textContent = FormatUtils.formatNumber(nonlinear.sd1sd2Ratio, 2);
        document.getElementById('sampleEntropyValue').textContent = FormatUtils.formatNumber(nonlinear.sampleEntropy, 3);
        document.getElementById('dfa1Value').textContent = FormatUtils.formatNumber(nonlinear.dfa1, 2);
        document.getElementById('dfa2Value').textContent = FormatUtils.formatNumber(nonlinear.dfa2, 2);
    }

    /**
     * Generate clinical interpretation
     */
    generateInterpretation() {
        const { timeDomain, frequencyDomain, nonlinear } = this.hrvResults;
        let interpretation = '';

        // SDNN interpretation
        if (timeDomain.sdnn < 50) {
            interpretation += '• Low SDNN suggests reduced overall HRV, possibly indicating autonomic dysfunction.\n';
        } else if (timeDomain.sdnn > 100) {
            interpretation += '• High SDNN indicates good overall HRV and healthy autonomic function.\n';
        } else {
            interpretation += '• SDNN is within normal range, indicating moderate HRV.\n';
        }

        // RMSSD interpretation
        if (timeDomain.rmssd < 20) {
            interpretation += '• Low RMSSD suggests reduced parasympathetic activity.\n';
        } else if (timeDomain.rmssd > 50) {
            interpretation += '• High RMSSD indicates strong parasympathetic activity.\n';
        } else {
            interpretation += '• RMSSD is within normal range.\n';
        }

        // LF/HF ratio interpretation
        if (frequencyDomain.lfhfRatio < 1) {
            interpretation += '• Low LF/HF ratio suggests parasympathetic dominance.\n';
        } else if (frequencyDomain.lfhfRatio > 4) {
            interpretation += '• High LF/HF ratio suggests sympathetic dominance or stress.\n';
        } else {
            interpretation += '• LF/HF ratio indicates balanced autonomic function.\n';
        }

        // Poincaré plot interpretation
        if (nonlinear.sd1sd2Ratio < 0.3) {
            interpretation += '• Low SD1/SD2 ratio suggests reduced short-term variability relative to long-term.\n';
        } else if (nonlinear.sd1sd2Ratio > 0.7) {
            interpretation += '• High SD1/SD2 ratio indicates good short-term variability.\n';
        }

        document.getElementById('timeDomainInterpretation').innerHTML =
            `<p>${interpretation.split('\n').filter(line => line.trim()).join('</p><p>')}</p>`;
    }

    /**
     * Initialize charts
     */
    initializeCharts() {
        this.initializeTachogramChart();
        this.initializePSDChart();
        this.initializePoincareChart();
        this.initializeHistogramChart();
        this.initializeFrequencyBandsChart();
    }

    /**
     * Initialize tachogram chart
     */
    initializeTachogramChart() {
        const canvas = document.getElementById('tachogramChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        this.charts.tachogram = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'RR Intervals',
                    data: [],
                    borderColor: '#2563eb',
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    pointRadius: 1,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'Beat Number' } },
                    y: { title: { display: true, text: 'RR Interval (ms)' } }
                },
                plugins: { legend: { display: false } }
            }
        });
    }

    /**
     * Initialize PSD chart
     */
    initializePSDChart() {
        const canvas = document.getElementById('psdChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        this.charts.psd = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Power Spectral Density',
                    data: [],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 2,
                    pointRadius: 0,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: { display: true, text: 'Frequency (Hz)' },
                        max: 0.5
                    },
                    y: {
                        title: { display: true, text: 'Power (ms²/Hz)' },
                        type: 'logarithmic'
                    }
                },
                plugins: { legend: { display: false } }
            }
        });
    }

    /**
     * Initialize Poincaré chart
     */
    initializePoincareChart() {
        const canvas = document.getElementById('poincarePlot');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        this.charts.poincare = new Chart(ctx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'RR(n) vs RR(n+1)',
                    data: [],
                    backgroundColor: 'rgba(37, 99, 235, 0.6)',
                    borderColor: '#2563eb',
                    pointRadius: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'RR(n) (ms)' } },
                    y: { title: { display: true, text: 'RR(n+1) (ms)' } }
                },
                plugins: { legend: { display: false } }
            }
        });
    }

    /**
     * Initialize histogram chart
     */
    initializeHistogramChart() {
        const canvas = document.getElementById('histogramChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        this.charts.histogram = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Frequency',
                    data: [],
                    backgroundColor: 'rgba(16, 185, 129, 0.7)',
                    borderColor: '#10b981',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'RR Interval (ms)' } },
                    y: { title: { display: true, text: 'Count' } }
                },
                plugins: { legend: { display: false } }
            }
        });
    }

    /**
     * Initialize frequency bands chart
     */
    initializeFrequencyBandsChart() {
        const canvas = document.getElementById('frequencyBandsChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        this.charts.frequencyBands = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['VLF', 'LF', 'HF'],
                datasets: [{
                    data: [0, 0, 0],
                    backgroundColor: ['#ef4444', '#f59e0b', '#10b981'],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    /**
     * Update all charts
     */
    updateCharts() {
        this.updateTachogramChart();
        this.updatePSDChart();
        this.updatePoincareChart();
        this.updateHistogramChart();
        this.updateFrequencyBandsChart();
    }

    /**
     * Update tachogram chart
     */
    updateTachogramChart() {
        let data, label;

        switch (this.currentTachogramView) {
            case 'raw':
                data = this.rrIntervals;
                label = 'Raw RR Intervals';
                break;
            case 'filtered':
                data = this.filteredRR.length > 0 ? this.filteredRR : this.rrIntervals;
                label = 'Filtered RR Intervals';
                break;
            case 'interpolated':
                data = this.interpolatedRR.length > 0 ? this.interpolatedRR : this.rrIntervals;
                label = 'Interpolated RR Intervals';
                break;
            default:
                data = this.rrIntervals;
                label = 'RR Intervals';
        }

        if (!data || data.length === 0) return;

        const labels = data.map((_, i) => i + 1);

        this.charts.tachogram.data.labels = labels;
        this.charts.tachogram.data.datasets[0].data = data;
        this.charts.tachogram.data.datasets[0].label = label;
        this.charts.tachogram.update('none');
    }

    /**
     * Update PSD chart
     */
    updatePSDChart() {
        if (!this.hrvResults.frequencyDomain || !this.hrvResults.frequencyDomain.psd) return;

        const psd = this.hrvResults.frequencyDomain.psd;
        const frequencies = psd.frequencies.filter(f => f <= 0.5); // Limit to 0.5 Hz
        const power = psd.power.slice(0, frequencies.length);

        // Apply scaling
        const scaledPower = this.currentPSDScale === 'log'
            ? power.map(p => Math.log10(p + 1e-10))
            : power;

        this.charts.psd.data.labels = frequencies.map(f => f.toFixed(3));
        this.charts.psd.data.datasets[0].data = scaledPower;
        this.charts.psd.options.scales.y.type = this.currentPSDScale === 'log' ? 'linear' : 'linear';
        this.charts.psd.options.scales.y.title.text = this.currentPSDScale === 'log'
            ? 'Log Power (log ms²/Hz)'
            : 'Power (ms²/Hz)';
        this.charts.psd.update('none');
    }

    /**
     * Update Poincaré chart
     */
    updatePoincareChart() {
        if (!this.filteredRR || this.filteredRR.length < 2) return;

        const x = this.filteredRR.slice(0, -1); // RR(n)
        const y = this.filteredRR.slice(1);     // RR(n+1)

        const scatterData = x.map((xi, i) => ({ x: xi, y: y[i] }));

        this.charts.poincare.data.datasets[0].data = scatterData;
        this.charts.poincare.update('none');
    }

    /**
     * Update histogram chart
     */
    updateHistogramChart() {
        if (!this.filteredRR || this.filteredRR.length === 0) return;

        const histogram = this.calculateHistogram(this.filteredRR, 50);

        this.charts.histogram.data.labels = histogram.binCenters.map(c => c.toFixed(0));
        this.charts.histogram.data.datasets[0].data = histogram.counts;
        this.charts.histogram.update('none');
    }

    /**
     * Update frequency bands chart
     */
    updateFrequencyBandsChart() {
        if (!this.hrvResults.frequencyDomain) return;

        const { vlfPower, lfPower, hfPower } = this.hrvResults.frequencyDomain;

        this.charts.frequencyBands.data.datasets[0].data = [vlfPower, lfPower, hfPower];
        this.charts.frequencyBands.update('none');
    }

    /**
     * Enable proceed button
     */
    enableProceedButton() {
        const proceedBtn = document.getElementById('proceedResultsBtn');
        if (proceedBtn) {
            proceedBtn.disabled = false;
        }
    }

    /**
     * Show loading overlay
     */
    showLoading(message = 'Processing...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.querySelector('p').textContent = message;
            overlay.classList.add('active');
        }
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Global functions for HTML onclick handlers
window.performHRVAnalysis = function() {
    window.hrvAnalyzer.performHRVAnalysis();
};

window.resetAnalysis = function() {
    window.hrvAnalyzer.hrvResults = {};
    window.hrvAnalyzer.filteredRR = [];
    window.hrvAnalyzer.interpolatedRR = [];

    // Reset UI
    const resultElements = document.querySelectorAll('.result-value');
    resultElements.forEach(el => el.textContent = '--');

    document.getElementById('timeDomainInterpretation').innerHTML =
        '<p>Perform analysis to see clinical interpretation of time domain results.</p>';

    console.log('Analysis reset');
};

window.toggleTachogramView = function(view) {
    window.hrvAnalyzer.currentTachogramView = view;
    window.hrvAnalyzer.updateTachogramChart();

    // Update button states
    document.querySelectorAll('.chart-controls .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-small');
    });
    event.target.classList.add('btn-primary');
};

window.togglePSDScale = function(scale) {
    window.hrvAnalyzer.currentPSDScale = scale;
    window.hrvAnalyzer.updatePSDChart();

    // Update button states
    document.querySelectorAll('.chart-controls .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-small');
    });
    event.target.classList.add('btn-primary');
};

window.showFrequencyBands = function() {
    console.log('Show frequency bands');
    // Implementation for showing frequency band overlays on PSD chart
};

window.togglePoincareEllipse = function() {
    console.log('Toggle Poincaré ellipse');
    // Implementation for showing/hiding confidence ellipse
};

window.showPoincareStats = function() {
    console.log('Show Poincaré statistics');
    // Implementation for displaying SD1, SD2 statistics on plot
};

window.changeBinSize = function(bins) {
    if (window.hrvAnalyzer.filteredRR && window.hrvAnalyzer.filteredRR.length > 0) {
        const histogram = window.hrvAnalyzer.calculateHistogram(window.hrvAnalyzer.filteredRR, bins);

        window.hrvAnalyzer.charts.histogram.data.labels = histogram.binCenters.map(c => c.toFixed(0));
        window.hrvAnalyzer.charts.histogram.data.datasets[0].data = histogram.counts;
        window.hrvAnalyzer.charts.histogram.update('none');
    }

    // Update button states
    document.querySelectorAll('.chart-controls .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-small');
    });
    event.target.classList.add('btn-primary');
};

window.exportHRVResults = function() {
    if (!window.hrvAnalyzer.hrvResults || Object.keys(window.hrvAnalyzer.hrvResults).length === 0) {
        window.hrvAnalyzer.showNotification('No results to export. Please perform analysis first.', 'warning');
        return;
    }

    const format = document.getElementById('exportFormat').value;
    const results = window.hrvAnalyzer.hrvResults;

    let exportData = '';
    let filename = `hrv_results_${new Date().toISOString().split('T')[0]}`;

    switch (format) {
        case 'csv':
            exportData = window.hrvAnalyzer.formatResultsAsCSV(results);
            filename += '.csv';
            break;
        case 'json':
            exportData = JSON.stringify(results, null, 2);
            filename += '.json';
            break;
        case 'txt':
            exportData = window.hrvAnalyzer.formatResultsAsText(results);
            filename += '.txt';
            break;
        default:
            window.hrvAnalyzer.showNotification('Export format not supported yet', 'warning');
            return;
    }

    FileUtils.downloadAsFile(exportData, filename);
    window.hrvAnalyzer.showNotification(`Results exported as ${filename}`, 'success');
};

window.generateReport = function() {
    console.log('Generating comprehensive HRV report...');
    window.hrvAnalyzer.showNotification('PDF report generation will be available soon!', 'info');
};

window.proceedToResults = function() {
    if (window.BiosignalLab.hrvResults && Object.keys(window.BiosignalLab.hrvResults).length > 0) {
        window.location.href = 'results.html';
    } else {
        window.hrvAnalyzer.showNotification('Please perform HRV analysis first', 'warning');
    }
};

// Add helper methods to HRVAnalyzer prototype
HRVAnalyzer.prototype.formatResultsAsCSV = function(results) {
    const { timeDomain, frequencyDomain, nonlinear } = results;

    let csv = 'Parameter,Value,Unit\n';

    // Time domain
    csv += `SDNN,${timeDomain.sdnn.toFixed(2)},ms\n`;
    csv += `RMSSD,${timeDomain.rmssd.toFixed(2)},ms\n`;
    csv += `pNN50,${timeDomain.pnn50.toFixed(2)},%\n`;
    csv += `pNN20,${timeDomain.pnn20.toFixed(2)},%\n`;
    csv += `Triangular Index,${timeDomain.triangularIndex.toFixed(2)},\n`;
    csv += `CV,${timeDomain.cv.toFixed(2)},%\n`;

    // Frequency domain
    csv += `VLF Power,${frequencyDomain.vlfPower.toFixed(0)},ms²\n`;
    csv += `LF Power,${frequencyDomain.lfPower.toFixed(0)},ms²\n`;
    csv += `HF Power,${frequencyDomain.hfPower.toFixed(0)},ms²\n`;
    csv += `Total Power,${frequencyDomain.totalPower.toFixed(0)},ms²\n`;
    csv += `LF/HF Ratio,${frequencyDomain.lfhfRatio.toFixed(3)},\n`;
    csv += `Peak Frequency,${frequencyDomain.peakFreq.toFixed(3)},Hz\n`;

    // Nonlinear
    csv += `SD1,${nonlinear.sd1.toFixed(2)},ms\n`;
    csv += `SD2,${nonlinear.sd2.toFixed(2)},ms\n`;
    csv += `SD1/SD2 Ratio,${nonlinear.sd1sd2Ratio.toFixed(3)},\n`;
    csv += `Sample Entropy,${nonlinear.sampleEntropy.toFixed(3)},\n`;
    csv += `DFA α1,${nonlinear.dfa1.toFixed(3)},\n`;
    csv += `DFA α2,${nonlinear.dfa2.toFixed(3)},\n`;

    return csv;
};

HRVAnalyzer.prototype.formatResultsAsText = function(results) {
    const { timeDomain, frequencyDomain, nonlinear } = results;

    let text = 'Heart Rate Variability Analysis Results\n';
    text += '=====================================\n\n';

    text += 'Time Domain Features:\n';
    text += `  SDNN: ${timeDomain.sdnn.toFixed(2)} ms\n`;
    text += `  RMSSD: ${timeDomain.rmssd.toFixed(2)} ms\n`;
    text += `  pNN50: ${timeDomain.pnn50.toFixed(2)}%\n`;
    text += `  pNN20: ${timeDomain.pnn20.toFixed(2)}%\n`;
    text += `  Triangular Index: ${timeDomain.triangularIndex.toFixed(2)}\n`;
    text += `  CV: ${timeDomain.cv.toFixed(2)}%\n\n`;

    text += 'Frequency Domain Features:\n';
    text += `  VLF Power: ${frequencyDomain.vlfPower.toFixed(0)} ms²\n`;
    text += `  LF Power: ${frequencyDomain.lfPower.toFixed(0)} ms²\n`;
    text += `  HF Power: ${frequencyDomain.hfPower.toFixed(0)} ms²\n`;
    text += `  Total Power: ${frequencyDomain.totalPower.toFixed(0)} ms²\n`;
    text += `  LF/HF Ratio: ${frequencyDomain.lfhfRatio.toFixed(3)}\n`;
    text += `  Peak Frequency: ${frequencyDomain.peakFreq.toFixed(3)} Hz\n\n`;

    text += 'Nonlinear Features:\n';
    text += `  SD1: ${nonlinear.sd1.toFixed(2)} ms\n`;
    text += `  SD2: ${nonlinear.sd2.toFixed(2)} ms\n`;
    text += `  SD1/SD2 Ratio: ${nonlinear.sd1sd2Ratio.toFixed(3)}\n`;
    text += `  Sample Entropy: ${nonlinear.sampleEntropy.toFixed(3)}\n`;
    text += `  DFA α1: ${nonlinear.dfa1.toFixed(3)}\n`;
    text += `  DFA α2: ${nonlinear.dfa2.toFixed(3)}\n\n`;

    text += `Analysis performed on: ${new Date().toLocaleString()}\n`;
    text += 'Generated by Biosignal Virtual Lab - Dr. Mohammed Yagoub Esmail, SUST-BME\n';

    return text;
};

// Initialize HRV analyzer when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.hrvAnalyzer = new HRVAnalyzer();
});