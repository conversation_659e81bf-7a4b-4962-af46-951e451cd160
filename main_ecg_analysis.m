% =========================================================================
% الملف الرئيسي: main_ecg_analysis.m
% =========================================================================
% هذا هو السكريبت الرئيسي الذي يقوم بتشغيل عملية تحليل إشارة ECG بأكملها.
% 1. تحميل وإعداد البيانات.
% 2. استدعاء وظيفة المعالجة الرئيسية لاستخلاص السمات.
% 3. عرض النتائج وتصويرها.
% -------------------------------------------------------------------------

clear; clc; close all;

% --- الخطوة 1: تحميل وإعداد البيانات ---
% مثال: تحميل بيانات من ملف .mat أو إنشاء إشارة اصطناعية
% For demonstration, we will generate a synthetic ECG signal.
% ملاحظة: لقد تم استبدال الكود الخاص بك لتحميل البيانات بهذا المثال لتسهيل التشغيل.
% قم بإلغاء التعليق على الأسطر الخاصة بك إذا كنت تستخدم بياناتك.

% [signal, ~] = ecgsyn(); % إنشاء إشارة اصطناعية
% Fs = 250; % تردد العينة للإشارة الاصطناعية
% signal = resample(signal, 1000, Fs); % إعادة التعيين إلى 1000 هرتز
% Fs = 1000;
% val = signal(1:10000)'; % خذ 10 ثوانٍ من البيانات
% val = repmat(val, 12, 1); % تكرار الإشارة لإنشاء 12 قناة (للتوافق مع الكود الأصلي)

% --- أو استخدم بياناتك الخاصة ---
% load('your_ecg_data.mat'); % e.g., ECG.data
% val = resample(ECG.data', 2, 1);
% val = val';
% val = val(:, 1:10000); % خذ أول 10000 عينة

% --- بيانات وهمية للتشغيل الفوري ---
Fs = 1000; % تردد العينة
t = 0:1/Fs:10-1/Fs;
val = repmat(ecg(length(t))', 12, 1) + 0.1*randn(12, length(t));
disp('تم إنشاء بيانات ECG وهمية للتجربة.');


% --- الخطوة 2: استدعاء وظيفة المعالجة الرئيسية ---
% هذه الوظيفة تقوم بكل العمليات: التنقية، استخلاص السمات الشكلية والإحصائية
try
    [features, denoised_ecg] = process_ecg_signals(val, Fs);
    disp('تم استخلاص السمات بنجاح.');
    
    % عرض بعض السمات المستخلصة للقناة الأولى
    disp('--- السمات الشكلية للقناة الأولى ---');
    fprintf('متوسط مدة QRS: %.4f ثانية\n', features.morphological.QRS_duration(1));
    fprintf('متوسط سعة موجة T: %.4f\n', features.morphological.T_amplitude(1));
    fprintf('متوسط مدة موجة T: %.4f ثانية\n', features.morphological.T_duration(1));
    fprintf('معدل ضربات القلب: %d نبضة/دقيقة\n', features.morphological.BPM(1));

    % --- الخطوة 3: تصوير النتائج ---
    % تصور الإشارة الأصلية، المنقاة، والنقاط المكتشفة للقناة الأولى
    plot_ecg_analysis(val(1,:), denoised_ecg(1,:), features.morphological.detected_points(1), Fs);

catch ME
    fprintf('حدث خطأ أثناء المعالجة: %s\n', ME.message);
    disp('قد تحتاج إلى ضبط معاملات الكشف عن القمم لتناسب بياناتك.');
end


% =========================================================================
% الملف: process_ecg_signals.m
% =========================================================================
% الوظيفة الرئيسية التي تقوم بتنسيق عملية معالجة إشارة ECG.
% -------------------------------------------------------------------------
function [features, ecg_clean] = process_ecg_signals(ecg_raw, Fs)
    % --- 1. المعالجة الأولية: إزالة التشويش ---
    
    % إزالة انحراف خط الأساس (Baseline Wandering)
    % باستخدام تحويل المويجات المتقطع (DWT) مع 'db6'
    dec_bw = mdwtdec('r', ecg_raw, 10, 'db6');
    a10 = mdwtrec(dec_bw, 'a', 10);
    ecg_bw_removed = ecg_raw - a10;
    
    % إزالة التشويش عالي التردد
    ecg_clean = denoise_swt(ecg_bw_removed);

    % --- 2. استخلاص السمات ---
    
    % استخدام تحويل المويجات المتقطع (DWT) مع 'haar' لتحليل الموجات
    dec_haar = mdwtdec('r', ecg_clean, 9, 'haar');
    
    % استخلاص السمات الشكلية (QRS, T, etc.)
    % نستخدم معاملات التفاصيل من المستوى الثاني للمساعدة في كشف R-peaks
    cd2 = mdwtrec(dec_haar, 'cd', 2);
    [morph_features, detected_points] = extract_morphological_features(ecg_clean, cd2, Fs);
    
    % استخلاص السمات الإحصائية من معاملات المويجات
    stats_features = extract_wavelet_stats(dec_haar);
    
    % --- 3. تجميع كل السمات في هيكل واحد ---
    features.morphological = morph_features;
    features.morphological.detected_points = detected_points;
    features.statistical = stats_features;
end


% =========================================================================
% الملف: extract_morphological_features.m
% =========================================================================
% وظيفة لاستخلاص السمات الشكلية مثل QRS, T-wave من كل قناة.
% -------------------------------------------------------------------------
function [features, all_points] = extract_morphological_features(ecg, cd2, Fs)
    num_leads = size(ecg, 1);
    
    % تهيئة متغيرات لتخزين النتائج لكل قناة
    BPM_all = zeros(1, num_leads);
    QRS_duration_all = zeros(1, num_leads);
    T_amplitude_all = zeros(1, num_leads);
    T_duration_all = zeros(1, num_leads);
    ST_duration_all = zeros(1, num_leads);
    
    all_points = struct('R_locs', [], 'Q_locs', [], 'S_locs', [], 'T_locs', [], 'Ton_locs', [], 'Toff_locs', []);
    all_points = repmat(all_points, num_leads, 1);

    for j = 1:num_leads
        ecg_lead = ecg(j, :);
        cd2_lead = cd2(j, :);
        
        % --- كشف R-peaks ---
        % استخدام معاملات cd2 لتحديد مواقع R-peaks الأولية
        mx = max(cd2_lead);
        threshold_mx = 0.6 * mx;
        possible_r_locs = find(cd2_lead >= threshold_mx);
        
        % فلترة القمم لضمان وجود مسافة كافية بينها (على الأقل 0.2 ثانية)
        min_dist = round(0.2 * Fs);
        r_locs_cd2 = [];
        if ~isempty(possible_r_locs)
            last_loc = possible_r_locs(1);
            r_locs_cd2 = [last_loc];
            for i = 2:length(possible_r_locs)
                if (possible_r_locs(i) > (last_loc + min_dist/4)) % Scale down for cd2
                    last_loc = possible_r_locs(i);
                    r_locs_cd2 = [r_locs_cd2 last_loc];
                end
            end
        end
        
        % تحديد الموقع الدقيق لـ R-peak في الإشارة الأصلية
        r_locs_scaled = r_locs_cd2 * 4; % تحجيم المواقع لتناسب الإشارة الأصلية
        r_locs = [];
        for i = 1:length(r_locs_scaled)
            search_win = r_locs_scaled(i)-round(0.05*Fs) : r_locs_scaled(i)+round(0.05*Fs);
            search_win = search_win(search_win > 0 & search_win <= length(ecg_lead));
            if isempty(search_win), continue; end
            [~, max_idx] = max(ecg_lead(search_win));
            r_locs = [r_locs, search_win(max_idx)];
        end
        
        if isempty(r_locs), continue; end % انتقل للقناة التالية إذا لم يتم العثور على قمم

        % --- كشف Q و S peaks ---
        q_locs = [];
        s_locs = [];
        for i = 1:length(r_locs)
            % البحث عن Q
            q_win = r_locs(i)-round(0.1*Fs) : r_locs(i)-round(0.01*Fs);
            q_win = q_win(q_win > 0);
            if ~isempty(q_win)
                [~, min_idx] = min(ecg_lead(q_win));
                q_locs = [q_locs, q_win(min_idx)];
            end
            
            % البحث عن S
            s_win = r_locs(i)+round(0.01*Fs) : r_locs(i)+round(0.1*Fs);
            s_win = s_win(s_win <= length(ecg_lead));
            if ~isempty(s_win)
                [~, min_idx] = min(ecg_lead(s_win));
                s_locs = [s_locs, s_win(min_idx)];
            end
        end

        % --- كشف T-wave ---
        t_locs = [];
        for i = 1:length(r_locs)
            t_win = r_locs(i)+round(0.1*Fs) : r_locs(i)+round(0.4*Fs);
            t_win = t_win(t_win <= length(ecg_lead));
            if ~isempty(t_win)
                [~, max_idx] = max(ecg_lead(t_win));
                t_locs = [t_locs, t_win(max_idx)];
            end
        end

        % --- حساب السمات الشكلية ---
        % معدل ضربات القلب (BPM)
        if length(r_locs) > 1
            BPM_all(j) = round(60 / (mean(diff(r_locs)) / Fs));
        end
        
        % مدة QRS
        if length(q_locs) == length(s_locs) && ~isempty(q_locs)
            qrs_dur_samples = s_locs - q_locs;
            QRS_duration_all(j) = mean(qrs_dur_samples) / Fs;
        end
        
        % سعة T-wave
        if ~isempty(t_locs)
            T_amplitude_all(j) = mean(ecg_lead(t_locs));
        end
        
        % مدة T-wave (تقريبية)
        % هنا يمكن إضافة خوارزمية أكثر دقة لكشف بداية ونهاية T-wave
        % للتبسيط، سنستخدم قيمة ثابتة أو نتركها صفراً
        T_duration_all(j) = 0.150; % قيمة تقديرية بالثواني
        
        % مدة ST (تقريبية)
        ST_duration_all(j) = 0.120; % قيمة تقديرية بالثواني

        % تخزين النقاط المكتشفة
        all_points(j).R_locs = r_locs;
        all_points(j).Q_locs = q_locs;
        all_points(j).S_locs = s_locs;
        all_points(j).T_locs = t_locs;
    end
    
    % تجميع السمات في هيكل واحد
    features.BPM = BPM_all;
    features.QRS_duration = QRS_duration_all;
    features.T_amplitude = T_amplitude_all;
    features.T_duration = T_duration_all;
    features.ST_duration = ST_duration_all;
end


% =========================================================================
% الملف: extract_wavelet_stats.m
% =========================================================================
% وظيفة لاستخلاص السمات الإحصائية من معاملات المويجات.
% -------------------------------------------------------------------------
function C = extract_wavelet_stats(dec)
    num_leads = size(dec.r, 1);
    num_levels = dec.level;
    
    C_all_levels = [];
    
    for level = 1:num_levels
        cd = mdwtrec(dec, 'cd', level); % استخلاص معاملات التفاصيل للمستوى الحالي
        
        mean_val = mean(cd, 2);
        median_val = median(cd, 2);
        std_val = std(cd, 0, 2);
        
        % حساب إنتروبيا الطاقة اللوغاريتمية لكل قناة
        entropy_val = zeros(num_leads, 1);
        for lead = 1:num_leads
            entropy_val(lead) = wentropy(cd(lead, :), 'log energy');
        end
        
        C_level = [mean_val, median_val, std_val, entropy_val];
        C_all_levels = [C_all_levels, C_level];
    end
    
    C = C_all_levels;
end


% =========================================================================
% الملف: denoise_swt.m
% =========================================================================
% وظيفة لإزالة التشويش باستخدام تحويل المويجات الثابت (SWT).
% -------------------------------------------------------------------------
function X = denoise_swt(ecg)
    X = zeros(size(ecg));
    N = size(ecg, 2);
    num_leads = size(ecg, 1);

    for i = 1:num_leads
        [app, detail] = swt(ecg(i, :), 3, 'db4');
        
        % حساب العتبة لكل مستوى تفاصيل
        thresholds = zeros(1, 3);
        for k = 1:3
            mad = median(abs(detail(k, :) - median(detail(k, :))));
            sigma = mad / 0.6745;
            thresholds(k) = sigma * (sqrt(2 * log(N)) / (log(k + 1))); % تعديل طفيف لتجنب log(1)
        end
        
        % تطبيق العتبة الناعمة (Soft Thresholding)
        detail(1, :) = wthresh(detail(1, :), 's', thresholds(1));
        detail(2, :) = wthresh(detail(2, :), 's', thresholds(2));
        detail(3, :) = wthresh(detail(3, :), 's', thresholds(3));
        
        % إعادة بناء الإشارة المنقاة
        X(i, :) = iswt(app, detail, 'db4');
    end
end


% =========================================================================
% الملف: plot_ecg_analysis.m
% =========================================================================
% وظيفة لتصوير نتائج تحليل ECG.
% -------------------------------------------------------------------------
function plot_ecg_analysis(raw_signal, denoised_signal, points, Fs)
    figure('Name', 'نتائج تحليل إشارة ECG', 'Position', [100, 100, 1200, 800]);
    t = (0:length(raw_signal)-1) / Fs;

    % --- الرسمة الأولى: مقارنة الإشارة الأصلية والمنقاة ---
    subplot(2, 1, 1);
    plot(t, raw_signal, 'b-', 'DisplayName', 'الإشارة الأصلية');
    hold on;
    plot(t, denoised_signal, 'r-', 'LineWidth', 1.5, 'DisplayName', 'الإشارة بعد التنقية');
    title('مقارنة بين الإشارة الأصلية والإشارة المنقاة');
    xlabel('الزمن (ثانية)');
    ylabel('السعة');
    legend show;
    grid on;
    xlim([t(1), t(end)]);

    % --- الرسمة الثانية: الإشارة المنقاة مع النقاط المكتشفة ---
    subplot(2, 1, 2);
    plot(t, denoised_signal, 'k-', 'DisplayName', 'الإشارة المنقاة');
    hold on;
    
    % رسم النقاط المكتشفة إذا كانت موجودة
    if ~isempty(points.R_locs)
        plot(t(points.R_locs), denoised_signal(points.R_locs), 'rv', 'MarkerFaceColor', 'r', 'DisplayName', 'R-peaks');
    end
    if ~isempty(points.Q_locs)
        plot(t(points.Q_locs), denoised_signal(points.Q_locs), 'g>', 'MarkerFaceColor', 'g', 'DisplayName', 'Q-peaks');
    end
    if ~isempty(points.S_locs)
        plot(t(points.S_locs), denoised_signal(points.S_locs), 'b^', 'MarkerFaceColor', 'b', 'DisplayName', 'S-peaks');
    end
    if ~isempty(points.T_locs)
        plot(t(points.T_locs), denoised_signal(points.T_locs), 'mo', 'MarkerFaceColor', 'm', 'DisplayName', 'T-peaks');
    end
    
    title('النقاط المميزة (Q, R, S, T) على الإشارة المنقاة');
    xlabel('الزمن (ثانية)');
    ylabel('السعة');
    legend show;
    grid on;
    xlim([t(1), t(end)]);
end
