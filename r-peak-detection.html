<!DOCTYPE html>
<!--
    Biosignal Virtual Lab - R-Peak Detection Module

    Author: Dr. <PERSON>smail
    Institution: SUST - BME (Sudan University of Science and Technology - Biomedical Engineering)
    Year: 2025

    Contact Information:
    Email: <EMAIL>
    Phone: +249912867327, +966538076790

    Copyright © 2025 Dr. <PERSON>smail. All rights reserved.
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>R-Peak Detection - Biosignal Virtual Lab</title>
    <meta name="author" content="Dr. <PERSON>smail">
    <meta name="copyright" content="© 2025 Dr. <PERSON>">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/modules.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-heartbeat"></i>
                <span>Biosignal Virtual Lab</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="signal-processing.html" class="nav-link">Signal Processing</a>
                <a href="r-peak-detection.html" class="nav-link active">R-Peak Detection</a>
                <a href="hrv-analysis.html" class="nav-link">HRV Analysis</a>
                <a href="results.html" class="nav-link">Results</a>
                <div class="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumbs -->
    <div class="breadcrumbs-container">
        <div class="container">
            <nav class="breadcrumbs">
                <a href="index.html">Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="signal-processing.html">Signal Processing</a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current">R-Peak Detection</span>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <i class="fas fa-search-plus"></i>
                    <h1>R-Peak Detection</h1>
                </div>
                <p class="page-description">
                    Detect R-peaks in ECG signals using the Pan-Tompkins algorithm with adaptive thresholding and validation.
                </p>
            </div>

            <!-- Algorithm Overview -->
            <div class="algorithm-overview">
                <h2>Pan-Tompkins Algorithm Steps</h2>
                <div class="algorithm-steps">
                    <div class="algorithm-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>Bandpass Filter</h3>
                            <p>5-15 Hz filter to isolate QRS frequency band</p>
                        </div>
                    </div>
                    <div class="algorithm-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>Differentiation</h3>
                            <p>Emphasize steep slopes of QRS complex</p>
                        </div>
                    </div>
                    <div class="algorithm-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>Squaring</h3>
                            <p>Enhance peaks and make all values positive</p>
                        </div>
                    </div>
                    <div class="algorithm-step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h3>Integration</h3>
                            <p>Moving window integration for smoothing</p>
                        </div>
                    </div>
                    <div class="algorithm-step">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <h3>Thresholding</h3>
                            <p>Adaptive threshold for peak detection</p>
                        </div>
                    </div>
                    <div class="algorithm-step">
                        <div class="step-number">6</div>
                        <div class="step-content">
                            <h3>Back-search</h3>
                            <p>Find actual R-peaks in original signal</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detection Interface -->
            <div class="detection-interface">
                <!-- Control Panel -->
                <div class="control-panel">
                    <!-- Algorithm Parameters -->
                    <div class="control-section active">
                        <h3><i class="fas fa-cogs"></i> Algorithm Parameters</h3>
                        <div class="parameter-controls">
                            <div class="form-group">
                                <label class="form-label">Bandpass Filter Range (Hz)</label>
                                <div class="filter-range">
                                    <div class="range-input">
                                        <label>Low Freq</label>
                                        <input type="number" class="form-input" id="bpLowFreq" value="5" min="1" max="10" step="0.5">
                                    </div>
                                    <div class="range-input">
                                        <label>High Freq</label>
                                        <input type="number" class="form-input" id="bpHighFreq" value="15" min="10" max="30" step="1">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Integration Window (ms)</label>
                                <input type="number" class="form-input" id="integrationWindow" value="150" min="80" max="300" step="10">
                                <small class="form-help">Typical QRS duration: 80-120ms</small>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Refractory Period (ms)</label>
                                <input type="number" class="form-input" id="refractoryPeriod" value="200" min="150" max="400" step="10">
                                <small class="form-help">Minimum time between R-peaks</small>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Threshold Factor</label>
                                <input type="range" class="form-range" id="thresholdFactor" min="0.1" max="1.0" step="0.05" value="0.5">
                                <div class="range-labels">
                                    <span>0.1</span>
                                    <span id="thresholdValue">0.5</span>
                                    <span>1.0</span>
                                </div>
                                <small class="form-help">Higher values = more selective detection</small>
                            </div>
                        </div>
                        
                        <div class="detection-actions">
                            <button type="button" class="btn btn-primary" onclick="detectRPeaks()">
                                <i class="fas fa-play"></i> Detect R-Peaks
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetDetection()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>

                    <!-- Detection Results -->
                    <div class="control-section">
                        <h3><i class="fas fa-chart-bar"></i> Detection Results</h3>
                        <div class="results-summary">
                            <div class="result-card">
                                <div class="result-label">R-Peaks Detected</div>
                                <div class="result-value" id="peakCount">--</div>
                            </div>
                            
                            <div class="result-card">
                                <div class="result-label">Mean Heart Rate</div>
                                <div class="result-value" id="meanHeartRate">-- BPM</div>
                            </div>
                            
                            <div class="result-card">
                                <div class="result-label">RR Interval Range</div>
                                <div class="result-value" id="rrRange">-- ms</div>
                            </div>
                            
                            <div class="result-card">
                                <div class="result-label">Detection Quality</div>
                                <div class="result-value" id="detectionQuality">--</div>
                                <div class="quality-bar">
                                    <div class="quality-fill" id="qualityFill"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="validation-controls">
                            <h4>Peak Validation</h4>
                            <div class="validation-options">
                                <label class="form-label">
                                    <input type="checkbox" id="enablePhysiological" checked> 
                                    Physiological Constraints
                                </label>
                                <label class="form-label">
                                    <input type="checkbox" id="enableMorphology" checked> 
                                    Morphology Validation
                                </label>
                                <label class="form-label">
                                    <input type="checkbox" id="enableTemporal" checked> 
                                    Temporal Consistency
                                </label>
                            </div>
                            
                            <button type="button" class="btn btn-outline" onclick="validatePeaks()">
                                <i class="fas fa-check"></i> Validate Peaks
                            </button>
                        </div>
                    </div>

                    <!-- Manual Correction -->
                    <div class="control-section">
                        <h3><i class="fas fa-edit"></i> Manual Correction</h3>
                        <div class="correction-tools">
                            <div class="tool-buttons">
                                <button type="button" class="btn btn-small" id="addPeakBtn" onclick="enableAddPeak()">
                                    <i class="fas fa-plus"></i> Add Peak
                                </button>
                                <button type="button" class="btn btn-small" id="removePeakBtn" onclick="enableRemovePeak()">
                                    <i class="fas fa-minus"></i> Remove Peak
                                </button>
                                <button type="button" class="btn btn-small" id="movePeakBtn" onclick="enableMovePeak()">
                                    <i class="fas fa-arrows-alt"></i> Move Peak
                                </button>
                            </div>
                            
                            <div class="correction-info">
                                <p id="correctionInstructions">Click on the signal to add/remove/move R-peaks</p>
                                <div class="correction-stats">
                                    <span>Added: <span id="addedCount">0</span></span>
                                    <span>Removed: <span id="removedCount">0</span></span>
                                    <span>Moved: <span id="movedCount">0</span></span>
                                </div>
                            </div>
                            
                            <button type="button" class="btn btn-secondary" onclick="undoCorrection()">
                                <i class="fas fa-undo"></i> Undo Last
                            </button>
                        </div>
                    </div>

                    <!-- Export Options -->
                    <div class="control-section">
                        <h3><i class="fas fa-download"></i> Export R-Peaks</h3>
                        <div class="export-options">
                            <div class="form-group">
                                <label class="form-label">Export Format</label>
                                <select class="form-select" id="peakExportFormat">
                                    <option value="csv">CSV (indices)</option>
                                    <option value="txt">TXT (time stamps)</option>
                                    <option value="json">JSON (detailed)</option>
                                    <option value="annotation">Annotation File</option>
                                </select>
                            </div>
                            
                            <div class="export-checkboxes">
                                <label class="form-label">
                                    <input type="checkbox" id="exportIndices" checked> Peak Indices
                                </label>
                                <label class="form-label">
                                    <input type="checkbox" id="exportTimestamps" checked> Time Stamps
                                </label>
                                <label class="form-label">
                                    <input type="checkbox" id="exportRRIntervals" checked> RR Intervals
                                </label>
                                <label class="form-label">
                                    <input type="checkbox" id="exportStatistics"> Detection Statistics
                                </label>
                            </div>
                        </div>
                        
                        <div class="export-actions">
                            <button type="button" class="btn btn-primary" onclick="exportRPeaks()">
                                <i class="fas fa-download"></i> Export R-Peaks
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Visualization Panel -->
                <div class="visualization-panel">
                    <!-- Processing Steps Visualization -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Algorithm Processing Steps</h3>
                            <div class="chart-controls">
                                <button type="button" class="btn btn-small btn-primary" onclick="showProcessingStep('original')">Original</button>
                                <button type="button" class="btn btn-small" onclick="showProcessingStep('filtered')">Filtered</button>
                                <button type="button" class="btn btn-small" onclick="showProcessingStep('differentiated')">Differentiated</button>
                                <button type="button" class="btn btn-small" onclick="showProcessingStep('squared')">Squared</button>
                                <button type="button" class="btn btn-small" onclick="showProcessingStep('integrated')">Integrated</button>
                            </div>
                        </div>
                        <div class="chart-area">
                            <canvas id="processingChart" width="800" height="300"></canvas>
                        </div>
                    </div>

                    <!-- R-Peak Detection Results -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>R-Peak Detection Results</h3>
                            <div class="chart-controls">
                                <button type="button" class="btn btn-small" onclick="zoomToRegion()">
                                    <i class="fas fa-search-plus"></i> Zoom
                                </button>
                                <button type="button" class="btn btn-small" onclick="resetZoom()">
                                    <i class="fas fa-search-minus"></i> Reset
                                </button>
                                <button type="button" class="btn btn-small" onclick="togglePeakLabels()">
                                    <i class="fas fa-tags"></i> Labels
                                </button>
                            </div>
                        </div>
                        <div class="chart-area">
                            <canvas id="detectionChart" width="800" height="400"></canvas>
                        </div>
                        <div class="chart-info">
                            <div class="info-item">
                                <span class="info-label">Time Range:</span>
                                <span class="info-value" id="timeRange">0 - 60 s</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Zoom Level:</span>
                                <span class="info-value" id="zoomLevel">1x</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Selected Peak:</span>
                                <span class="info-value" id="selectedPeak">None</span>
                            </div>
                        </div>
                    </div>

                    <!-- RR Interval Analysis -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>RR Interval Analysis</h3>
                        </div>
                        <div class="chart-area">
                            <canvas id="rrIntervalChart" width="800" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button type="button" class="btn btn-secondary" onclick="window.location.href='signal-processing.html'">
                    <i class="fas fa-arrow-left"></i> Back to Signal Processing
                </button>
                <button type="button" class="btn btn-primary" onclick="proceedToHRVAnalysis()" id="proceedHRVBtn" disabled>
                    <i class="fas fa-arrow-right"></i> Proceed to HRV Analysis
                </button>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Detecting R-peaks...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/core/utils.js"></script>
    <script src="js/core/navigation.js"></script>
    <script src="js/modules/r-peak-detection.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
