<!DOCTYPE html>
<!--
    Biosignal Virtual Lab - HRV Analysis Module
    
    Author: Dr. <PERSON> Esmail
    Institution: SUST - BME (Sudan University of Science and Technology - Biomedical Engineering)
    Year: 2025
    
    Contact Information:
    Email: <EMAIL>
    Phone: +249912867327, +966538076790
    
    Copyright © 2025 Dr. <PERSON>smail. All rights reserved.
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HRV Analysis - Biosignal Virtual Lab</title>
    <meta name="author" content="Dr. <PERSON>smail">
    <meta name="copyright" content="© 2025 Dr. <PERSON>">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/modules.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-heartbeat"></i>
                <span>Biosignal Virtual Lab</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="signal-processing.html" class="nav-link">Signal Processing</a>
                <a href="r-peak-detection.html" class="nav-link">R-Peak Detection</a>
                <a href="hrv-analysis.html" class="nav-link active">HRV Analysis</a>
                <a href="results.html" class="nav-link">Results</a>
                <div class="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumbs -->
    <div class="breadcrumbs-container">
        <div class="container">
            <nav class="breadcrumbs">
                <a href="index.html">Home</a>
                <span class="breadcrumb-separator">›</span>
                <a href="signal-processing.html">Signal Processing</a>
                <span class="breadcrumb-separator">›</span>
                <a href="r-peak-detection.html">R-Peak Detection</a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current">HRV Analysis</span>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <i class="fas fa-chart-line"></i>
                    <h1>Heart Rate Variability Analysis</h1>
                </div>
                <p class="page-description">
                    Comprehensive HRV analysis with time-domain and frequency-domain features for autonomic nervous system assessment.
                </p>
            </div>

            <!-- HRV Overview -->
            <div class="hrv-overview">
                <h2>HRV Analysis Domains</h2>
                <div class="domain-cards">
                    <div class="domain-card">
                        <div class="domain-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <h3>Time Domain</h3>
                        <p>Statistical measures of RR intervals including SDNN, RMSSD, pNN50, and triangular index</p>
                        <ul class="domain-features">
                            <li>SDNN - Overall HRV</li>
                            <li>RMSSD - Short-term variability</li>
                            <li>pNN50 - Parasympathetic activity</li>
                            <li>Triangular Index - Geometric measure</li>
                        </ul>
                    </div>
                    
                    <div class="domain-card">
                        <div class="domain-icon">
                            <i class="fas fa-wave-square"></i>
                        </div>
                        <h3>Frequency Domain</h3>
                        <p>Power spectral analysis of RR intervals in different frequency bands</p>
                        <ul class="domain-features">
                            <li>VLF (0.003-0.04 Hz) - Thermoregulation</li>
                            <li>LF (0.04-0.15 Hz) - Sympathetic activity</li>
                            <li>HF (0.15-0.4 Hz) - Parasympathetic activity</li>
                            <li>LF/HF Ratio - Sympathovagal balance</li>
                        </ul>
                    </div>
                    
                    <div class="domain-card">
                        <div class="domain-icon">
                            <i class="fas fa-project-diagram"></i>
                        </div>
                        <h3>Nonlinear Analysis</h3>
                        <p>Geometric and complexity measures of heart rate dynamics</p>
                        <ul class="domain-features">
                            <li>Poincaré Plot - SD1, SD2</li>
                            <li>Sample Entropy - Complexity</li>
                            <li>Detrended Fluctuation Analysis</li>
                            <li>Recurrence Quantification</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Analysis Interface -->
            <div class="analysis-interface">
                <!-- Control Panel -->
                <div class="control-panel">
                    <!-- Analysis Settings -->
                    <div class="control-section active">
                        <h3><i class="fas fa-cogs"></i> Analysis Settings</h3>
                        <div class="analysis-settings">
                            <div class="form-group">
                                <label class="form-label">Analysis Window</label>
                                <select class="form-select" id="analysisWindow">
                                    <option value="full">Full Recording</option>
                                    <option value="5min" selected>5 Minutes</option>
                                    <option value="10min">10 Minutes</option>
                                    <option value="custom">Custom Range</option>
                                </select>
                            </div>
                            
                            <div class="form-group" id="customRangeGroup" style="display: none;">
                                <label class="form-label">Custom Range (seconds)</label>
                                <div class="range-inputs">
                                    <input type="number" class="form-input" id="startTime" placeholder="Start" min="0">
                                    <span>to</span>
                                    <input type="number" class="form-input" id="endTime" placeholder="End">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">RR Interval Filtering</label>
                                <div class="filter-options">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableArtifactFilter" checked>
                                        Remove Artifacts
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableEctopicFilter" checked>
                                        Remove Ectopic Beats
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="enableOutlierFilter" checked>
                                        Remove Outliers
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Interpolation Method</label>
                                <select class="form-select" id="interpolationMethod">
                                    <option value="cubic" selected>Cubic Spline</option>
                                    <option value="linear">Linear</option>
                                    <option value="akima">Akima</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Interpolation Rate (Hz)</label>
                                <input type="number" class="form-input" id="interpolationRate" value="4" min="1" max="10" step="0.5">
                                <small class="form-help">Recommended: 4 Hz for HRV analysis</small>
                            </div>
                        </div>
                        
                        <div class="analysis-actions">
                            <button type="button" class="btn btn-primary" onclick="performHRVAnalysis()">
                                <i class="fas fa-play"></i> Analyze HRV
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetAnalysis()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>

                    <!-- Time Domain Results -->
                    <div class="control-section">
                        <h3><i class="fas fa-clock"></i> Time Domain Results</h3>
                        <div class="results-grid">
                            <div class="result-item">
                                <div class="result-label">SDNN (ms)</div>
                                <div class="result-value" id="sdnnValue">--</div>
                                <div class="result-description">Standard deviation of NN intervals</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">RMSSD (ms)</div>
                                <div class="result-value" id="rmssdValue">--</div>
                                <div class="result-description">Root mean square of successive differences</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">pNN50 (%)</div>
                                <div class="result-value" id="pnn50Value">--</div>
                                <div class="result-description">Percentage of successive RR intervals > 50ms</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">pNN20 (%)</div>
                                <div class="result-value" id="pnn20Value">--</div>
                                <div class="result-description">Percentage of successive RR intervals > 20ms</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">Triangular Index</div>
                                <div class="result-value" id="triangularIndexValue">--</div>
                                <div class="result-description">Total NN intervals / histogram height</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">CV (%)</div>
                                <div class="result-value" id="cvValue">--</div>
                                <div class="result-description">Coefficient of variation</div>
                            </div>
                        </div>
                        
                        <div class="interpretation-panel">
                            <h4>Clinical Interpretation</h4>
                            <div class="interpretation-content" id="timeDomainInterpretation">
                                <p>Perform analysis to see clinical interpretation of time domain results.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Frequency Domain Results -->
                    <div class="control-section">
                        <h3><i class="fas fa-wave-square"></i> Frequency Domain Results</h3>
                        <div class="results-grid">
                            <div class="result-item">
                                <div class="result-label">VLF Power (ms²)</div>
                                <div class="result-value" id="vlfPowerValue">--</div>
                                <div class="result-description">Very low frequency (0.003-0.04 Hz)</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">LF Power (ms²)</div>
                                <div class="result-value" id="lfPowerValue">--</div>
                                <div class="result-description">Low frequency (0.04-0.15 Hz)</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">HF Power (ms²)</div>
                                <div class="result-value" id="hfPowerValue">--</div>
                                <div class="result-description">High frequency (0.15-0.4 Hz)</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">LF/HF Ratio</div>
                                <div class="result-value" id="lfhfRatioValue">--</div>
                                <div class="result-description">Sympathovagal balance</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">Total Power (ms²)</div>
                                <div class="result-value" id="totalPowerValue">--</div>
                                <div class="result-description">Sum of all frequency components</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">Peak Frequency (Hz)</div>
                                <div class="result-value" id="peakFreqValue">--</div>
                                <div class="result-description">Dominant frequency in LF band</div>
                            </div>
                        </div>
                        
                        <div class="frequency-bands">
                            <h4>Frequency Band Distribution</h4>
                            <div class="band-chart">
                                <canvas id="frequencyBandsChart" width="300" height="200"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Nonlinear Analysis Results -->
                    <div class="control-section">
                        <h3><i class="fas fa-project-diagram"></i> Nonlinear Analysis</h3>
                        <div class="results-grid">
                            <div class="result-item">
                                <div class="result-label">SD1 (ms)</div>
                                <div class="result-value" id="sd1Value">--</div>
                                <div class="result-description">Short-term variability (Poincaré)</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">SD2 (ms)</div>
                                <div class="result-value" id="sd2Value">--</div>
                                <div class="result-description">Long-term variability (Poincaré)</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">SD1/SD2 Ratio</div>
                                <div class="result-value" id="sd1sd2RatioValue">--</div>
                                <div class="result-description">Ratio of short to long-term variability</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">Sample Entropy</div>
                                <div class="result-value" id="sampleEntropyValue">--</div>
                                <div class="result-description">Complexity measure</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">DFA α1</div>
                                <div class="result-value" id="dfa1Value">--</div>
                                <div class="result-description">Short-term fractal scaling</div>
                            </div>
                            
                            <div class="result-item">
                                <div class="result-label">DFA α2</div>
                                <div class="result-value" id="dfa2Value">--</div>
                                <div class="result-description">Long-term fractal scaling</div>
                            </div>
                        </div>
                    </div>

                    <!-- Export Options -->
                    <div class="control-section">
                        <h3><i class="fas fa-download"></i> Export Results</h3>
                        <div class="export-options">
                            <div class="form-group">
                                <label class="form-label">Export Format</label>
                                <select class="form-select" id="exportFormat">
                                    <option value="csv">CSV (Spreadsheet)</option>
                                    <option value="json">JSON (Data)</option>
                                    <option value="pdf">PDF (Report)</option>
                                    <option value="txt">TXT (Summary)</option>
                                </select>
                            </div>
                            
                            <div class="export-checkboxes">
                                <label class="checkbox-label">
                                    <input type="checkbox" id="exportTimeDomain" checked>
                                    Time Domain Results
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="exportFreqDomain" checked>
                                    Frequency Domain Results
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="exportNonlinear" checked>
                                    Nonlinear Analysis
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="exportCharts">
                                    Charts and Plots
                                </label>
                                <label class="checkbox-label">
                                    <input type="checkbox" id="exportInterpretation">
                                    Clinical Interpretation
                                </label>
                            </div>
                        </div>
                        
                        <div class="export-actions">
                            <button type="button" class="btn btn-primary" onclick="exportHRVResults()">
                                <i class="fas fa-download"></i> Export Results
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="generateReport()">
                                <i class="fas fa-file-pdf"></i> Generate Report
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Visualization Panel -->
                <div class="visualization-panel">
                    <!-- RR Interval Tachogram -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>RR Interval Tachogram</h3>
                            <div class="chart-controls">
                                <button type="button" class="btn btn-small" onclick="toggleTachogramView('raw')">Raw</button>
                                <button type="button" class="btn btn-small btn-primary" onclick="toggleTachogramView('filtered')">Filtered</button>
                                <button type="button" class="btn btn-small" onclick="toggleTachogramView('interpolated')">Interpolated</button>
                            </div>
                        </div>
                        <div class="chart-area">
                            <canvas id="tachogramChart" width="800" height="300"></canvas>
                        </div>
                        <div class="chart-info">
                            <div class="info-item">
                                <span class="info-label">RR Intervals:</span>
                                <span class="info-value" id="rrCount">--</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Mean RR:</span>
                                <span class="info-value" id="meanRR">-- ms</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Heart Rate:</span>
                                <span class="info-value" id="meanHR">-- BPM</span>
                            </div>
                        </div>
                    </div>

                    <!-- Power Spectral Density -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Power Spectral Density</h3>
                            <div class="chart-controls">
                                <button type="button" class="btn btn-small" onclick="togglePSDScale('linear')">Linear</button>
                                <button type="button" class="btn btn-small btn-primary" onclick="togglePSDScale('log')">Log</button>
                                <button type="button" class="btn btn-small" onclick="showFrequencyBands()">Show Bands</button>
                            </div>
                        </div>
                        <div class="chart-area">
                            <canvas id="psdChart" width="800" height="300"></canvas>
                        </div>
                    </div>

                    <!-- Poincaré Plot -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Poincaré Plot</h3>
                            <div class="chart-controls">
                                <button type="button" class="btn btn-small" onclick="togglePoincareEllipse()">Toggle Ellipse</button>
                                <button type="button" class="btn btn-small" onclick="showPoincareStats()">Show Stats</button>
                            </div>
                        </div>
                        <div class="chart-area">
                            <canvas id="poincarePlot" width="800" height="400"></canvas>
                        </div>
                    </div>

                    <!-- RR Interval Histogram -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>RR Interval Histogram</h3>
                            <div class="chart-controls">
                                <button type="button" class="btn btn-small" onclick="changeBinSize(20)">20 bins</button>
                                <button type="button" class="btn btn-small btn-primary" onclick="changeBinSize(50)">50 bins</button>
                                <button type="button" class="btn btn-small" onclick="changeBinSize(100)">100 bins</button>
                            </div>
                        </div>
                        <div class="chart-area">
                            <canvas id="histogramChart" width="800" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button type="button" class="btn btn-secondary" onclick="window.location.href='r-peak-detection.html'">
                    <i class="fas fa-arrow-left"></i> Back to R-Peak Detection
                </button>
                <button type="button" class="btn btn-primary" onclick="proceedToResults()" id="proceedResultsBtn" disabled>
                    <i class="fas fa-arrow-right"></i> Proceed to Results
                </button>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Analyzing HRV...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/plotly.js-dist@2.26.0/plotly.min.js"></script>
    <script src="js/core/utils.js"></script>
    <script src="js/core/navigation.js"></script>
    <script src="js/modules/hrv-analysis.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
