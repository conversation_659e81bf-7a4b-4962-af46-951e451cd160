<!DOCTYPE html>
<!--
    Biosignal Virtual Lab - ECG HRV Analysis Platform

    Author: Dr. <PERSON> Esmail
    Institution: SUST - BME (Sudan University of Science and Technology - Biomedical Engineering)
    Year: 2025

    Contact Information:
    Email: <EMAIL>
    Phone: +249912867327, +966538076790

    Copyright © 2025 Dr. <PERSON> Esmail. All rights reserved.

    This platform provides comprehensive tools for ECG signal processing,
    R-peak detection, and Heart Rate Variability (HRV) analysis for
    research and educational purposes.
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biosignal Virtual Lab - ECG HRV Analysis Platform</title>
    <meta name="author" content="Dr. <PERSON>go<PERSON> Esmail">
    <meta name="description" content="Advanced ECG signal processing and HRV analysis platform for research and education">
    <meta name="keywords" content="ECG, HRV, biomedical engineering, signal processing, R-peak detection">
    <meta name="copyright" content="© 2025 Dr. <PERSON>")
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-heartbeat"></i>
                <span>Biosignal Virtual Lab</span>
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link active">Home</a>
                <a href="learning-module.html" class="nav-link">Learning Module</a>
                <a href="#features" class="nav-link">Features</a>
                <a href="#platform" class="nav-link">Platform</a>
                <a href="#about" class="nav-link">About</a>
                <button type="button" class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
                <div class="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="ecg-animation"></div>
            <div class="particles">
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
                <div class="particle"></div>
            </div>
        </div>
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">
                    Advanced ECG Signal Processing &
                    <span class="gradient-text">HRV Analysis Platform</span>
                </h1>
                <p class="hero-description">
                    A comprehensive virtual laboratory for biosignal processing featuring real-time R-peak detection,
                    heart rate variability analysis, and advanced signal processing algorithms.
                </p>
                <div class="hero-buttons">
                    <button type="button" class="btn btn-primary" onclick="navigateToModule('signal-processing')">
                        <i class="fas fa-play"></i>
                        Start Analysis
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="navigateToModule('documentation')">
                        <i class="fas fa-book"></i>
                        Documentation
                    </button>
                </div>
            </div>
            <div class="hero-visual">
                <div class="platform-preview">
                    <div class="preview-screen">
                        <canvas id="heroECGCanvas" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header">
                <h2>Platform Features</h2>
                <p>Comprehensive tools for ECG signal analysis and heart rate variability assessment</p>
            </div>
            <div class="features-grid">
                <div class="feature-card" data-module="signal-processing">
                    <div class="feature-icon">
                        <i class="fas fa-wave-square"></i>
                    </div>
                    <h3>Signal Processing</h3>
                    <p>Advanced ECG signal preprocessing with filtering, noise reduction, and baseline correction</p>
                    <ul class="feature-list">
                        <li>Bandpass filtering (5-15 Hz)</li>
                        <li>Baseline drift removal</li>
                        <li>Noise artifact detection</li>
                        <li>Signal normalization</li>
                    </ul>
                    <button type="button" class="btn btn-outline" onclick="navigateToModule('signal-processing')">
                        Explore <i class="fas fa-arrow-right"></i>
                    </button>
                </div>

                <div class="feature-card" data-module="r-peak-detection">
                    <div class="feature-icon">
                        <i class="fas fa-search-plus"></i>
                    </div>
                    <h3>R-Peak Detection</h3>
                    <p>Pan-Tompkins algorithm implementation with adaptive thresholding and validation</p>
                    <ul class="feature-list">
                        <li>Real-time peak detection</li>
                        <li>Adaptive thresholding</li>
                        <li>Artifact removal</li>
                        <li>Quality assessment</li>
                    </ul>
                    <button class="btn btn-outline" onclick="navigateToModule('r-peak-detection')">
                        Explore <i class="fas fa-arrow-right"></i>
                    </button>
                </div>

                <div class="feature-card" data-module="hrv-analysis">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>HRV Analysis</h3>
                    <p>Comprehensive heart rate variability analysis with time and frequency domain features</p>
                    <ul class="feature-list">
                        <li>Time domain metrics (SDNN, RMSSD)</li>
                        <li>Frequency domain analysis</li>
                        <li>Poincaré plot analysis</li>
                        <li>Statistical summaries</li>
                    </ul>
                    <button class="btn btn-outline" onclick="navigateToModule('hrv-analysis')">
                        Explore <i class="fas fa-arrow-right"></i>
                    </button>
                </div>

                <div class="feature-card" data-module="data-management">
                    <div class="feature-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h3>Data Management</h3>
                    <p>Multi-format data loading with support for standard ECG databases and custom files</p>
                    <ul class="feature-list">
                        <li>MIT-BIH database support</li>
                        <li>CSV/TXT file import</li>
                        <li>WFDB format support</li>
                        <li>Sample datasets included</li>
                    </ul>
                    <button class="btn btn-outline" onclick="navigateToModule('data-management')">
                        Explore <i class="fas fa-arrow-right"></i>
                    </button>
                </div>

                <div class="feature-card" data-module="visualization">
                    <div class="feature-icon">
                        <i class="fas fa-chart-area"></i>
                    </div>
                    <h3>Advanced Visualization</h3>
                    <p>Interactive charts and plots for comprehensive signal analysis and results presentation</p>
                    <ul class="feature-list">
                        <li>Real-time signal plotting</li>
                        <li>Interactive spectrograms</li>
                        <li>3D visualization</li>
                        <li>Export capabilities</li>
                    </ul>
                    <button class="btn btn-outline" onclick="navigateToModule('visualization')">
                        Explore <i class="fas fa-arrow-right"></i>
                    </button>
                </div>

                <div class="feature-card" data-module="results">
                    <div class="feature-icon">
                        <i class="fas fa-file-medical"></i>
                    </div>
                    <h3>Results & Reports</h3>
                    <p>Comprehensive analysis reports with clinical insights and exportable summaries</p>
                    <ul class="feature-list">
                        <li>Automated report generation</li>
                        <li>Clinical interpretations</li>
                        <li>PDF/CSV export</li>
                        <li>Comparison tools</li>
                    </ul>
                    <button class="btn btn-outline" onclick="navigateToModule('results')">
                        Explore <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Platform Access Section -->
    <section id="platform" class="platform-access">
        <div class="container">
            <div class="section-header">
                <h2>Access Platform Modules</h2>
                <p>Choose your analysis workflow or start with a guided tutorial</p>
            </div>
            <div class="platform-modules">
                <div class="module-grid">
                    <div class="module-card primary" onclick="navigateToModule('signal-processing')">
                        <div class="module-header">
                            <i class="fas fa-wave-square"></i>
                            <h3>Signal Processing</h3>
                        </div>
                        <p>Upload and preprocess ECG signals</p>
                        <div class="module-status">
                            <span class="status-badge ready">Ready</span>
                        </div>
                    </div>

                    <div class="module-card" onclick="navigateToModule('r-peak-detection')">
                        <div class="module-header">
                            <i class="fas fa-search-plus"></i>
                            <h3>R-Peak Detection</h3>
                        </div>
                        <p>Detect and validate R-peaks</p>
                        <div class="module-status">
                            <span class="status-badge ready">Ready</span>
                        </div>
                    </div>

                    <div class="module-card" onclick="navigateToModule('hrv-analysis')">
                        <div class="module-header">
                            <i class="fas fa-chart-line"></i>
                            <h3>HRV Analysis</h3>
                        </div>
                        <p>Comprehensive HRV metrics</p>
                        <div class="module-status">
                            <span class="status-badge ready">Ready</span>
                        </div>
                    </div>

                    <div class="module-card" onclick="navigateToModule('results')">
                        <div class="module-header">
                            <i class="fas fa-chart-area"></i>
                            <h3>Results & Visualization</h3>
                        </div>
                        <p>View and export analysis results</p>
                        <div class="module-status">
                            <span class="status-badge ready">Ready</span>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="btn btn-primary btn-large" onclick="startGuidedTour()">
                        <i class="fas fa-play-circle"></i>
                        Start Guided Tutorial
                    </button>
                    <button class="btn btn-secondary btn-large" onclick="loadSampleData()">
                        <i class="fas fa-download"></i>
                        Load Sample Data
                    </button>
                </div>
            </div>
        </div>
    </section>
    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2>About the Platform</h2>
                    <p>
                        The Biosignal Virtual Lab is an advanced web-based platform for ECG signal processing
                        and heart rate variability analysis. Built with modern web technologies, it provides
                        researchers, clinicians, and students with powerful tools for cardiac signal analysis.
                    </p>
                    <div class="about-features">
                        <div class="about-feature">
                            <i class="fas fa-code"></i>
                            <div>
                                <h4>Open Source</h4>
                                <p>Built with modern web technologies and open-source libraries</p>
                            </div>
                        </div>
                        <div class="about-feature">
                            <i class="fas fa-graduation-cap"></i>
                            <div>
                                <h4>Educational</h4>
                                <p>Perfect for teaching signal processing and biomedical engineering</p>
                            </div>
                        </div>
                        <div class="about-feature">
                            <i class="fas fa-flask"></i>
                            <div>
                                <h4>Research Ready</h4>
                                <p>Suitable for clinical research and algorithm development</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="about-stats">
                    <div class="stat-card">
                        <div class="stat-number">6</div>
                        <div class="stat-label">Analysis Modules</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">15+</div>
                        <div class="stat-label">HRV Features</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">5</div>
                        <div class="stat-label">File Formats</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Web-Based</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-brand">
                        <i class="fas fa-heartbeat"></i>
                        <span>Biosignal Virtual Lab</span>
                    </div>
                    <p>Advanced ECG signal processing and HRV analysis platform for research and education.</p>
                    <div class="author-info">
                        <p><strong>Author:</strong> Dr. Mohammed Yagoub Esmail</p>
                        <p><strong>Institution:</strong> SUST - BME</p>
                        <p><strong>Contact:</strong> <EMAIL></p>
                        <p><strong>Phone:</strong> +249912867327, +966538076790</p>
                    </div>
                    <div class="footer-social">
                        <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                        <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                        <a href="#" class="social-link"><i class="fas fa-envelope"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Platform</h4>
                    <ul class="footer-links">
                        <li><a href="signal-processing.html">Signal Processing</a></li>
                        <li><a href="r-peak-detection.html">R-Peak Detection</a></li>
                        <li><a href="hrv-analysis.html">HRV Analysis</a></li>
                        <li><a href="data-management.html">Data Management</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Resources</h4>
                    <ul class="footer-links">
                        <li><a href="documentation.html">Documentation</a></li>
                        <li><a href="tutorials.html">Tutorials</a></li>
                        <li><a href="examples.html">Examples</a></li>
                        <li><a href="api-reference.html">API Reference</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <ul class="footer-links">
                        <li><a href="help.html">Help Center</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="feedback.html">Feedback</a></li>
                        <li><a href="bug-report.html">Report Bug</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Dr. Mohammed Yagoub Esmail, SUST-BME. All rights reserved.</p>
                <div class="footer-bottom-links">
                    <a href="privacy.html">Privacy Policy</a>
                    <a href="terms.html">Terms of Service</a>
                    <a href="license.html">License</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Loading Platform...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/plotly.js-dist@2.26.0/plotly.min.js"></script>
    <script src="js/core/utils.js"></script>
    <script src="js/core/navigation.js"></script>
    <script src="js/core/animations.js"></script>
    <script src="js/components/ecg-canvas.js"></script>
    <script src="js/main.js"></script>
</body>
</html>