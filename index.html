<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECG Signal Processing Explained</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <h1>Understanding ECG Signal Processing</h1>
        <p>A step-by-step guide to analyzing electrocardiogram (ECG) signals.</p>
    </header>

    <main>
        <section id="introduction">
            <h2>Introduction to ECG Analysis</h2>
            <p>Electrocardiography (ECG) is a fundamental tool for assessing cardiac health. Raw ECG signals, however, require sophisticated processing to extract clinically meaningful information. This guide walks through the essential steps of this process, from detecting the key features in the signal to performing advanced analysis.</p>
        </section>

        <section id="steps">
            <h2>The ECG Processing Pipeline</h2>
            <div class="step">
                <h3>Step 1: R-Peak Detection (The Heartbeat's Signature)</h3>
                <p>The most prominent feature of the ECG waveform is the QRS complex, with the R-peak being its highest point. Accurate R-peak detection is the foundation of most automated ECG analysis. We use an advanced algorithm inspired by Pan-<PERSON><PERSON>kins, which involves several stages:</p>
                <ul>
                    <li><strong>Bandpass Filtering:</strong> Isolates the QRS complex frequency band (5-15 Hz).</li>
                    <li><strong>Differentiation:</strong> Highlights the steep slopes of the R-peak.</li>
                    <li><strong>Squaring:</strong> Enhances the peaks and makes them all positive.</li>
                    <li><strong>Moving Window Integration:</strong> Smooths the signal to create a clear waveform for detection.</li>
                    <li><strong>Adaptive Thresholding:</strong> A dynamic threshold identifies peaks while ignoring noise.</li>
                </ul>
                <div class="visualization">
                    <img src="https://i.imgur.com/5JCR6M7.png" alt="R-Peak Detection Steps">
                    <p class="caption">Visualization of the R-peak detection process.</p>
                </div>
            </div>

            <div class="step">
                <h3>Step 2: Heart Rate Variability (HRV) Analysis</h3>
                <p>HRV measures the variation in time between consecutive heartbeats (R-R intervals). It is a powerful indicator of the autonomic nervous system's health. HRV analysis is divided into two main domains:</p>
                <h4>Time-Domain Features</h4>
                <ul>
                    <li><strong>SDNN:</strong> Standard deviation of NN (normal-to-normal) intervals. Reflects overall HRV.</li>
                    <li><strong>RMSSD:</strong> Root mean square of successive differences. Reflects short-term, high-frequency variations.</li>
                    <li><strong>pNN50:</strong> Percentage of successive differences greater than 50ms.</li>
                </ul>
                <h4>Frequency-Domain Features</h4>
                <ul>
                    <li><strong>VLF, LF, HF Bands:</strong> Power in different frequency bands (Very Low, Low, and High Frequency).</li>
                    <li><strong>LF/HF Ratio:</strong> An indicator of the balance between the sympathetic and parasympathetic nervous systems.</li>
                </ul>
                <div class="visualization">
                    <img src="https://i.imgur.com/O3tACwg.png" alt="HRV Analysis Visualization">
                    <p class="caption">HRV analysis visualizations, including a tachogram and a Poincaré plot.</p>
                </div>
            </div>

            <div class="step">
                <h3>Step 3: Frequency Domain (Spectral) Analysis</h3>
                <p>This step involves transforming the RR interval time series into the frequency domain to analyze its spectral components. This is typically done using Welch's method to calculate the Power Spectral Density (PSD).</p>
                <ul>
                    <li><strong>Interpolation:</strong> The unevenly spaced RR intervals are interpolated to create an evenly sampled signal.</li>
                    <li><strong>Welch's Method:</strong> The signal is divided into overlapping segments, windowed, and then a Fourier Transform is performed on each segment. The results are averaged to produce the PSD.</li>
                    <li><strong>Power Calculation:</strong> The power in the VLF, LF, and HF bands is calculated from the PSD.</li>
                </ul>
                <div class="visualization">
                    <img src="https://i.imgur.com/sZ3bJ2g.png" alt="Power Spectral Density Plot">
                    <p class="caption">Power Spectral Density (PSD) plot showing the different frequency bands.</p>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <p>Developed by the Biosignal Processing Group</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>