/**
 * R-Peak Detection Module - Biosignal Virtual Lab
 * Implements Pan-<PERSON><PERSON> algorithm for R-peak detection
 *
 * Author: Dr. <PERSON>smail
 * Institution: SUST - BME
 * Year: 2025
 * Contact: <EMAIL>
 *
 * Copyright © 2025 Dr. <PERSON> Esmail. All rights reserved.
 */

class RPeakDetector {
    constructor() {
        this.ecgSignal = null;
        this.samplingRate = 360;
        this.rPeaks = [];
        this.processingSteps = {};
        this.charts = {};
        this.currentStep = 'original';
        this.correctionMode = null;
        this.correctionHistory = [];
        
        this.initializeModule();
    }
    
    /**
     * Initialize the R-peak detection module
     */
    initializeModule() {
        console.log('Initializing R-Peak Detection Module...');
        
        this.loadECGData();
        this.bindEvents();
        this.initializeCharts();
        this.setupControlSections();
        
        if (!this.ecgSignal) {
            this.showDataRequiredMessage();
        }
    }
    
    /**
     * Load ECG data from global state
     */
    loadECGData() {
        const ecgData = window.BiosignalLab.ecgData;
        if (ecgData && ecgData.signal) {
            this.ecgSignal = [...ecgData.signal];
            this.samplingRate = ecgData.samplingRate || 360;
            console.log(`Loaded ECG signal: ${this.ecgSignal.length} samples at ${this.samplingRate} Hz`);
        }
    }
    
    /**
     * Show message when ECG data is required
     */
    showDataRequiredMessage() {
        this.showNotification('Please load ECG data in Signal Processing module first', 'warning');
        
        // Disable detection button
        const detectBtn = document.querySelector('[onclick="detectRPeaks()"]');
        if (detectBtn) {
            detectBtn.disabled = true;
            detectBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Load ECG Data First';
        }
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // Threshold factor slider
        const thresholdSlider = document.getElementById('thresholdFactor');
        if (thresholdSlider) {
            thresholdSlider.addEventListener('input', (e) => {
                document.getElementById('thresholdValue').textContent = e.target.value;
            });
        }
        
        // Parameter changes
        ['bpLowFreq', 'bpHighFreq', 'integrationWindow', 'refractoryPeriod'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => this.updateParameters());
            }
        });
    }
    
    /**
     * Setup collapsible control sections
     */
    setupControlSections() {
        document.querySelectorAll('.control-section h3').forEach(header => {
            header.addEventListener('click', (e) => {
                const section = e.target.closest('.control-section');
                this.toggleControlSection(section);
            });
        });
        
        // Activate first section by default
        const firstSection = document.querySelector('.control-section');
        if (firstSection) {
            firstSection.classList.add('active');
        }
    }
    
    /**
     * Toggle control section
     */
    toggleControlSection(section) {
        const isActive = section.classList.contains('active');
        
        // Close all sections
        document.querySelectorAll('.control-section').forEach(s => {
            s.classList.remove('active');
        });
        
        // Open clicked section if it wasn't active
        if (!isActive) {
            section.classList.add('active');
        }
    }
    
    /**
     * Update algorithm parameters
     */
    updateParameters() {
        // Re-run detection if peaks already exist
        if (this.rPeaks.length > 0) {
            this.detectRPeaks();
        }
    }
    
    /**
     * Detect R-peaks using Pan-Tompkins algorithm
     */
    detectRPeaks() {
        if (!this.ecgSignal) {
            this.showNotification('No ECG signal loaded', 'error');
            return;
        }
        
        try {
            this.showLoading('Detecting R-peaks...');
            
            // Get algorithm parameters
            const params = this.getAlgorithmParameters();
            
            // Apply Pan-Tompkins algorithm
            this.applyPanTompkinsAlgorithm(params);
            
            // Update results
            this.updateDetectionResults();
            this.updateCharts();
            this.enableProceedButton();
            
            this.showNotification(`Detected ${this.rPeaks.length} R-peaks`, 'success');
            
        } catch (error) {
            console.error('Error detecting R-peaks:', error);
            this.showNotification(`Detection error: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * Get algorithm parameters from UI
     */
    getAlgorithmParameters() {
        return {
            lowFreq: parseFloat(document.getElementById('bpLowFreq').value),
            highFreq: parseFloat(document.getElementById('bpHighFreq').value),
            integrationWindow: parseInt(document.getElementById('integrationWindow').value),
            refractoryPeriod: parseInt(document.getElementById('refractoryPeriod').value),
            thresholdFactor: parseFloat(document.getElementById('thresholdFactor').value)
        };
    }
    
    /**
     * Apply Pan-Tompkins algorithm
     */
    applyPanTompkinsAlgorithm(params) {
        const signal = [...this.ecgSignal];
        
        // Step 1: Bandpass filter (5-15 Hz)
        const filtered = this.bandpassFilter(signal, params.lowFreq, params.highFreq);
        this.processingSteps.filtered = filtered;
        
        // Step 2: Differentiation
        const differentiated = this.differentiate(filtered);
        this.processingSteps.differentiated = differentiated;
        
        // Step 3: Squaring
        const squared = differentiated.map(x => x * x);
        this.processingSteps.squared = squared;
        
        // Step 4: Moving window integration
        const windowSamples = Math.round(params.integrationWindow * this.samplingRate / 1000);
        const integrated = this.movingWindowIntegration(squared, windowSamples);
        this.processingSteps.integrated = integrated;
        
        // Step 5: Peak detection with adaptive thresholding
        const peaks = this.adaptiveThresholding(integrated, params);
        
        // Step 6: Back-search for actual R-peaks
        this.rPeaks = this.backSearchRPeaks(signal, peaks);
        
        // Store original signal for visualization
        this.processingSteps.original = signal;
        
        // Update global state
        window.BiosignalLab.rPeaks = this.rPeaks;
    }
    
    /**
     * Bandpass filter implementation (simplified)
     */
    bandpassFilter(signal, lowFreq, highFreq) {
        // Simplified bandpass filter
        // In a real implementation, use proper filter design
        return SignalUtils.bandpassFilter(signal, lowFreq, highFreq, this.samplingRate);
    }
    
    /**
     * Five-point derivative
     */
    differentiate(signal) {
        const diff = new Array(signal.length);
        
        // Handle boundaries
        diff[0] = 0;
        diff[1] = 0;
        diff[signal.length - 2] = 0;
        diff[signal.length - 1] = 0;
        
        // Five-point derivative: y'[n] = (1/8T)(-x[n-2] - 2x[n-1] + 2x[n+1] + x[n+2])
        for (let i = 2; i < signal.length - 2; i++) {
            diff[i] = (1/8) * (-signal[i-2] - 2*signal[i-1] + 2*signal[i+1] + signal[i+2]);
        }
        
        return diff;
    }
    
    /**
     * Moving window integration
     */
    movingWindowIntegration(signal, windowSize) {
        const integrated = new Array(signal.length);
        
        for (let i = 0; i < signal.length; i++) {
            let sum = 0;
            let count = 0;
            
            const start = Math.max(0, i - Math.floor(windowSize/2));
            const end = Math.min(signal.length - 1, i + Math.floor(windowSize/2));
            
            for (let j = start; j <= end; j++) {
                sum += signal[j];
                count++;
            }
            
            integrated[i] = sum / count;
        }
        
        return integrated;
    }
    
    /**
     * Adaptive thresholding for peak detection
     */
    adaptiveThresholding(signal, params) {
        const peaks = [];
        const refractorySamples = Math.round(params.refractoryPeriod * this.samplingRate / 1000);
        
        let signalPeak = 0;
        let noisePeak = 0;
        let threshold = 0;
        
        // Initialize thresholds
        const initialWindow = Math.min(2000, signal.length);
        const sortedValues = signal.slice(0, initialWindow).sort((a, b) => b - a);
        signalPeak = sortedValues[Math.floor(sortedValues.length * 0.1)];
        noisePeak = sortedValues[Math.floor(sortedValues.length * 0.9)];
        threshold = noisePeak + params.thresholdFactor * (signalPeak - noisePeak);
        
        let lastPeak = -refractorySamples;
        
        for (let i = 1; i < signal.length - 1; i++) {
            // Check if current sample is a local maximum above threshold
            if (signal[i] > signal[i-1] && 
                signal[i] > signal[i+1] && 
                signal[i] > threshold &&
                i - lastPeak >= refractorySamples) {
                
                peaks.push(i);
                lastPeak = i;
                
                // Update signal peak
                signalPeak = 0.125 * signal[i] + 0.875 * signalPeak;
                
            } else if (signal[i] > threshold) {
                // Update noise peak
                noisePeak = 0.125 * signal[i] + 0.875 * noisePeak;
            }
            
            // Update threshold
            threshold = noisePeak + params.thresholdFactor * (signalPeak - noisePeak);
        }
        
        return peaks;
    }
    
    /**
     * Back-search for actual R-peaks in original signal
     */
    backSearchRPeaks(originalSignal, peakCandidates) {
        const rPeaks = [];
        const searchWindow = Math.round(0.05 * this.samplingRate); // 50ms window
        
        for (const candidate of peakCandidates) {
            const start = Math.max(0, candidate - searchWindow);
            const end = Math.min(originalSignal.length - 1, candidate + searchWindow);
            
            let maxValue = originalSignal[start];
            let maxIndex = start;
            
            for (let i = start; i <= end; i++) {
                if (originalSignal[i] > maxValue) {
                    maxValue = originalSignal[i];
                    maxIndex = i;
                }
            }
            
            rPeaks.push(maxIndex);
        }
        
        return rPeaks;
    }
    
    /**
     * Update detection results display
     */
    updateDetectionResults() {
        if (this.rPeaks.length === 0) return;
        
        // Calculate RR intervals
        const rrIntervals = [];
        for (let i = 1; i < this.rPeaks.length; i++) {
            const rrMs = (this.rPeaks[i] - this.rPeaks[i-1]) / this.samplingRate * 1000;
            rrIntervals.push(rrMs);
        }
        
        // Calculate statistics
        const meanRR = MathUtils.mean(rrIntervals);
        const meanHR = 60000 / meanRR; // Convert to BPM
        const minRR = Math.min(...rrIntervals);
        const maxRR = Math.max(...rrIntervals);
        
        // Update UI
        document.getElementById('peakCount').textContent = this.rPeaks.length;
        document.getElementById('meanHeartRate').textContent = `${meanHR.toFixed(1)} BPM`;
        document.getElementById('rrRange').textContent = `${minRR.toFixed(0)} - ${maxRR.toFixed(0)} ms`;
        
        // Calculate detection quality (simplified)
        const rrStd = MathUtils.std(rrIntervals);
        const cv = rrStd / meanRR * 100;
        let quality = 'Good';
        let qualityScore = 0.8;
        
        if (cv > 20) {
            quality = 'Poor';
            qualityScore = 0.3;
        } else if (cv > 10) {
            quality = 'Fair';
            qualityScore = 0.6;
        }
        
        document.getElementById('detectionQuality').textContent = quality;
        document.getElementById('qualityFill').style.width = `${qualityScore * 100}%`;
    }
    
    /**
     * Initialize charts
     */
    initializeCharts() {
        this.initializeProcessingChart();
        this.initializeDetectionChart();
        this.initializeRRIntervalChart();
    }
    
    /**
     * Initialize processing steps chart
     */
    initializeProcessingChart() {
        const canvas = document.getElementById('processingChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        this.charts.processing = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Signal',
                    data: [],
                    borderColor: '#2563eb',
                    backgroundColor: 'transparent',
                    borderWidth: 1,
                    pointRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'Sample' } },
                    y: { title: { display: true, text: 'Amplitude' } }
                },
                plugins: { legend: { display: false } }
            }
        });
    }
    
    /**
     * Initialize detection results chart
     */
    initializeDetectionChart() {
        const canvas = document.getElementById('detectionChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        this.charts.detection = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'ECG Signal',
                    data: [],
                    borderColor: '#2563eb',
                    backgroundColor: 'transparent',
                    borderWidth: 1,
                    pointRadius: 0
                }, {
                    label: 'R-Peaks',
                    data: [],
                    borderColor: '#ef4444',
                    backgroundColor: '#ef4444',
                    borderWidth: 0,
                    pointRadius: 4,
                    pointStyle: 'triangle',
                    showLine: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'Time (s)' } },
                    y: { title: { display: true, text: 'Amplitude (mV)' } }
                },
                plugins: { legend: { display: true } }
            }
        });
    }
    
    /**
     * Initialize RR interval chart
     */
    initializeRRIntervalChart() {
        const canvas = document.getElementById('rrIntervalChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        this.charts.rrInterval = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'RR Intervals',
                    data: [],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 2,
                    pointRadius: 3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: { title: { display: true, text: 'Beat Number' } },
                    y: { title: { display: true, text: 'RR Interval (ms)' } }
                },
                plugins: { legend: { display: false } }
            }
        });
    }
    
    /**
     * Update all charts
     */
    updateCharts() {
        this.updateProcessingChart();
        this.updateDetectionChart();
        this.updateRRIntervalChart();
    }
    
    /**
     * Update processing chart
     */
    updateProcessingChart() {
        const signal = this.processingSteps[this.currentStep];
        if (!signal) return;
        
        const maxPoints = 1000;
        const step = Math.max(1, Math.floor(signal.length / maxPoints));
        
        const labels = [];
        const data = [];
        
        for (let i = 0; i < signal.length; i += step) {
            labels.push(i);
            data.push(signal[i]);
        }
        
        this.charts.processing.data.labels = labels;
        this.charts.processing.data.datasets[0].data = data;
        this.charts.processing.data.datasets[0].label = this.currentStep.charAt(0).toUpperCase() + this.currentStep.slice(1);
        this.charts.processing.update('none');
    }
    
    /**
     * Update detection chart
     */
    updateDetectionChart() {
        if (!this.ecgSignal) return;
        
        const maxPoints = 2000;
        const step = Math.max(1, Math.floor(this.ecgSignal.length / maxPoints));
        
        const timeLabels = [];
        const signalData = [];
        
        for (let i = 0; i < this.ecgSignal.length; i += step) {
            timeLabels.push((i / this.samplingRate).toFixed(2));
            signalData.push(this.ecgSignal[i]);
        }
        
        // R-peak data
        const peakData = [];
        for (const peak of this.rPeaks) {
            const timeIndex = Math.round(peak / step);
            if (timeIndex < timeLabels.length) {
                peakData.push({
                    x: timeLabels[timeIndex],
                    y: this.ecgSignal[peak]
                });
            }
        }
        
        this.charts.detection.data.labels = timeLabels;
        this.charts.detection.data.datasets[0].data = signalData;
        this.charts.detection.data.datasets[1].data = peakData;
        this.charts.detection.update('none');
    }
    
    /**
     * Update RR interval chart
     */
    updateRRIntervalChart() {
        if (this.rPeaks.length < 2) return;
        
        const rrIntervals = [];
        const beatNumbers = [];
        
        for (let i = 1; i < this.rPeaks.length; i++) {
            const rrMs = (this.rPeaks[i] - this.rPeaks[i-1]) / this.samplingRate * 1000;
            rrIntervals.push(rrMs);
            beatNumbers.push(i);
        }
        
        this.charts.rrInterval.data.labels = beatNumbers;
        this.charts.rrInterval.data.datasets[0].data = rrIntervals;
        this.charts.rrInterval.update('none');
    }
    
    /**
     * Enable proceed button
     */
    enableProceedButton() {
        const proceedBtn = document.getElementById('proceedHRVBtn');
        if (proceedBtn) {
            proceedBtn.disabled = false;
        }
    }
    
    /**
     * Show loading overlay
     */
    showLoading(message = 'Processing...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.querySelector('p').textContent = message;
            overlay.classList.add('active');
        }
    }
    
    /**
     * Hide loading overlay
     */
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }
    
    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Global functions for HTML onclick handlers
window.detectRPeaks = function() {
    window.rPeakDetector.detectRPeaks();
};

window.resetDetection = function() {
    window.rPeakDetector.rPeaks = [];
    window.rPeakDetector.updateDetectionResults();
    window.rPeakDetector.updateCharts();
    console.log('Detection reset');
};

window.showProcessingStep = function(step) {
    window.rPeakDetector.currentStep = step;
    window.rPeakDetector.updateProcessingChart();
    
    // Update button states
    document.querySelectorAll('.chart-controls .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-small');
    });
    event.target.classList.add('btn-primary');
};

window.validatePeaks = function() {
    console.log('Validating peaks...');
    // Implementation for peak validation
};

window.enableAddPeak = function() {
    window.rPeakDetector.correctionMode = 'add';
    console.log('Add peak mode enabled');
};

window.enableRemovePeak = function() {
    window.rPeakDetector.correctionMode = 'remove';
    console.log('Remove peak mode enabled');
};

window.enableMovePeak = function() {
    window.rPeakDetector.correctionMode = 'move';
    console.log('Move peak mode enabled');
};

window.undoCorrection = function() {
    console.log('Undo correction');
    // Implementation for undo functionality
};

window.exportRPeaks = function() {
    console.log('Exporting R-peaks...');
    // Implementation for R-peak export
};

window.zoomToRegion = function() {
    console.log('Zoom to region');
    // Implementation for zoom functionality
};

window.resetZoom = function() {
    console.log('Reset zoom');
    // Implementation for zoom reset
};

window.togglePeakLabels = function() {
    console.log('Toggle peak labels');
    // Implementation for peak label toggle
};

window.proceedToHRVAnalysis = function() {
    if (window.BiosignalLab.rPeaks && window.BiosignalLab.rPeaks.length > 0) {
        window.location.href = 'hrv-analysis.html';
    } else {
        window.rPeakDetector.showNotification('Please detect R-peaks first', 'warning');
    }
};

// Initialize R-peak detector when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.rPeakDetector = new RPeakDetector();
});
