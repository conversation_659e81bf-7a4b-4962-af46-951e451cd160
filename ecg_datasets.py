"""
ECG Dataset Loading and Handling Utilities
Support for various ECG databases and formats
"""

import numpy as np
import os
import logging
from typing import Tuple, List, Dict, Optional, Union
import warnings

# Try to import wfdb for PhysioNet database support
try:
    import wfdb
    WFDB_AVAILABLE = True
except ImportError:
    WFDB_AVAILABLE = False
    warnings.warn("wfdb not available. PhysioNet database support disabled.")

# Try to import h5py for HDF5 file support
try:
    import h5py
    H5PY_AVAILABLE = True
except ImportError:
    H5PY_AVAILABLE = False
    warnings.warn("h5py not available. HDF5 file support disabled.")

logger = logging.getLogger(__name__)

class ECGDatasetLoader:
    """
    Utility class for loading ECG datasets from various sources
    """
    
    def __init__(self):
        """Initialize the dataset loader"""
        self.supported_formats = ['wfdb', 'csv', 'txt', 'mat', 'hdf5']
        
    def load_mit_bih_record(self, record_name: str, 
                           database_path: str = None,
                           channel: int = 0,
                           return_annotations: bool = False) -> Tuple[np.ndarray, float, Optional[Dict]]:
        """
        Load a record from MIT-BIH or other PhysioNet databases
        
        Args:
            record_name: Name of the record (e.g., '100', '101')
            database_path: Path to database directory
            channel: ECG channel to load (0 or 1)
            return_annotations: Whether to return annotations
            
        Returns:
            Tuple of (ecg_signal, sampling_rate, annotations)
        """
        if not WFDB_AVAILABLE:
            raise ImportError("wfdb package is required for PhysioNet database support")
        
        try:
            # Read the record
            if database_path:
                record = wfdb.rdrecord(os.path.join(database_path, record_name))
            else:
                # Try to read from PhysioNet directly
                record = wfdb.rdrecord(record_name, pn_dir='mitdb')
            
            # Extract ECG signal
            ecg_signal = record.p_signal[:, channel]
            sampling_rate = record.fs
            
            annotations = None
            if return_annotations:
                try:
                    if database_path:
                        annotation = wfdb.rdann(os.path.join(database_path, record_name), 'atr')
                    else:
                        annotation = wfdb.rdann(record_name, 'atr', pn_dir='mitdb')
                    
                    annotations = {
                        'sample': annotation.sample,
                        'symbol': annotation.symbol,
                        'fs': sampling_rate
                    }
                except:
                    logger.warning(f"Could not load annotations for record {record_name}")
            
            logger.info(f"Loaded record {record_name}: {len(ecg_signal)} samples at {sampling_rate} Hz")
            return ecg_signal, sampling_rate, annotations
            
        except Exception as e:
            logger.error(f"Error loading MIT-BIH record {record_name}: {e}")
            raise
    
    def load_csv_file(self, file_path: str, 
                     column: int = 0,
                     sampling_rate: float = 360.0,
                     skip_header: int = 0,
                     delimiter: str = ',') -> Tuple[np.ndarray, float]:
        """
        Load ECG data from CSV file
        
        Args:
            file_path: Path to CSV file
            column: Column index containing ECG data
            sampling_rate: Sampling rate in Hz
            skip_header: Number of header lines to skip
            delimiter: Column delimiter
            
        Returns:
            Tuple of (ecg_signal, sampling_rate)
        """
        try:
            # Load data using numpy
            data = np.loadtxt(file_path, delimiter=delimiter, skiprows=skip_header)
            
            # Extract ECG signal
            if data.ndim == 1:
                ecg_signal = data
            else:
                ecg_signal = data[:, column]
            
            logger.info(f"Loaded CSV file {file_path}: {len(ecg_signal)} samples")
            return ecg_signal, sampling_rate
            
        except Exception as e:
            logger.error(f"Error loading CSV file {file_path}: {e}")
            raise
    
    def load_txt_file(self, file_path: str, 
                     sampling_rate: float = 360.0,
                     skip_header: int = 0) -> Tuple[np.ndarray, float]:
        """
        Load ECG data from text file
        
        Args:
            file_path: Path to text file
            sampling_rate: Sampling rate in Hz
            skip_header: Number of header lines to skip
            
        Returns:
            Tuple of (ecg_signal, sampling_rate)
        """
        try:
            # Load data
            ecg_signal = np.loadtxt(file_path, skiprows=skip_header)
            
            logger.info(f"Loaded text file {file_path}: {len(ecg_signal)} samples")
            return ecg_signal, sampling_rate
            
        except Exception as e:
            logger.error(f"Error loading text file {file_path}: {e}")
            raise
    
    def load_hdf5_file(self, file_path: str, 
                      dataset_name: str = 'ecg',
                      sampling_rate: float = 360.0) -> Tuple[np.ndarray, float]:
        """
        Load ECG data from HDF5 file
        
        Args:
            file_path: Path to HDF5 file
            dataset_name: Name of dataset containing ECG data
            sampling_rate: Sampling rate in Hz
            
        Returns:
            Tuple of (ecg_signal, sampling_rate)
        """
        if not H5PY_AVAILABLE:
            raise ImportError("h5py package is required for HDF5 file support")
        
        try:
            with h5py.File(file_path, 'r') as f:
                # Try to find ECG dataset
                if dataset_name in f:
                    ecg_signal = f[dataset_name][:]
                else:
                    # Try common dataset names
                    possible_names = ['ecg', 'ECG', 'signal', 'data', 'values']
                    for name in possible_names:
                        if name in f:
                            ecg_signal = f[name][:]
                            break
                    else:
                        raise ValueError(f"Could not find ECG dataset in {file_path}")
                
                # Try to read sampling rate if available
                if 'fs' in f:
                    sampling_rate = f['fs'][()]
                elif 'sampling_rate' in f:
                    sampling_rate = f['sampling_rate'][()]
            
            logger.info(f"Loaded HDF5 file {file_path}: {len(ecg_signal)} samples")
            return ecg_signal, sampling_rate
            
        except Exception as e:
            logger.error(f"Error loading HDF5 file {file_path}: {e}")
            raise
    
    def load_ecg_data(self, file_path: str, 
                     file_format: str = 'auto',
                     **kwargs) -> Tuple[np.ndarray, float, Optional[Dict]]:
        """
        Generic ECG data loader that auto-detects format
        
        Args:
            file_path: Path to ECG file
            file_format: File format ('auto', 'wfdb', 'csv', 'txt', 'hdf5')
            **kwargs: Additional arguments for specific loaders
            
        Returns:
            Tuple of (ecg_signal, sampling_rate, annotations)
        """
        try:
            # Auto-detect format if not specified
            if file_format == 'auto':
                _, ext = os.path.splitext(file_path)
                ext = ext.lower()
                
                if ext == '.csv':
                    file_format = 'csv'
                elif ext in ['.txt', '.dat']:
                    file_format = 'txt'
                elif ext in ['.h5', '.hdf5']:
                    file_format = 'hdf5'
                elif ext == '.mat':
                    file_format = 'mat'
                else:
                    # Try WFDB format
                    file_format = 'wfdb'
            
            # Load data based on format
            annotations = None
            
            if file_format == 'wfdb':
                record_name = os.path.splitext(os.path.basename(file_path))[0]
                database_path = os.path.dirname(file_path)
                ecg_signal, sampling_rate, annotations = self.load_mit_bih_record(
                    record_name, database_path, **kwargs
                )
            elif file_format == 'csv':
                ecg_signal, sampling_rate = self.load_csv_file(file_path, **kwargs)
            elif file_format == 'txt':
                ecg_signal, sampling_rate = self.load_txt_file(file_path, **kwargs)
            elif file_format == 'hdf5':
                ecg_signal, sampling_rate = self.load_hdf5_file(file_path, **kwargs)
            elif file_format == 'mat':
                ecg_signal, sampling_rate = self.load_mat_file(file_path, **kwargs)
            else:
                raise ValueError(f"Unsupported file format: {file_format}")
            
            # Basic validation
            if len(ecg_signal) == 0:
                raise ValueError("Loaded ECG signal is empty")
            
            if sampling_rate <= 0:
                raise ValueError("Invalid sampling rate")
            
            logger.info(f"Successfully loaded ECG data: {len(ecg_signal)} samples at {sampling_rate} Hz")
            return ecg_signal, sampling_rate, annotations
            
        except Exception as e:
            logger.error(f"Error loading ECG data from {file_path}: {e}")
            raise
    
    def load_mat_file(self, file_path: str, 
                     variable_name: str = 'ecg',
                     sampling_rate: float = 360.0) -> Tuple[np.ndarray, float]:
        """
        Load ECG data from MATLAB .mat file
        
        Args:
            file_path: Path to .mat file
            variable_name: Name of variable containing ECG data
            sampling_rate: Sampling rate in Hz
            
        Returns:
            Tuple of (ecg_signal, sampling_rate)
        """
        try:
            from scipy.io import loadmat
            
            # Load MATLAB file
            mat_data = loadmat(file_path)
            
            # Try to find ECG data
            if variable_name in mat_data:
                ecg_signal = mat_data[variable_name].flatten()
            else:
                # Try common variable names
                possible_names = ['ecg', 'ECG', 'signal', 'data', 'val']
                for name in possible_names:
                    if name in mat_data:
                        ecg_signal = mat_data[name].flatten()
                        break
                else:
                    raise ValueError(f"Could not find ECG variable in {file_path}")
            
            # Try to read sampling rate
            if 'fs' in mat_data:
                sampling_rate = float(mat_data['fs'][0, 0])
            elif 'Fs' in mat_data:
                sampling_rate = float(mat_data['Fs'][0, 0])
            elif 'sampling_rate' in mat_data:
                sampling_rate = float(mat_data['sampling_rate'][0, 0])
            
            logger.info(f"Loaded MATLAB file {file_path}: {len(ecg_signal)} samples")
            return ecg_signal, sampling_rate
            
        except ImportError:
            raise ImportError("scipy is required for MATLAB file support")
        except Exception as e:
            logger.error(f"Error loading MATLAB file {file_path}: {e}")
            raise
    
    def get_sample_mit_bih_records(self) -> List[str]:
        """
        Get list of sample MIT-BIH records for testing
        
        Returns:
            List of record names
        """
        return [
            '100', '101', '102', '103', '104', '105', '106', '107', '108', '109',
            '111', '112', '113', '114', '115', '116', '117', '118', '119', '121',
            '122', '123', '124', '200', '201', '202', '203', '205', '207', '208',
            '209', '210', '212', '213', '214', '215', '217', '219', '220', '221',
            '222', '223', '228', '230', '231', '232', '233', '234'
        ]
    
    def preprocess_signal(self, ecg_signal: np.ndarray, 
                         sampling_rate: float,
                         remove_baseline: bool = True,
                         normalize: bool = True) -> np.ndarray:
        """
        Basic preprocessing of ECG signal
        
        Args:
            ecg_signal: Raw ECG signal
            sampling_rate: Sampling rate in Hz
            remove_baseline: Whether to remove baseline drift
            normalize: Whether to normalize the signal
            
        Returns:
            Preprocessed ECG signal
        """
        try:
            processed_signal = ecg_signal.copy()
            
            # Remove baseline drift using high-pass filter
            if remove_baseline:
                from scipy.signal import butter, filtfilt
                
                # Design high-pass filter (0.5 Hz cutoff)
                nyquist = sampling_rate / 2
                low_cutoff = 0.5 / nyquist
                
                if low_cutoff < 1.0:
                    b, a = butter(2, low_cutoff, btype='high')
                    processed_signal = filtfilt(b, a, processed_signal)
            
            # Normalize signal
            if normalize:
                signal_std = np.std(processed_signal)
                if signal_std > 0:
                    processed_signal = processed_signal / signal_std
            
            return processed_signal
            
        except Exception as e:
            logger.error(f"Error preprocessing signal: {e}")
            return ecg_signal

def create_dataset_info():
    """
    Create information about supported ECG datasets
    
    Returns:
        Dictionary with dataset information
    """
    datasets_info = {
        'MIT-BIH Arrhythmia Database': {
            'description': 'Standard database for arrhythmia analysis',
            'url': 'https://physionet.org/content/mitdb/',
            'sampling_rate': 360,
            'records': 48,
            'duration': '30 minutes each',
            'annotations': 'Beat annotations available'
        },
        'MIT-BIH Normal Sinus Rhythm Database': {
            'description': 'Normal sinus rhythm recordings',
            'url': 'https://physionet.org/content/nsrdb/',
            'sampling_rate': 128,
            'records': 18,
            'duration': '~24 hours each',
            'annotations': 'No beat annotations'
        },
        'European ST-T Database': {
            'description': 'ST-T change analysis',
            'url': 'https://physionet.org/content/edb/',
            'sampling_rate': 250,
            'records': 90,
            'duration': '2 hours each',
            'annotations': 'ST-T annotations available'
        },
        'QT Database': {
            'description': 'QT interval analysis',
            'url': 'https://physionet.org/content/qtdb/',
            'sampling_rate': 250,
            'records': 105,
            'duration': '15 minutes each',
            'annotations': 'QT annotations available'
        },
        'Long-term AF Database': {
            'description': 'Long-term atrial fibrillation recordings',
            'url': 'https://physionet.org/content/ltafdb/',
            'sampling_rate': 128,
            'records': 84,
            'duration': '24-25 hours each',
            'annotations': 'Rhythm annotations available'
        }
    }
    
    return datasets_info

def print_dataset_info():
    """
    Print information about supported datasets
    """
    datasets = create_dataset_info()
    
    print("SUPPORTED ECG DATASETS")
    print("=" * 50)
    
    for name, info in datasets.items():
        print(f"\n{name}:")
        print(f"  Description: {info['description']}")
        print(f"  URL: {info['url']}")
        print(f"  Sampling Rate: {info['sampling_rate']} Hz")
        print(f"  Records: {info['records']}")
        print(f"  Duration: {info['duration']}")
        print(f"  Annotations: {info['annotations']}")

if __name__ == "__main__":
    print_dataset_info()