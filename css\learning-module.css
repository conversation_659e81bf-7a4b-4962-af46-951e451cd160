/* ===================================
   Learning Module CSS - Biosignal Virtual Lab
   
   Author: Dr. <PERSON>il
   Institution: SUST - BME
   Year: 2025
   Contact: <EMAIL>
   
   Copyright © 2025 Dr. <PERSON> Ya<PERSON>ub Esmail
   =================================== */

/* Learning Module Header */
.learning-header {
    padding: 120px 0 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    position: relative;
    overflow: hidden;
}

.learning-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
}

.header-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    position: relative;
    z-index: 2;
}

.learning-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: white;
    margin-bottom: var(--spacing-lg);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

.learning-title i {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-right: var(--spacing-md);
}

.learning-subtitle {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-2xl);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-overview {
    display: flex;
    gap: var(--spacing-xl);
}

.progress-item {
    text-align: center;
    color: white;
}

.progress-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    background: rgba(255, 255, 255, 0.2);
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-sm);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.progress-label {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

/* Learning Preview */
.learning-preview {
    position: relative;
    perspective: 1000px;
}

.preview-screen {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-2xl);
    padding: var(--spacing-xl);
    transform: rotateY(-10deg) rotateX(5deg);
    transition: transform var(--transition-slow);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.preview-screen:hover {
    transform: rotateY(0deg) rotateX(0deg) scale(1.02);
}

.slide-preview {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-lg);
}

.preview-content {
    text-align: center;
}

.preview-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto var(--spacing-md);
    background: var(--primary-gradient);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    animation: pulse 2s ease-in-out infinite;
}

.preview-content h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.preview-waveform {
    height: 100px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Slide Navigation */
.slide-navigation {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    padding: var(--spacing-lg) 0;
    position: sticky;
    top: 70px;
    z-index: var(--z-sticky);
    backdrop-filter: blur(10px);
}

.nav-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    background: var(--primary-gradient);
    color: white;
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.nav-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-colored);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.slide-counter {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.progress-bar {
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
    width: 8.33%; /* 1/12 slides */
}

/* Slide Container */
.slide-container {
    min-height: 80vh;
    background: var(--bg-gradient-secondary);
}

.learning-swiper {
    height: 100%;
    padding: var(--spacing-2xl) 0;
}

.swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--spacing-lg);
}

/* Slide Content */
.slide-content {
    max-width: 1200px;
    width: 100%;
    background: var(--bg-gradient-primary);
    border: 1px solid var(--border-accent);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: slideIn 0.6s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-header {
    background: var(--primary-gradient);
    color: white;
    padding: var(--spacing-2xl);
    text-align: center;
    position: relative;
}

.slide-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.slide-number {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    background: rgba(255, 255, 255, 0.2);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-full);
    display: inline-block;
    margin-bottom: var(--spacing-md);
    backdrop-filter: blur(10px);
}

.slide-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slide-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
    font-weight: var(--font-weight-normal);
}

.slide-body {
    padding: var(--spacing-2xl);
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.content-text h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-xl);
}

.content-text p {
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-lg);
}

/* Info Cards */
.info-cards {
    display: grid;
    gap: var(--spacing-md);
}

.info-card {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.info-card:hover {
    transform: translateX(10px);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.card-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-gradient);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.info-card h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-weight-semibold);
}

.info-card p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* Visual Container */
.visual-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-light);
}

.demo-image {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.demo-photo {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
    cursor: pointer;
}

.demo-image:hover .image-overlay {
    opacity: 1;
}

.demo-image:hover .demo-photo {
    transform: scale(1.05);
}

.overlay-content {
    color: white;
    text-align: center;
}

.overlay-content i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-sm);
}

/* Signal Visualization */
.signal-visualization {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
}

/* Slide Footer */
.slide-footer {
    background: var(--bg-secondary);
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-light);
}

.key-points h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.key-points h4::before {
    content: "💡";
    font-size: var(--font-size-lg);
}

.key-points ul {
    list-style: none;
    padding: 0;
}

.key-points li {
    color: var(--text-secondary);
    padding: var(--spacing-sm) 0;
    position: relative;
    padding-left: var(--spacing-lg);
}

.key-points li::before {
    content: "✓";
    color: var(--success-color);
    font-weight: var(--font-weight-bold);
    position: absolute;
    left: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .progress-overview {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .learning-title {
        font-size: var(--font-size-2xl);
    }
    
    .slide-header {
        padding: var(--spacing-lg);
    }
    
    .slide-body {
        padding: var(--spacing-lg);
    }
    
    .nav-controls {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .progress-overview {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

/* ECG Components Styling */
.ecg-components {
    display: grid;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.component-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 2px solid transparent;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.component-item:hover {
    border-color: var(--primary-color);
    transform: translateX(5px);
    box-shadow: var(--shadow-md);
}

.component-marker {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
}

.component-info h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-base);
}

.component-info p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    margin: 0;
}

/* Heart Anatomy Visualization */
.heart-anatomy {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.anatomy-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.anatomy-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.electrical-path {
    position: relative;
    width: 100%;
    height: 100%;
}

.path-point {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: var(--radius-full);
    background: var(--accent-color);
    border: 3px solid white;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.path-point:hover {
    transform: scale(1.2);
}

.sa-node {
    top: 30%;
    right: 25%;
}

.av-node {
    top: 60%;
    right: 40%;
}

.pulse-animation {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 2px solid var(--accent-color);
    border-radius: var(--radius-full);
    animation: pulse 2s ease-in-out infinite;
}

/* ECG Waveform Demo */
.ecg-waveform-demo {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
}

.waveform-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-md);
}

.control-btn {
    width: 48px;
    height: 48px;
    border: none;
    border-radius: var(--radius-full);
    background: var(--primary-gradient);
    color: white;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
}

.control-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-colored);
}

.heart-rate-display {
    background: var(--bg-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

/* Interactive Quiz */
.interactive-quiz {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-light);
}

.interactive-quiz h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.interactive-quiz h4::before {
    content: "🤔";
    font-size: var(--font-size-lg);
}

.quiz-question p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-weight-medium);
}

.quiz-options {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.quiz-option {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 2px solid var(--border-medium);
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: var(--font-weight-medium);
}

.quiz-option:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.quiz-option.correct {
    border-color: var(--success-color);
    background: var(--success-color);
    color: white;
}

.quiz-option.incorrect {
    border-color: var(--error-color);
    background: var(--error-color);
    color: white;
}

/* Acquisition Steps */
.acquisition-steps {
    display: grid;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border-left: 4px solid var(--primary-color);
    transition: all var(--transition-normal);
}

.step-item:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-md);
}

.step-number {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.step-content h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.step-content p {
    color: var(--text-secondary);
    margin: 0;
    line-height: var(--line-height-relaxed);
}

/* Signal Flow Diagram */
.signal-flow {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: rgba(255, 255, 255, 0.9);
    border-radius: var(--radius-lg);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.flow-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.flow-step:hover {
    background: var(--bg-secondary);
    transform: translateY(-5px);
}

.flow-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-gradient);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-lg);
}

.flow-step span {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.flow-arrow {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
}

/* Sampling Demo */
.sampling-demo {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
}

.sampling-demo h4 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.sampling-controls {
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.sampling-controls label {
    display: block;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
}

.sampling-controls input[type="range"] {
    width: 100%;
    max-width: 300px;
    height: 6px;
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    outline: none;
    -webkit-appearance: none;
}

.sampling-controls input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: var(--radius-full);
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.sampling-controls input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: var(--radius-full);
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-sm);
}

/* Slide Thumbnails */
.slide-thumbnails {
    background: var(--bg-primary);
    padding: var(--spacing-2xl) 0;
    border-top: 1px solid var(--border-light);
}

.slide-thumbnails h3 {
    text-align: center;
    color: var(--text-primary);
    margin-bottom: var(--spacing-2xl);
}

.thumbnail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.thumbnail-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.thumbnail-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.thumbnail-item.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.thumbnail-number {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: var(--font-weight-bold);
    flex-shrink: 0;
}

.thumbnail-item.active .thumbnail-number {
    background: rgba(255, 255, 255, 0.2);
}

.thumbnail-content h4 {
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-base);
}

.thumbnail-content p {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    margin: 0;
}
