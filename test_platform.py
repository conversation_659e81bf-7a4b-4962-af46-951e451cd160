"""
Test script for the ECG Processing Platform
Tests all major functionality with various scenarios
"""

import numpy as np
import matplotlib.pyplot as plt
import unittest
import logging
from ecg_processor import ECGProcessor, HRVAnalyzer
from ecg_datasets import ECGDatasetLoader
from example_usage import generate_synthetic_ecg

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestECGProcessor(unittest.TestCase):
    """Test cases for ECG processor functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.processor = ECGProcessor(sampling_rate=360.0)
        self.hrv_analyzer = HRVAnalyzer(sampling_rate=360.0)
        self.test_signal = generate_synthetic_ecg(duration=30.0, sampling_rate=360.0)
    
    def test_bandpass_filter(self):
        """Test bandpass filtering"""
        filtered_signal = self.processor.bandpass_filter(self.test_signal)
        
        # Check that filtering doesn't change signal length
        self.assertEqual(len(filtered_signal), len(self.test_signal))
        
        # Check that filtering reduces noise (signal should be smoother)
        self.assertLess(np.std(np.diff(filtered_signal)), np.std(np.diff(self.test_signal)))
    
    def test_differentiation(self):
        """Test signal differentiation"""
        diff_signal = self.processor.differentiate_signal(self.test_signal)
        
        # Check that differentiation doesn't change signal length
        self.assertEqual(len(diff_signal), len(self.test_signal))
        
        # Check that differentiation emphasizes sharp changes
        self.assertGreater(np.max(np.abs(diff_signal)), 0)
    
    def test_r_peak_detection(self):
        """Test R-peak detection"""
        r_peaks, processing_info = self.processor.detect_r_peaks(self.test_signal)
        
        # Check that peaks are detected
        self.assertGreater(len(r_peaks), 0)
        
        # Check that all peaks are within signal bounds
        self.assertTrue(np.all(r_peaks >= 0))
        self.assertTrue(np.all(r_peaks < len(self.test_signal)))
        
        # Check that processing info is provided
        self.assertIn('filtered', processing_info)
        self.assertIn('integrated', processing_info)
    
    def test_r_peak_validation(self):
        """Test R-peak validation"""
        r_peaks, _ = self.processor.detect_r_peaks(self.test_signal)
        validated_peaks, validation_info = self.processor.validate_r_peaks(r_peaks, len(self.test_signal))
        
        # Check that validation info is provided
        self.assertIn('original_count', validation_info)
        self.assertIn('final_count', validation_info)
        
        # Check that validated peaks are reasonable
        if len(validated_peaks) > 1:
            rr_intervals = np.diff(validated_peaks) / self.processor.sampling_rate
            self.assertTrue(np.all(rr_intervals > 0.3))  # Min 0.3s between peaks
            self.assertTrue(np.all(rr_intervals < 2.0))  # Max 2.0s between peaks
    
    def test_empty_signal(self):
        """Test handling of empty signal"""
        empty_signal = np.array([])
        r_peaks, _ = self.processor.detect_r_peaks(empty_signal)
        
        # Should return empty array without crashing
        self.assertEqual(len(r_peaks), 0)
    
    def test_short_signal(self):
        """Test handling of very short signal"""
        short_signal = np.random.normal(0, 1, 10)
        r_peaks, _ = self.processor.detect_r_peaks(short_signal)
        
        # Should handle short signals gracefully
        self.assertIsInstance(r_peaks, np.ndarray)

class TestHRVAnalyzer(unittest.TestCase):
    """Test cases for HRV analyzer functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.hrv_analyzer = HRVAnalyzer(sampling_rate=360.0)
        self.processor = ECGProcessor(sampling_rate=360.0)
        
        # Generate test signal and detect R-peaks
        self.test_signal = generate_synthetic_ecg(duration=60.0, sampling_rate=360.0)
        self.r_peaks, _ = self.processor.detect_r_peaks(self.test_signal)
        self.validated_peaks, _ = self.processor.validate_r_peaks(self.r_peaks, len(self.test_signal))
    
    def test_rr_interval_calculation(self):
        """Test RR interval calculation"""
        rr_intervals = self.hrv_analyzer.calculate_rr_intervals(self.validated_peaks)
        
        # Check that we get reasonable RR intervals
        self.assertGreater(len(rr_intervals), 0)
        self.assertTrue(np.all(rr_intervals > 0))
        self.assertTrue(np.all(rr_intervals < 2.0))  # Less than 2 seconds
    
    def test_time_domain_features(self):
        """Test time domain HRV features"""
        rr_intervals = self.hrv_analyzer.calculate_rr_intervals(self.validated_peaks)
        features = self.hrv_analyzer.time_domain_features(rr_intervals)
        
        # Check that all expected features are present
        expected_features = ['mean_rr', 'std_rr', 'sdnn', 'rmssd', 'pnn50', 'mean_hr']
        for feature in expected_features:
            self.assertIn(feature, features)
            self.assertIsInstance(features[feature], (int, float))
            self.assertGreater(features[feature], 0)
    
    def test_frequency_domain_features(self):
        """Test frequency domain HRV features"""
        rr_intervals = self.hrv_analyzer.calculate_rr_intervals(self.validated_peaks)
        
        if len(rr_intervals) >= 10:
            features = self.hrv_analyzer.frequency_domain_features(rr_intervals)
            
            # Check that power features are present
            power_features = ['vlf_power', 'lf_power', 'hf_power', 'total_power']
            for feature in power_features:
                self.assertIn(feature, features)
                self.assertIsInstance(features[feature], (int, float))
                self.assertGreaterEqual(features[feature], 0)
    
    def test_hrv_analysis(self):
        """Test comprehensive HRV analysis"""
        analysis_results = self.hrv_analyzer.analyze_hrv(self.validated_peaks, len(self.test_signal))
        
        # Check that analysis completed successfully
        self.assertIn('status', analysis_results)
        self.assertIn('time_domain', analysis_results)
        self.assertIn('quality_metrics', analysis_results)
        
        if analysis_results['status'] == 'success':
            self.assertGreater(len(analysis_results['time_domain']), 0)
            self.assertGreater(len(analysis_results['quality_metrics']), 0)
    
    def test_insufficient_peaks(self):
        """Test HRV analysis with insufficient peaks"""
        insufficient_peaks = np.array([100, 200])  # Only 2 peaks
        analysis_results = self.hrv_analyzer.analyze_hrv(insufficient_peaks, 1000)
        
        # Should handle insufficient peaks gracefully
        self.assertEqual(analysis_results['status'], 'insufficient_peaks')
        self.assertIn('error', analysis_results)
    
    def test_quality_metrics(self):
        """Test quality metrics calculation"""
        analysis_results = self.hrv_analyzer.analyze_hrv(self.validated_peaks, len(self.test_signal))
        
        if analysis_results['status'] == 'success':
            quality_metrics = analysis_results['quality_metrics']
            
            # Check that quality metrics are reasonable
            self.assertIn('data_completeness', quality_metrics)
            self.assertIn('quality_score', quality_metrics)
            
            self.assertGreaterEqual(quality_metrics['data_completeness'], 0.0)
            self.assertLessEqual(quality_metrics['data_completeness'], 1.0)
            
            self.assertGreaterEqual(quality_metrics['quality_score'], 0.0)
            self.assertLessEqual(quality_metrics['quality_score'], 100.0)

class TestECGDatasetLoader(unittest.TestCase):
    """Test cases for ECG dataset loader"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.loader = ECGDatasetLoader()
    
    def test_supported_formats(self):
        """Test that supported formats are defined"""
        self.assertIsInstance(self.loader.supported_formats, list)
        self.assertGreater(len(self.loader.supported_formats), 0)
    
    def test_sample_records(self):
        """Test that sample MIT-BIH records are available"""
        records = self.loader.get_sample_mit_bih_records()
        self.assertIsInstance(records, list)
        self.assertGreater(len(records), 0)
    
    def test_preprocessing(self):
        """Test signal preprocessing"""
        test_signal = generate_synthetic_ecg(duration=10.0, sampling_rate=360.0)
        processed_signal = self.loader.preprocess_signal(test_signal, 360.0)
        
        # Check that preprocessing doesn't change signal length
        self.assertEqual(len(processed_signal), len(test_signal))
        
        # Check that preprocessing is applied
        self.assertFalse(np.array_equal(processed_signal, test_signal))

def run_performance_tests():
    """Run performance tests on the ECG processing platform"""
    print("\n" + "="*50)
    print("PERFORMANCE TESTS")
    print("="*50)
    
    import time
    
    processor = ECGProcessor(sampling_rate=360.0)
    hrv_analyzer = HRVAnalyzer(sampling_rate=360.0)
    
    # Test different signal lengths
    durations = [10, 30, 60, 300]  # 10s, 30s, 1min, 5min
    
    for duration in durations:
        print(f"\nTesting {duration}s signal:")
        
        # Generate test signal
        test_signal = generate_synthetic_ecg(duration=duration, sampling_rate=360.0)
        
        # Time R-peak detection
        start_time = time.time()
        r_peaks, _ = processor.detect_r_peaks(test_signal)
        detection_time = time.time() - start_time
        
        print(f"  R-peak detection: {detection_time:.3f}s ({len(r_peaks)} peaks)")
        
        # Time HRV analysis
        if len(r_peaks) > 2:
            start_time = time.time()
            analysis_results = hrv_analyzer.analyze_hrv(r_peaks, len(test_signal))
            hrv_time = time.time() - start_time
            
            print(f"  HRV analysis: {hrv_time:.3f}s ({analysis_results['status']})")
        
        # Calculate processing rate
        processing_rate = duration / (detection_time + hrv_time if len(r_peaks) > 2 else detection_time)
        print(f"  Processing rate: {processing_rate:.1f}x real-time")

def run_accuracy_tests():
    """Run accuracy tests with known ground truth"""
    print("\n" + "="*50)
    print("ACCURACY TESTS")
    print("="*50)
    
    processor = ECGProcessor(sampling_rate=360.0)
    hrv_analyzer = HRVAnalyzer(sampling_rate=360.0)
    
    # Test different heart rates
    test_heart_rates = [50, 60, 70, 80, 90, 100, 120]
    
    for heart_rate in test_heart_rates:
        print(f"\nTesting HR {heart_rate} bpm:")
        
        # Generate synthetic ECG with known heart rate
        test_signal = generate_synthetic_ecg(duration=60.0, 
                                           sampling_rate=360.0,
                                           heart_rate=heart_rate,
                                           noise_level=0.05)
        
        # Detect R-peaks
        r_peaks, _ = processor.detect_r_peaks(test_signal)
        validated_peaks, _ = processor.validate_r_peaks(r_peaks, len(test_signal))
        
        if len(validated_peaks) > 1:
            # Calculate detected heart rate
            rr_intervals = np.diff(validated_peaks) / 360.0
            detected_hr = 60.0 / np.mean(rr_intervals)
            
            # Calculate accuracy
            accuracy = 100 - abs(detected_hr - heart_rate) / heart_rate * 100
            
            print(f"  Expected: {heart_rate} bpm")
            print(f"  Detected: {detected_hr:.1f} bpm")
            print(f"  Accuracy: {accuracy:.1f}%")
            print(f"  Detected peaks: {len(validated_peaks)}")

def run_noise_robustness_tests():
    """Test robustness to different noise levels"""
    print("\n" + "="*50)
    print("NOISE ROBUSTNESS TESTS")
    print("="*50)
    
    processor = ECGProcessor(sampling_rate=360.0)
    noise_levels = [0.01, 0.05, 0.1, 0.2, 0.3, 0.5]
    
    for noise_level in noise_levels:
        print(f"\nTesting noise level {noise_level}:")
        
        # Generate noisy signal
        test_signal = generate_synthetic_ecg(duration=30.0,
                                           sampling_rate=360.0,
                                           heart_rate=75.0,
                                           noise_level=noise_level)
        
        # Detect R-peaks
        r_peaks, _ = processor.detect_r_peaks(test_signal)
        validated_peaks, validation_info = processor.validate_r_peaks(r_peaks, len(test_signal))
        
        # Calculate metrics
        detection_rate = len(validated_peaks) / 30.0 * 60.0  # peaks per minute
        validation_rate = validation_info['final_count'] / validation_info['original_count'] * 100
        
        print(f"  Detected peaks: {len(r_peaks)}")
        print(f"  Validated peaks: {len(validated_peaks)}")
        print(f"  Detection rate: {detection_rate:.1f} peaks/min")
        print(f"  Validation rate: {validation_rate:.1f}%")

def main():
    """Main test function"""
    print("ECG PROCESSING PLATFORM - COMPREHENSIVE TESTS")
    print("="*60)
    
    # Run unit tests
    print("\nRunning unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance tests
    run_performance_tests()
    
    # Run accuracy tests
    run_accuracy_tests()
    
    # Run noise robustness tests
    run_noise_robustness_tests()
    
    print("\n" + "="*60)
    print("ALL TESTS COMPLETED")
    print("="*60)

if __name__ == "__main__":
    main()