<!DOCTYPE html>
<!--
    Biosignal Virtual Lab - Signal Processing Module

    Author: Dr. <PERSON>smail
    Institution: SUST - BME (Sudan University of Science and Technology - Biomedical Engineering)
    Year: 2025

    Contact Information:
    Email: <EMAIL>
    Phone: +249912867327, +966538076790

    Copyright © 2025 Dr. <PERSON>il. All rights reserved.
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signal Processing - Biosignal Virtual Lab</title>
    <meta name="author" content="Dr. <PERSON>">
    <meta name="copyright" content="© 2025 Dr. <PERSON>">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/modules.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-heartbeat"></i>
                <span>Biosignal Virtual Lab</span>
            </div>
            <div class="nav-menu">
                <a href="index.html" class="nav-link">Home</a>
                <a href="signal-processing.html" class="nav-link active">Signal Processing</a>
                <a href="r-peak-detection.html" class="nav-link">R-Peak Detection</a>
                <a href="hrv-analysis.html" class="nav-link">HRV Analysis</a>
                <a href="results.html" class="nav-link">Results</a>
                <div class="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumbs -->
    <div class="breadcrumbs-container">
        <div class="container">
            <nav class="breadcrumbs">
                <a href="index.html">Home</a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current">Signal Processing</span>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-title">
                    <i class="fas fa-wave-square"></i>
                    <h1>ECG Signal Processing</h1>
                </div>
                <p class="page-description">
                    Upload, preprocess, and analyze ECG signals with advanced filtering and noise reduction techniques.
                </p>
            </div>

            <!-- Processing Pipeline -->
            <div class="processing-pipeline">
                <div class="pipeline-step active" data-step="upload">
                    <div class="step-icon">
                        <i class="fas fa-upload"></i>
                    </div>
                    <div class="step-content">
                        <h3>Data Upload</h3>
                        <p>Load ECG data</p>
                    </div>
                </div>
                <div class="pipeline-arrow">→</div>
                <div class="pipeline-step" data-step="preprocess">
                    <div class="step-icon">
                        <i class="fas fa-filter"></i>
                    </div>
                    <div class="step-content">
                        <h3>Preprocessing</h3>
                        <p>Filter & clean</p>
                    </div>
                </div>
                <div class="pipeline-arrow">→</div>
                <div class="pipeline-step" data-step="analyze">
                    <div class="step-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="step-content">
                        <h3>Analysis</h3>
                        <p>Quality check</p>
                    </div>
                </div>
                <div class="pipeline-arrow">→</div>
                <div class="pipeline-step" data-step="export">
                    <div class="step-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="step-content">
                        <h3>Export</h3>
                        <p>Save results</p>
                    </div>
                </div>
            </div>

            <!-- Main Processing Interface -->
            <div class="processing-interface">
                <!-- Left Panel - Controls -->
                <div class="control-panel">
                    <!-- Data Input Section -->
                    <div class="control-section active" id="upload-section">
                        <h3><i class="fas fa-upload"></i> Data Input</h3>
                        
                        <div class="input-tabs">
                            <button class="tab-btn active" data-tab="file-upload">File Upload</button>
                            <button class="tab-btn" data-tab="sample-data">Sample Data</button>
                            <button class="tab-btn" data-tab="synthetic">Generate Synthetic</button>
                        </div>

                        <!-- File Upload Tab -->
                        <div class="tab-content active" id="file-upload">
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h4>Drop ECG files here or click to browse</h4>
                                <p>Supported formats: CSV, TXT, WFDB, MAT</p>
                                <input type="file" id="fileInput" accept=".csv,.txt,.dat,.mat" multiple hidden>
                                <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                    <i class="fas fa-folder-open"></i> Browse Files
                                </button>
                            </div>
                            
                            <div class="file-settings">
                                <div class="form-group">
                                    <label class="form-label">Sampling Rate (Hz)</label>
                                    <input type="number" class="form-input" id="samplingRate" value="360" min="100" max="2000">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Data Column</label>
                                    <select class="form-select" id="dataColumn">
                                        <option value="0">Column 1</option>
                                        <option value="1">Column 2</option>
                                        <option value="2">Column 3</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Skip Header Rows</label>
                                    <input type="number" class="form-input" id="skipRows" value="0" min="0" max="10">
                                </div>
                            </div>
                        </div>

                        <!-- Sample Data Tab -->
                        <div class="tab-content" id="sample-data">
                            <div class="sample-datasets">
                                <div class="dataset-card" data-dataset="mit-bih-100">
                                    <h4>MIT-BIH Record 100</h4>
                                    <p>Normal sinus rhythm, 30 minutes</p>
                                    <div class="dataset-info">
                                        <span class="info-tag">360 Hz</span>
                                        <span class="info-tag">Normal</span>
                                    </div>
                                    <button type="button" class="btn btn-outline btn-small">Load Dataset</button>
                                </div>
                                
                                <div class="dataset-card" data-dataset="mit-bih-101">
                                    <h4>MIT-BIH Record 101</h4>
                                    <p>Atrial premature beats</p>
                                    <div class="dataset-info">
                                        <span class="info-tag">360 Hz</span>
                                        <span class="info-tag">Arrhythmia</span>
                                    </div>
                                    <button type="button" class="btn btn-outline btn-small">Load Dataset</button>
                                </div>
                                
                                <div class="dataset-card" data-dataset="nsr-001">
                                    <h4>NSR Database 001</h4>
                                    <p>Normal sinus rhythm, long-term</p>
                                    <div class="dataset-info">
                                        <span class="info-tag">128 Hz</span>
                                        <span class="info-tag">Normal</span>
                                    </div>
                                    <button type="button" class="btn btn-outline btn-small">Load Dataset</button>
                                </div>
                            </div>
                        </div>

                        <!-- Synthetic Data Tab -->
                        <div class="tab-content" id="synthetic">
                            <div class="synthetic-controls">
                                <div class="form-group">
                                    <label class="form-label">Duration (seconds)</label>
                                    <input type="number" class="form-input" id="syntheticDuration" value="60" min="10" max="300">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Heart Rate (BPM)</label>
                                    <input type="number" class="form-input" id="syntheticHeartRate" value="70" min="40" max="150">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Noise Level</label>
                                    <select class="form-select" id="noiseLevel">
                                        <option value="0">No Noise</option>
                                        <option value="0.05">Low Noise</option>
                                        <option value="0.1" selected>Medium Noise</option>
                                        <option value="0.2">High Noise</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Arrhythmia Type</label>
                                    <select class="form-select" id="arrhythmiaType">
                                        <option value="normal" selected>Normal Sinus Rhythm</option>
                                        <option value="bradycardia">Bradycardia</option>
                                        <option value="tachycardia">Tachycardia</option>
                                        <option value="irregular">Irregular Rhythm</option>
                                    </select>
                                </div>
                                <button type="button" class="btn btn-primary" onclick="generateSyntheticECG()">
                                    <i class="fas fa-magic"></i> Generate ECG
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Preprocessing Section -->
                    <div class="control-section" id="preprocess-section">
                        <h3><i class="fas fa-filter"></i> Signal Preprocessing</h3>
                        
                        <div class="filter-controls">
                            <div class="form-group">
                                <label class="form-label">
                                    <input type="checkbox" id="enableBandpass" checked> 
                                    Bandpass Filter
                                </label>
                                <div class="filter-range">
                                    <div class="range-input">
                                        <label>Low Freq (Hz)</label>
                                        <input type="number" class="form-input" id="lowFreq" value="0.5" min="0.1" max="10" step="0.1">
                                    </div>
                                    <div class="range-input">
                                        <label>High Freq (Hz)</label>
                                        <input type="number" class="form-input" id="highFreq" value="40" min="10" max="100" step="1">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <input type="checkbox" id="enableNotch"> 
                                    Notch Filter (50/60 Hz)
                                </label>
                                <select class="form-select" id="notchFreq">
                                    <option value="50">50 Hz</option>
                                    <option value="60">60 Hz</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <input type="checkbox" id="enableBaseline"> 
                                    Baseline Correction
                                </label>
                                <select class="form-select" id="baselineMethod">
                                    <option value="detrend">Linear Detrend</option>
                                    <option value="highpass">High-pass Filter</option>
                                    <option value="median">Median Filter</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">
                                    <input type="checkbox" id="enableNormalize"> 
                                    Normalize Signal
                                </label>
                                <select class="form-select" id="normalizeMethod">
                                    <option value="minmax">Min-Max [0,1]</option>
                                    <option value="zscore">Z-Score</option>
                                    <option value="robust">Robust Scaling</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="processing-actions">
                            <button type="button" class="btn btn-primary" onclick="applyPreprocessing()">
                                <i class="fas fa-play"></i> Apply Filters
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="resetPreprocessing()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>

                    <!-- Quality Assessment Section -->
                    <div class="control-section" id="analyze-section">
                        <h3><i class="fas fa-chart-line"></i> Quality Assessment</h3>
                        
                        <div class="quality-metrics">
                            <div class="metric-card">
                                <div class="metric-label">Signal Quality Index</div>
                                <div class="metric-value" id="qualityIndex">--</div>
                                <div class="metric-bar">
                                    <div class="metric-fill" id="qualityBar"></div>
                                </div>
                            </div>
                            
                            <div class="metric-card">
                                <div class="metric-label">SNR (dB)</div>
                                <div class="metric-value" id="snrValue">--</div>
                            </div>
                            
                            <div class="metric-card">
                                <div class="metric-label">Baseline Drift</div>
                                <div class="metric-value" id="baselineDrift">--</div>
                            </div>
                            
                            <div class="metric-card">
                                <div class="metric-label">Artifacts Detected</div>
                                <div class="metric-value" id="artifactCount">--</div>
                            </div>
                        </div>
                        
                        <button type="button" class="btn btn-primary" onclick="assessQuality()">
                            <i class="fas fa-search"></i> Analyze Quality
                        </button>
                    </div>

                    <!-- Export Section -->
                    <div class="control-section" id="export-section">
                        <h3><i class="fas fa-download"></i> Export Data</h3>
                        
                        <div class="export-options">
                            <div class="form-group">
                                <label class="form-label">Export Format</label>
                                <select class="form-select" id="exportFormat">
                                    <option value="csv">CSV</option>
                                    <option value="txt">TXT</option>
                                    <option value="json">JSON</option>
                                    <option value="mat">MATLAB</option>
                                </select>
                            </div>
                            
                            <div class="export-checkboxes">
                                <label class="form-label">
                                    <input type="checkbox" id="exportRaw" checked> Raw Signal
                                </label>
                                <label class="form-label">
                                    <input type="checkbox" id="exportFiltered" checked> Filtered Signal
                                </label>
                                <label class="form-label">
                                    <input type="checkbox" id="exportMetadata" checked> Metadata
                                </label>
                            </div>
                        </div>
                        
                        <div class="export-actions">
                            <button type="button" class="btn btn-primary" onclick="exportData()">
                                <i class="fas fa-download"></i> Export Data
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="exportReport()">
                                <i class="fas fa-file-pdf"></i> Export Report
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Right Panel - Visualization -->
                <div class="visualization-panel">
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>ECG Signal Visualization</h3>
                            <div class="chart-controls">
                                <button type="button" class="btn btn-small" onclick="toggleSignalView('raw')">Raw</button>
                                <button type="button" class="btn btn-small btn-primary" onclick="toggleSignalView('filtered')">Filtered</button>
                                <button type="button" class="btn btn-small" onclick="toggleSignalView('both')">Both</button>
                            </div>
                        </div>
                        <div class="chart-area">
                            <canvas id="ecgChart" width="800" height="400"></canvas>
                        </div>
                        <div class="chart-info">
                            <div class="info-item">
                                <span class="info-label">Duration:</span>
                                <span class="info-value" id="signalDuration">--</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Samples:</span>
                                <span class="info-value" id="sampleCount">--</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Sampling Rate:</span>
                                <span class="info-value" id="displaySamplingRate">--</span>
                            </div>
                        </div>
                    </div>

                    <!-- Frequency Domain Analysis -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3>Frequency Domain Analysis</h3>
                        </div>
                        <div class="chart-area">
                            <canvas id="spectrumChart" width="800" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button type="button" class="btn btn-secondary" onclick="window.location.href='index.html'">
                    <i class="fas fa-arrow-left"></i> Back to Home
                </button>
                <button type="button" class="btn btn-primary" onclick="proceedToRPeakDetection()" id="proceedBtn" disabled>
                    <i class="fas fa-arrow-right"></i> Proceed to R-Peak Detection
                </button>
            </div>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>Processing signal...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/plotly.js-dist@2.26.0/plotly.min.js"></script>
    <script src="js/core/utils.js"></script>
    <script src="js/core/navigation.js"></script>
    <script src="js/modules/signal-processing.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
