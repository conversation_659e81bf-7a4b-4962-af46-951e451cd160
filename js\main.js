/**
 * Biosignal Virtual Lab - Main JavaScript
 * ECG HRV Analysis Platform
 */

// Global application state
window.BiosignalLab = {
    currentModule: null,
    ecgData: null,
    rPeaks: null,
    hrvResults: null,
    settings: {
        theme: 'light',
        samplingRate: 360,
        filterSettings: {
            lowpass: 15,
            highpass: 5
        }
    }
};

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * Initialize the application
 */
function initializeApp() {
    console.log('Initializing Biosignal Virtual Lab...');
    
    // Initialize components
    initializeNavigation();
    initializeScrollAnimations();
    initializeHeroCanvas();
    initializeThemeToggle();
    
    // Load saved settings
    loadSettings();
    
    // Hide loading overlay
    hideLoadingOverlay();
    
    console.log('Application initialized successfully');
}

/**
 * Navigation functions
 */
function navigateToModule(moduleName) {
    console.log(`Navigating to module: ${moduleName}`);
    
    // Show loading overlay
    showLoadingOverlay();
    
    // Update current module
    window.BiosignalLab.currentModule = moduleName;
    
    // Simulate navigation delay
    setTimeout(() => {
        const moduleUrls = {
            'signal-processing': 'signal-processing.html',
            'r-peak-detection': 'r-peak-detection.html',
            'hrv-analysis': 'hrv-analysis.html',
            'data-management': 'data-management.html',
            'visualization': 'visualization.html',
            'results': 'results.html',
            'documentation': 'documentation.html'
        };
        
        const url = moduleUrls[moduleName];
        if (url) {
            window.location.href = url;
        } else {
            console.warn(`Module ${moduleName} not found`);
            hideLoadingOverlay();
        }
    }, 500);
}

/**
 * Start guided tutorial
 */
function startGuidedTour() {
    console.log('Starting guided tutorial...');
    
    // Show tutorial modal or navigate to tutorial page
    showNotification('Guided tutorial will be available soon!', 'info');
}

/**
 * Load sample data
 */
function loadSampleData() {
    console.log('Loading sample data...');
    
    showLoadingOverlay();
    
    // Simulate loading sample ECG data
    setTimeout(() => {
        // Generate synthetic ECG data for demonstration
        const sampleData = generateSyntheticECG(60, 360); // 60 seconds at 360 Hz
        window.BiosignalLab.ecgData = sampleData;
        
        hideLoadingOverlay();
        showNotification('Sample ECG data loaded successfully!', 'success');
        
        // Navigate to signal processing module
        setTimeout(() => {
            navigateToModule('signal-processing');
        }, 1000);
    }, 1500);
}

/**
 * Generate synthetic ECG data for demonstration
 */
function generateSyntheticECG(duration, samplingRate) {
    const numSamples = duration * samplingRate;
    const ecgData = new Array(numSamples);
    const heartRate = 70; // BPM
    const rrInterval = 60 / heartRate; // seconds
    const samplesPerBeat = rrInterval * samplingRate;
    
    for (let i = 0; i < numSamples; i++) {
        const t = i / samplingRate;
        const beatPhase = (t % rrInterval) / rrInterval;
        
        // Simple ECG waveform approximation
        let amplitude = 0;
        
        // P wave (0.1-0.2)
        if (beatPhase >= 0.1 && beatPhase <= 0.2) {
            amplitude += 0.2 * Math.sin(Math.PI * (beatPhase - 0.1) / 0.1);
        }
        
        // QRS complex (0.35-0.45)
        if (beatPhase >= 0.35 && beatPhase <= 0.45) {
            const qrsPhase = (beatPhase - 0.35) / 0.1;
            if (qrsPhase < 0.3) {
                amplitude -= 0.3 * Math.sin(Math.PI * qrsPhase / 0.3); // Q wave
            } else if (qrsPhase < 0.7) {
                amplitude += 1.0 * Math.sin(Math.PI * (qrsPhase - 0.3) / 0.4); // R wave
            } else {
                amplitude -= 0.4 * Math.sin(Math.PI * (qrsPhase - 0.7) / 0.3); // S wave
            }
        }
        
        // T wave (0.6-0.8)
        if (beatPhase >= 0.6 && beatPhase <= 0.8) {
            amplitude += 0.3 * Math.sin(Math.PI * (beatPhase - 0.6) / 0.2);
        }
        
        // Add some noise
        amplitude += (Math.random() - 0.5) * 0.05;
        
        ecgData[i] = amplitude;
    }
    
    return {
        signal: ecgData,
        samplingRate: samplingRate,
        duration: duration,
        timestamp: new Date().toISOString()
    };
}

/**
 * Initialize hero canvas animation
 */
function initializeHeroCanvas() {
    const canvas = document.getElementById('heroECGCanvas');
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    // Generate ECG-like waveform
    const points = [];
    const numPoints = 200;
    
    for (let i = 0; i < numPoints; i++) {
        const x = (i / numPoints) * width;
        let y = height / 2;
        
        // Create ECG-like pattern
        const phase = (i / numPoints) * 4 * Math.PI;
        if (i % 40 < 5) {
            // QRS complex
            const qrsPhase = (i % 40) / 5;
            y += Math.sin(qrsPhase * Math.PI) * 60;
        } else {
            // Baseline with small variations
            y += Math.sin(phase) * 10;
        }
        
        points.push({ x, y });
    }
    
    // Animation variables
    let animationFrame = 0;
    
    function animate() {
        ctx.clearRect(0, 0, width, height);
        
        // Draw grid
        ctx.strokeStyle = '#e5e7eb';
        ctx.lineWidth = 0.5;
        
        // Vertical lines
        for (let x = 0; x < width; x += 20) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, height);
            ctx.stroke();
        }
        
        // Horizontal lines
        for (let y = 0; y < height; y += 20) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(width, y);
            ctx.stroke();
        }
        
        // Draw ECG waveform
        ctx.strokeStyle = '#2563eb';
        ctx.lineWidth = 2;
        ctx.beginPath();
        
        const offset = (animationFrame * 2) % width;
        
        for (let i = 0; i < points.length - 1; i++) {
            const point1 = points[i];
            const point2 = points[i + 1];
            
            const x1 = (point1.x + offset) % width;
            const x2 = (point2.x + offset) % width;
            
            if (i === 0) {
                ctx.moveTo(x1, point1.y);
            }
            ctx.lineTo(x2, point2.y);
        }
        
        ctx.stroke();
        
        animationFrame++;
        requestAnimationFrame(animate);
    }
    
    animate();
}

/**
 * Initialize scroll animations
 */
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate');
            }
        });
    }, observerOptions);
    
    // Observe elements with scroll-animate class
    document.querySelectorAll('.scroll-animate').forEach(el => {
        observer.observe(el);
    });
}

/**
 * Initialize theme toggle
 */
function initializeThemeToggle() {
    const savedTheme = localStorage.getItem('biosignal-theme') || 'light';
    setTheme(savedTheme);
}

/**
 * Set application theme
 */
function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    window.BiosignalLab.settings.theme = theme;
    localStorage.setItem('biosignal-theme', theme);
}

/**
 * Toggle between light and dark themes
 */
function toggleTheme() {
    const currentTheme = window.BiosignalLab.settings.theme;
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

/**
 * Show loading overlay
 */
function showLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.add('active');
    }
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('active');
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Load application settings
 */
function loadSettings() {
    const savedSettings = localStorage.getItem('biosignal-settings');
    if (savedSettings) {
        try {
            const settings = JSON.parse(savedSettings);
            window.BiosignalLab.settings = { ...window.BiosignalLab.settings, ...settings };
        } catch (error) {
            console.warn('Failed to load saved settings:', error);
        }
    }
}

/**
 * Save application settings
 */
function saveSettings() {
    localStorage.setItem('biosignal-settings', JSON.stringify(window.BiosignalLab.settings));
}

/**
 * Initialize navigation
 */
function initializeNavigation() {
    // Mobile menu toggle
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
        });
    }
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Export functions for global access
window.navigateToModule = navigateToModule;
window.startGuidedTour = startGuidedTour;
window.loadSampleData = loadSampleData;
window.toggleTheme = toggleTheme;
