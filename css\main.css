/* ===================================
   Biosignal Virtual Lab - Main CSS
   ECG HRV Analysis Platform

   Author: Dr. <PERSON>smail
   Institution: SUST - BME
   Year: 2025
   Contact: <EMAIL>
   Phone: +249912867327, +966538076790

   Copyright © 2025 Dr. <PERSON> Esmail
   All rights reserved.
   =================================== */

/* CSS Variables for Design System */
:root {
  /* Enhanced Color Palette */
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #8b5cf6;
  --primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);

  --secondary-color: #06b6d4;
  --secondary-dark: #0891b2;
  --secondary-light: #22d3ee;
  --secondary-gradient: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);

  --accent-color: #f59e0b;
  --accent-dark: #d97706;
  --accent-light: #fbbf24;
  --accent-gradient: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);

  --success-color: #10b981;
  --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --warning-color: #f59e0b;
  --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #ea580c 100%);
  --error-color: #ef4444;
  --error-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --info-color: #3b82f6;
  --info-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  
  /* Enhanced Neutral Colors */
  --gray-50: #fafbfc;
  --gray-100: #f1f3f5;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #adb5bd;
  --gray-500: #6c757d;
  --gray-600: #495057;
  --gray-700: #343a40;
  --gray-800: #212529;
  --gray-900: #0d1117;

  /* Modern Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #fafbfc;
  --bg-tertiary: #f8fafc;
  --bg-quaternary: #f1f5f9;
  --bg-dark: #0f172a;
  --bg-darker: #020617;
  --bg-gradient-primary: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  --bg-gradient-secondary: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
  --bg-gradient-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  
  /* Enhanced Text Colors */
  --text-primary: #0d1117;
  --text-secondary: #495057;
  --text-tertiary: #6c757d;
  --text-quaternary: #adb5bd;
  --text-inverse: #ffffff;
  --text-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);

  /* Modern Border Colors */
  --border-light: #e9ecef;
  --border-medium: #dee2e6;
  --border-dark: #adb5bd;
  --border-accent: rgba(99, 102, 241, 0.2);
  --border-gradient: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);

  /* Enhanced Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.08), 0 1px 2px 0 rgba(0, 0, 0, 0.04);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.04);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.03);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.02);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  --shadow-colored: 0 10px 15px -3px rgba(99, 102, 241, 0.1), 0 4px 6px -2px rgba(99, 102, 241, 0.05);
  --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.15);
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  --font-size-xs: 0.875rem;   /* 14px - increased from 12px */
  --font-size-sm: 1rem;       /* 16px - increased from 14px */
  --font-size-base: 1.125rem; /* 18px - increased from 16px */
  --font-size-lg: 1.25rem;    /* 20px - increased from 18px */
  --font-size-xl: 1.5rem;     /* 24px - increased from 20px */
  --font-size-2xl: 1.875rem;  /* 30px - increased from 24px */
  --font-size-3xl: 2.25rem;   /* 36px - increased from 30px */
  --font-size-4xl: 2.75rem;   /* 44px - increased from 36px */
  --font-size-5xl: 3.5rem;    /* 56px - increased from 48px */
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Z-Index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  
  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Enhanced Dark Theme Variables */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-quaternary: #475569;
  --bg-dark: #020617;
  --bg-darker: #000000;
  --bg-gradient-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --bg-gradient-secondary: linear-gradient(135deg, #1e293b 0%, #334155 100%);

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --text-quaternary: #64748b;
  --text-inverse: #0f172a;

  --border-light: #334155;
  --border-medium: #475569;
  --border-dark: #64748b;
  --border-accent: rgba(99, 102, 241, 0.3);

  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-colored: 0 10px 15px -3px rgba(99, 102, 241, 0.2), 0 4px 6px -2px rgba(99, 102, 241, 0.1);
  --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
}

/* Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 18px; /* Increased from 16px for better readability */
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background: var(--bg-gradient-primary);
  background-attachment: fixed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-5xl); } /* Increased for better prominence */
h2 { font-size: var(--font-size-4xl); } /* Increased for better prominence */
h3 { font-size: var(--font-size-3xl); } /* Increased for better prominence */
h4 { font-size: var(--font-size-2xl); } /* Increased for better prominence */
h5 { font-size: var(--font-size-xl); } /* Increased for better prominence */
h6 { font-size: var(--font-size-lg); } /* Increased for better prominence */

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
  font-size: var(--font-size-lg); /* Added larger font size for better readability */
  line-height: var(--line-height-relaxed);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Layout Utilities */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

@media (min-width: 640px) {
  .container { padding: 0 var(--spacing-lg); }
}

@media (min-width: 1024px) {
  .container { padding: 0 var(--spacing-xl); }
}

/* Responsive Font Scaling */
@media (max-width: 768px) {
  html {
    font-size: 16px; /* Slightly smaller base font on mobile */
  }

  .hero-title {
    font-size: var(--font-size-4xl); /* Scale down hero title on mobile */
  }

  .hero-description {
    font-size: var(--font-size-xl); /* Scale down hero description on mobile */
  }

  h1 { font-size: var(--font-size-4xl); }
  h2 { font-size: var(--font-size-3xl); }
  h3 { font-size: var(--font-size-2xl); }
}

@media (max-width: 480px) {
  html {
    font-size: 15px; /* Even smaller base font on very small screens */
  }

  .hero-title {
    font-size: var(--font-size-3xl); /* Further scale down on very small screens */
  }

  .hero-description {
    font-size: var(--font-size-lg);
  }
}

/* Grid System */
.grid {
  display: grid;
  gap: var(--spacing-md);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
}

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* Spacing Utilities */
.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }
.ml-auto { margin-left: auto; }
.mr-auto { margin-right: auto; }

.p-0 { padding: 0; }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }

.py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }

/* Text Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }

/* Background Utilities */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }

/* Border Utilities */
.border { border: 1px solid var(--border-light); }
.border-t { border-top: 1px solid var(--border-light); }
.border-b { border-bottom: 1px solid var(--border-light); }
.border-l { border-left: 1px solid var(--border-light); }
.border-r { border-right: 1px solid var(--border-light); }

.rounded { border-radius: var(--radius-md); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Display Utilities */
.hidden { display: none; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* Position Utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Overflow Utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* Width and Height Utilities */
.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

/* Responsive Utilities */
@media (max-width: 767px) {
  .hidden-mobile { display: none; }
}

@media (min-width: 768px) {
  .hidden-desktop { display: none; }
}
