"""
Simple demonstration of ECG processing concepts
This script shows the basic concepts without requiring heavy dependencies
"""

import math
import random

def generate_simple_ecg(duration=10, sampling_rate=360, heart_rate=70):
    """
    Generate a simple synthetic ECG signal
    
    Args:
        duration: Duration in seconds
        sampling_rate: Sampling rate in Hz
        heart_rate: Heart rate in BPM
        
    Returns:
        List of ECG samples and time points
    """
    samples = []
    time_points = []
    
    total_samples = int(duration * sampling_rate)
    rr_interval = 60.0 / heart_rate  # Time between R-peaks
    
    for i in range(total_samples):
        t = i / sampling_rate
        time_points.append(t)
        
        # Generate R-peaks at regular intervals with some variability
        time_in_cycle = (t % rr_interval) / rr_interval
        
        # Simple ECG waveform generation
        ecg_value = 0
        
        # R-peak (main spike)
        if 0.3 < time_in_cycle < 0.5:
            peak_pos = (time_in_cycle - 0.4) * 20  # Center around 0.4
            ecg_value = math.exp(-(peak_pos ** 2) / 0.01)  # Gaussian R-peak
        
        # P-wave (small bump before R-peak)
        elif 0.1 < time_in_cycle < 0.25:
            p_pos = (time_in_cycle - 0.175) * 10
            ecg_value = 0.2 * math.exp(-(p_pos ** 2) / 0.1)
        
        # T-wave (after R-peak)
        elif 0.6 < time_in_cycle < 0.85:
            t_pos = (time_in_cycle - 0.725) * 8
            ecg_value = 0.3 * math.exp(-(t_pos ** 2) / 0.2)
        
        # Add some noise
        noise = random.uniform(-0.05, 0.05)
        ecg_value += noise
        
        samples.append(ecg_value)
    
    return samples, time_points

def simple_peak_detection(signal, sampling_rate=360, threshold=0.5):
    """
    Simple R-peak detection using basic thresholding
    
    Args:
        signal: ECG signal samples
        sampling_rate: Sampling rate in Hz
        threshold: Detection threshold
        
    Returns:
        List of R-peak indices
    """
    peaks = []
    refractory_samples = int(0.2 * sampling_rate)  # 200ms refractory period
    last_peak = -refractory_samples
    
    for i in range(1, len(signal) - 1):
        # Check if current sample is a local maximum above threshold
        if (signal[i] > signal[i-1] and 
            signal[i] > signal[i+1] and 
            signal[i] > threshold and
            i - last_peak > refractory_samples):
            
            peaks.append(i)
            last_peak = i
    
    return peaks

def calculate_basic_hrv(r_peaks, sampling_rate=360):
    """
    Calculate basic HRV metrics
    
    Args:
        r_peaks: List of R-peak indices
        sampling_rate: Sampling rate in Hz
        
    Returns:
        Dictionary of HRV metrics
    """
    if len(r_peaks) < 2:
        return {'error': 'Need at least 2 R-peaks'}
    
    # Calculate RR intervals in milliseconds
    rr_intervals = []
    for i in range(1, len(r_peaks)):
        rr_ms = (r_peaks[i] - r_peaks[i-1]) / sampling_rate * 1000
        rr_intervals.append(rr_ms)
    
    if len(rr_intervals) == 0:
        return {'error': 'No RR intervals calculated'}
    
    # Calculate basic statistics
    mean_rr = sum(rr_intervals) / len(rr_intervals)
    mean_hr = 60000 / mean_rr  # Heart rate in BPM
    
    # Calculate standard deviation (SDNN)
    variance = sum((rr - mean_rr) ** 2 for rr in rr_intervals) / len(rr_intervals)
    sdnn = math.sqrt(variance)
    
    # Calculate RMSSD
    if len(rr_intervals) > 1:
        diff_squares = []
        for i in range(1, len(rr_intervals)):
            diff = rr_intervals[i] - rr_intervals[i-1]
            diff_squares.append(diff ** 2)
        
        rmssd = math.sqrt(sum(diff_squares) / len(diff_squares))
    else:
        rmssd = 0
    
    # Calculate pNN50
    if len(rr_intervals) > 1:
        nn50_count = 0
        for i in range(1, len(rr_intervals)):
            if abs(rr_intervals[i] - rr_intervals[i-1]) > 50:
                nn50_count += 1
        pnn50 = (nn50_count / (len(rr_intervals) - 1)) * 100
    else:
        pnn50 = 0
    
    return {
        'n_peaks': len(r_peaks),
        'n_rr_intervals': len(rr_intervals),
        'mean_rr': round(mean_rr, 1),
        'mean_hr': round(mean_hr, 1),
        'sdnn': round(sdnn, 1),
        'rmssd': round(rmssd, 1),
        'pnn50': round(pnn50, 1)
    }

def print_signal_info(signal, time_points, r_peaks, hrv_results):
    """
    Print information about the ECG signal and analysis results
    """
    print("ECG SIGNAL ANALYSIS RESULTS")
    print("=" * 40)
    
    # Signal information
    print(f"Signal duration: {time_points[-1]:.1f} seconds")
    print(f"Total samples: {len(signal)}")
    print(f"Sampling rate: {len(signal) / time_points[-1]:.0f} Hz")
    print(f"Signal range: {min(signal):.3f} to {max(signal):.3f}")
    
    # R-peak detection results
    print(f"\nR-PEAK DETECTION:")
    print(f"Detected R-peaks: {len(r_peaks)}")
    if len(r_peaks) > 0:
        r_peak_times = [time_points[i] for i in r_peaks]
        print(f"R-peak times: {[f'{t:.2f}s' for t in r_peak_times[:5]]}")
        if len(r_peak_times) > 5:
            print(f"... and {len(r_peak_times) - 5} more")
    
    # HRV analysis results
    print(f"\nHRV ANALYSIS:")
    if 'error' in hrv_results:
        print(f"Error: {hrv_results['error']}")
    else:
        print(f"Mean RR interval: {hrv_results['mean_rr']} ms")
        print(f"Mean heart rate: {hrv_results['mean_hr']} bpm")
        print(f"SDNN: {hrv_results['sdnn']} ms")
        print(f"RMSSD: {hrv_results['rmssd']} ms")
        print(f"pNN50: {hrv_results['pnn50']}%")
    
    print("\n" + "=" * 40)

def create_simple_plot(signal, time_points, r_peaks):
    """
    Create a simple text-based plot of the ECG signal
    """
    print("\nSIMPLE ECG VISUALIZATION:")
    print("-" * 60)
    
    # Normalize signal for plotting
    min_val = min(signal)
    max_val = max(signal)
    range_val = max_val - min_val
    
    plot_height = 20
    plot_width = 60
    
    # Create plot grid
    plot_grid = [[' ' for _ in range(plot_width)] for _ in range(plot_height)]
    
    # Plot signal
    for i in range(0, len(signal), len(signal) // plot_width):
        if i >= len(signal):
            break
        
        # Normalize to plot coordinates
        x = int((i / len(signal)) * (plot_width - 1))
        y = int(((signal[i] - min_val) / range_val) * (plot_height - 1))
        y = plot_height - 1 - y  # Flip y-axis
        
        if 0 <= x < plot_width and 0 <= y < plot_height:
            plot_grid[y][x] = '*'
    
    # Mark R-peaks
    for peak_idx in r_peaks:
        x = int((peak_idx / len(signal)) * (plot_width - 1))
        y = int(((signal[peak_idx] - min_val) / range_val) * (plot_height - 1))
        y = plot_height - 1 - y
        
        if 0 <= x < plot_width and 0 <= y < plot_height:
            plot_grid[y][x] = 'R'
    
    # Print plot
    for row in plot_grid:
        print(''.join(row))
    
    print("-" * 60)
    print("Legend: * = ECG signal, R = R-peaks")
    print(f"Time scale: 0 to {time_points[-1]:.1f} seconds")

def demonstrate_error_handling():
    """
    Demonstrate error handling capabilities
    """
    print("\nERROR HANDLING DEMONSTRATION:")
    print("=" * 40)
    
    # Test with empty signal
    print("Testing with empty signal...")
    empty_signal = []
    empty_peaks = simple_peak_detection(empty_signal)
    print(f"Empty signal peaks: {len(empty_peaks)}")
    
    # Test with very short signal
    print("Testing with very short signal...")
    short_signal = [0.1, 0.2, 0.1]
    short_peaks = simple_peak_detection(short_signal)
    print(f"Short signal peaks: {len(short_peaks)}")
    
    # Test HRV with insufficient peaks
    print("Testing HRV with insufficient peaks...")
    insufficient_peaks = [100]
    hrv_result = calculate_basic_hrv(insufficient_peaks)
    print(f"Insufficient peaks result: {hrv_result}")

def main():
    """
    Main demonstration function
    """
    print("BASIC ECG PROCESSING DEMONSTRATION")
    print("=" * 50)
    print("This is a simplified version showing core concepts")
    print("For full functionality, install: numpy, scipy, matplotlib")
    print("=" * 50)
    
    # Generate synthetic ECG
    print("\nGenerating synthetic ECG signal...")
    ecg_signal, time_points = generate_simple_ecg(duration=10, heart_rate=75)
    
    # Detect R-peaks
    print("Detecting R-peaks...")
    r_peaks = simple_peak_detection(ecg_signal, threshold=0.5)
    
    # Calculate HRV
    print("Calculating HRV metrics...")
    hrv_results = calculate_basic_hrv(r_peaks)
    
    # Display results
    print_signal_info(ecg_signal, time_points, r_peaks, hrv_results)
    
    # Create simple visualization
    create_simple_plot(ecg_signal, time_points, r_peaks)
    
    # Demonstrate error handling
    demonstrate_error_handling()
    
    print("\n" + "=" * 50)
    print("DEMONSTRATION COMPLETED")
    print("=" * 50)
    print("\nNext steps:")
    print("1. Install required packages: pip install numpy scipy matplotlib")
    print("2. Run full demonstration: python example_usage.py")
    print("3. Run comprehensive tests: python test_platform.py")

if __name__ == "__main__":
    main()