I'll help you reconstruct this ECG HRV analysis project as a modern web application using HTML, CSS, and JavaScript. Let's create a comprehensive biosignal processing platform that demonstrates all the ECG analysis steps.

````html path=index.html mode=EDIT
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced ECG R-Peak Detection and HRV Analysis Platform</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/plotly.js-dist@2.26.0/plotly.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>🫀 ECG Signal Processing & HRV Analysis Platform</h1>
            <p>Advanced biosignal processing with real-time R-peak detection and comprehensive HRV analysis</p>
        </header>

        <!-- Navigation -->
        <nav class="nav-tabs">
            <button class="tab-btn active" data-tab="upload">📁 Data Input</button>
            <button class="tab-btn" data-tab="processing">⚙️ Signal Processing</button>
            <button class="tab-btn" data-tab="detection">🔍 R-Peak Detection</button>
            <button class="tab-btn" data-tab="hrv">📊 HRV Analysis</button>
            <button class="tab-btn" data-tab="results">📈 Results</button>
        </nav>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Data Input Tab -->
            <div id="upload" class="tab-pane active">
                <div class="card">
                    <h2>📁 ECG Data Input</h2>
                    <div class="input-section">
                        <div class="input-group">
                            <label>Choose Data Source:</label>
                            <div class="radio-group">
                                <input type="radio" id="synthetic" name="dataSource" value="synthetic" checked>
                                <label for="synthetic">Generate Synthetic ECG</label>
                                <input type="radio" id="upload-file" name="dataSource" value="upload">
                                <label for="upload-file">Upload ECG File</label>
                            </div>
                        </div>

                        <div id="synthetic-controls" class="controls-group">
                            <div class="parameter-grid">
                                <div class="param-item">
                                    <label for="duration">Duration (seconds):</label>
                                    <input type="range" id="duration" min="10" max="300" value="60">
                                    <span id="duration-value">60</span>
                                </div>
                                <div class="param-item">
                                    <label for="heart-rate">Heart Rate (BPM):</label>
                                    <input type="range" id="heart-rate" min="40" max="180" value="72">
                                    <span id="heart-rate-value">72</span>
                                </div>
                                <div class="param-item">
                                    <label for="noise-level">Noise Level:</label>
                                    <input type="range" id="noise-level" min="0" max="50" value="10">
                                    <span id="noise-level-value">10%</span>
                                </div>
                                <div class="param-item">
                                    <label for="sampling-rate">Sampling Rate (Hz):</label>
                                    <select id="sampling-rate">
                                        <option value="250">250 Hz</option>
                                        <option value="360" selected>360 Hz</option>
                                        <option value="500">500 Hz</option>
                                        <option value="1000">1000 Hz</option>
                                    </select>
                                </div>
                            </div>
                            <button id="generate-ecg" class="btn btn-primary">🔄 Generate ECG Signal</button>
                        </div>

                        <div id="upload-controls" class="controls-group" style="display: none;">
                            <input type="file" id="file-input" accept=".csv,.txt,.json" class="file-input">
                            <div class="file-info">
                                <p>Supported formats: CSV, TXT, JSON</p>
                                <p>Expected format: Single column of ECG values or time,value pairs</p>
                            </div>
                        </div>
                    </div>

                    <div id="raw-signal-container" class="chart-container">
                        <canvas id="raw-signal-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Signal Processing Tab -->
            <div id="processing" class="tab-pane">
                <div class="card">
                    <h2>⚙️ Signal Processing Pipeline</h2>
                    <div class="processing-steps">
                        <div class="step-card">
                            <h3>1. Baseline Removal</h3>
                            <p>Remove low-frequency baseline wandering using high-pass filtering</p>
                            <div class="step-controls">
                                <label>High-pass cutoff (Hz):</label>
                                <input type="range" id="highpass-cutoff" min="0.1" max="2" step="0.1" value="0.5">
                                <span id="highpass-value">0.5</span>
                            </div>
                        </div>

                        <div class="step-card">
                            <h3>2. Noise Filtering</h3>
                            <p>Apply bandpass filter to enhance QRS complexes (5-15 Hz)</p>
                            <div class="step-controls">
                                <label>Low cutoff (Hz):</label>
                                <input type="range" id="lowpass-low" min="3" max="8" value="5">
                                <span id="lowpass-low-value">5</span>
                                <label>High cutoff (Hz):</label>
                                <input type="range" id="lowpass-high" min="10" max="25" value="15">
                                <span id="lowpass-high-value">15</span>
                            </div>
                        </div>

                        <div class="step-card">
                            <h3>3. Signal Enhancement</h3>
                            <p>Differentiation and squaring to emphasize QRS complexes</p>
                            <button id="apply-processing" class="btn btn-secondary">🔄 Apply Processing</button>
                        </div>
                    </div>

                    <div id="processing-charts" class="charts-grid">
                        <div class="chart-item">
                            <h4>Original Signal</h4>
                            <canvas id="original-chart"></canvas>
                        </div>
                        <div class="chart-item">
                            <h4>Filtered Signal</h4>
                            <canvas id="filtered-chart"></canvas>
                        </div>
                        <div class="chart-item">
                            <h4>Differentiated Signal</h4>
                            <canvas id="diff-chart"></canvas>
                        </div>
                        <div class="chart-item">
                            <h4>Squared & Integrated</h4>
                            <canvas id="integrated-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- R-Peak Detection Tab -->
            <div id="detection" class="tab-pane">
                <div class="card">
                    <h2>🔍 R-Peak Detection Algorithm</h2>
                    <div class="detection-controls">
                        <div class="algorithm-selection">
                            <h3>Detection Algorithm</h3>
                            <select id="detection-algorithm">
                                <option value="pan-tompkins">Pan-Tompkins Algorithm</option>
                                <option value="adaptive-threshold">Adaptive Threshold</option>
                                <option value="wavelet">Wavelet-based</option>
                            </select>
                        </div>

                        <div class="threshold-controls">
                            <h3>Threshold Parameters</h3>
                            <div class="param-grid">
                                <div class="param-item">
                                    <label>Detection Threshold:</label>
                                    <input type="range" id="detection-threshold" min="0.1" max="1" step="0.05" value="0.3">
                                    <span id="threshold-value">0.3</span>
                                </div>
                                <div class="param-item">
                                    <label>Refractory Period (ms):</label>
                                    <input type="range" id="refractory-period" min="150" max="400" value="200">
                                    <span id="refractory-value">200</span>
                                </div>
                            </div>
                        </div>

                        <button id="detect-peaks" class="btn btn-primary">🔍 Detect R-Peaks</button>
                    </div>

                    <div id="detection-results" class="results-section">
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <h4>Detected Peaks</h4>
                                <span id="peak-count" class="metric-value">0</span>
                            </div>
                            <div class="metric-card">
                                <h4>Average HR</h4>
                                <span id="avg-hr" class="metric-value">0 BPM</span>
                            </div>
                            <div class="metric-card">
                                <h4>Detection Quality</h4>
                                <span id="detection-quality" class="metric-value">0%</span>
                            </div>
                        </div>

                        <div id="detection-chart-container" class="chart-container">
                            <canvas id="detection-chart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- HRV Analysis Tab -->
            <div id="hrv" class="tab-pane">
                <div class="card">
                    <h2>📊 Heart Rate Variability Analysis</h2>
                    <div class="hrv-controls">
                        <button id="analyze-hrv" class="btn btn-primary">📈 Analyze HRV</button>
                        <div class="hrv-options">
                            <label>
                                <input type="checkbox" id="time-domain" checked> Time Domain Analysis
                            </label>
                            <label>
                                <input type="checkbox" id="freq-domain" checked> Frequency Domain Analysis
                            </label>
                            <label>
                                <input type="checkbox" id="nonlinear" checked> Nonlinear Analysis
                            </label>
                        </div>
                    </div>

                    <div id="hrv-results" class="hrv-results">
                        <!-- Time Domain Features -->
                        <div class="feature-section">
                            <h3>⏱️ Time Domain Features</h3>
                            <div class="features-grid">
                                <div class="feature-card">
                                    <h4>SDNN</h4>
                                    <span id="sdnn-value" class="feature-value">-- ms</span>
                                    <p>Standard deviation of NN intervals</p>
                                </div>
                                <div class="feature-card">
                                    <h4>RMSSD</h4>
                                    <span id="rmssd-value" class="feature-value">-- ms</span>
                                    <p>Root mean square of successive differences</p>
                                </div>
                                <div class="feature-card">
                                    <h4>pNN50</h4>
                                    <span id="pnn50-value" class="feature-value">-- %</span>
                                    <p>Percentage of successive NN intervals > 50ms</p>
                                </div>
                                <div class="feature-card">
                                    <h4>Mean RR</h4>
                                    <span id="mean-rr-value" class="feature-value">-- ms</span>
                                    <p>Average RR interval</p>
                                </div>
                            </div>
                        </div>

                        <!-- Frequency Domain Features -->
                        <div class="feature-section">
                            <h3>🌊 Frequency Domain Features</h3>
                            <div class="features-grid">
                                <div class="feature-card">
                                    <h4>VLF Power</h4>
                                    <span id="vlf-power-value" class="feature-value">-- ms²</span>
                                    <p>Very Low Frequency (0.003-0.04 Hz)</p>
                                </div>
                                <div class="feature-card">
                                    <h4>LF Power</h4>
                                    <span id="lf-power-value" class="feature-value">-- ms²</span>
                                    <p>Low Frequency (0.04-0.15 Hz)</p>
                                </div>
                                <div class="feature-card">
                                    <h4>HF Power</h4>
                                    <span id="hf-power-value" class="feature-value">-- ms²</span>
                                    <p>High Frequency (0.15-0.4 Hz)</p>
                                </div>
                                <div class="feature-card">
                                    <h4>LF/HF Ratio</h4>
                                    <span id="lf-hf-ratio-value" class="feature-value">--</span>
                                    <p>Sympathovagal balance</p>
                                </div>
                            </div>
                        </div>

                        <!-- HRV Visualizations -->
                        <div class="hrv-charts">
                            <div class="chart-row">
                                <div class="chart-item">
                                    <h4>RR Interval Tachogram</h4>
                                    <div id="tachogram-chart"></div>
                                </div>
                                <div class="chart-item">
                                    <h4>RR Interval Histogram</h4>
                                    <div id="histogram-chart"></div>
                                </div>
                            </div>
                            <div class="chart-row">
                                <div class="chart-item">
                                    <h4>Poincaré Plot</h4>
                                    <div id="poincare-chart"></div>
                                </div>
                                <div class="chart-item">
                                    <h4>Power Spectral Density</h4>
                                    <div id="psd-chart"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results Tab -->
            <div id="results" class="tab-pane">
                <div class="card">
                    <h2>📈 Analysis Results & Export</h2>
                    <div class="results-summary">
                        <div class="summary-section">
                            <h3>📋 Analysis Summary</h3>
                            <div id="analysis-summary" class="summary-content">
                                <p>Complete the analysis to see results summary...</p>
                            </div>
                        </div>

                        <div class="export-section">
                            <h3>💾 Export Results</h3>
                            <div class="export-buttons">
                                <button id="export-json" class="btn btn-secondary">📄 Export JSON</button>
                                <button id="export-csv" class="btn btn-secondary">📊 Export CSV</button>
                                <button id="export-report" class="btn btn-primary">📑 Generate Report</button>
                            </div>
                        </div>

                        <div class="clinical-interpretation">
                            <h3>🏥 Clinical Interpretation</h3>
                            <div id="clinical-notes" class="interpretation-content">
                                <p>Analysis results will be interpreted here...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processing Status -->
        <div id="status-bar" class="status-bar">
            <span id="status-text">Ready</span>
            <div id="progress-bar" class="progress-bar">
                <div id="progress-fill" class="progress-fill"></div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/ecg-generator.js"></script>
    <script src="js/signal-processing.js"></script>
    <script src="js/peak-detection.js"></script>
    <script src="js/hrv-analysis.js"></script>
    <script src="js/visualization.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
````
