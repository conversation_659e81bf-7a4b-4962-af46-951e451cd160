"""
Setup script for ECG Processing Platform
"""

import sys
import subprocess
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ Failed to install {package}")
        return False

def check_package(package):
    """Check if a package is installed"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """Main setup function"""
    print("ECG Processing Platform Setup")
    print("=" * 40)
    
    # List of required packages
    required_packages = [
        'numpy',
        'scipy',
        'matplotlib',
        'scikit-learn',
        'pandas'
    ]
    
    optional_packages = [
        'h5py',
        'wfdb',
        'biosppy'
    ]
    
    print("Checking required packages...")
    
    missing_packages = []
    for package in required_packages:
        if check_package(package):
            print(f"✓ {package} is installed")
        else:
            print(f"✗ {package} is missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\nInstalling {len(missing_packages)} missing packages...")
        for package in missing_packages:
            install_package(package)
    
    print("\nChecking optional packages...")
    for package in optional_packages:
        if check_package(package):
            print(f"✓ {package} is installed")
        else:
            print(f"- {package} is optional (for extended functionality)")
    
    print("\nSetup completed!")
    print("\nYou can now run:")
    print("  python simple_demo.py        # Basic demonstration")
    print("  python example_usage.py      # Full demonstration")
    print("  python test_platform.py      # Comprehensive tests")

if __name__ == "__main__":
    main()