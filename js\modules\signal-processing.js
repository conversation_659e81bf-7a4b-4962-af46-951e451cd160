/**
 * Signal Processing Module - Biosignal Virtual Lab
 * Handles ECG signal loading, preprocessing, and visualization
 */

class SignalProcessor {
    constructor() {
        this.rawSignal = null;
        this.filteredSignal = null;
        this.samplingRate = 360;
        this.currentView = 'filtered';
        this.charts = {};
        
        this.initializeModule();
    }
    
    /**
     * Initialize the signal processing module
     */
    initializeModule() {
        console.log('Initializing Signal Processing Module...');
        
        this.bindEvents();
        this.initializeCharts();
        this.setupFileUpload();
        this.setupTabs();
        this.setupControlSections();
        
        // Load any existing data
        this.loadExistingData();
    }
    
    /**
     * Bind event listeners
     */
    bindEvents() {
        // File input
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }
        
        // Upload area drag and drop
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
            uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
        }
        
        // Sample dataset cards
        document.querySelectorAll('.dataset-card').forEach(card => {
            card.addEventListener('click', (e) => this.loadSampleDataset(e));
        });
        
        // Parameter changes
        document.getElementById('samplingRate')?.addEventListener('change', (e) => {
            this.samplingRate = parseFloat(e.target.value);
        });
    }
    
    /**
     * Setup file upload functionality
     */
    setupFileUpload() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        
        if (uploadArea && fileInput) {
            uploadArea.addEventListener('click', () => fileInput.click());
        }
    }
    
    /**
     * Setup tab functionality
     */
    setupTabs() {
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.getAttribute('data-tab');
                this.switchTab(tabName);
            });
        });
    }
    
    /**
     * Setup collapsible control sections
     */
    setupControlSections() {
        document.querySelectorAll('.control-section h3').forEach(header => {
            header.addEventListener('click', (e) => {
                const section = e.target.closest('.control-section');
                this.toggleControlSection(section);
            });
        });
        
        // Activate first section by default
        const firstSection = document.querySelector('.control-section');
        if (firstSection) {
            firstSection.classList.add('active');
        }
    }
    
    /**
     * Switch between tabs
     */
    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
    }
    
    /**
     * Toggle control section
     */
    toggleControlSection(section) {
        const isActive = section.classList.contains('active');
        
        // Close all sections
        document.querySelectorAll('.control-section').forEach(s => {
            s.classList.remove('active');
        });
        
        // Open clicked section if it wasn't active
        if (!isActive) {
            section.classList.add('active');
        }
    }
    
    /**
     * Handle file upload
     */
    async handleFileUpload(event) {
        const files = event.target.files;
        if (files.length > 0) {
            await this.processUploadedFile(files[0]);
        }
    }
    
    /**
     * Handle drag over
     */
    handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');
    }
    
    /**
     * Handle drag leave
     */
    handleDragLeave(event) {
        event.currentTarget.classList.remove('dragover');
    }
    
    /**
     * Handle file drop
     */
    async handleFileDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            await this.processUploadedFile(files[0]);
        }
    }
    
    /**
     * Process uploaded file
     */
    async processUploadedFile(file) {
        try {
            this.showLoading('Loading file...');
            
            const fileExtension = file.name.split('.').pop().toLowerCase();
            let signalData;
            
            switch (fileExtension) {
                case 'csv':
                case 'txt':
                    signalData = await this.parseTextFile(file);
                    break;
                case 'json':
                    signalData = await this.parseJSONFile(file);
                    break;
                default:
                    throw new Error(`Unsupported file format: ${fileExtension}`);
            }
            
            this.loadSignalData(signalData);
            this.showNotification(`File "${file.name}" loaded successfully!`, 'success');
            
        } catch (error) {
            console.error('Error processing file:', error);
            this.showNotification(`Error loading file: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * Parse text file (CSV/TXT)
     */
    async parseTextFile(file) {
        const text = await FileUtils.readFileAsText(file);
        const data = FileUtils.parseCSV(text);
        
        const columnIndex = parseInt(document.getElementById('dataColumn').value);
        const skipRows = parseInt(document.getElementById('skipRows').value);
        
        // Extract signal data
        const signal = [];
        for (let i = skipRows; i < data.length; i++) {
            const value = parseFloat(data[i][columnIndex]);
            if (!isNaN(value)) {
                signal.push(value);
            }
        }
        
        if (signal.length === 0) {
            throw new Error('No valid signal data found in file');
        }
        
        return signal;
    }
    
    /**
     * Parse JSON file
     */
    async parseJSONFile(file) {
        const text = await FileUtils.readFileAsText(file);
        const data = JSON.parse(text);
        
        // Assume JSON has signal array
        if (Array.isArray(data)) {
            return data;
        } else if (data.signal && Array.isArray(data.signal)) {
            if (data.samplingRate) {
                this.samplingRate = data.samplingRate;
                document.getElementById('samplingRate').value = this.samplingRate;
            }
            return data.signal;
        } else {
            throw new Error('Invalid JSON format');
        }
    }
    
    /**
     * Load sample dataset
     */
    async loadSampleDataset(event) {
        const datasetName = event.currentTarget.getAttribute('data-dataset');
        
        try {
            this.showLoading('Loading sample dataset...');
            
            // Generate sample data based on dataset type
            let signalData;
            switch (datasetName) {
                case 'mit-bih-100':
                    signalData = this.generateMITBIHSample(100, 360, 'normal');
                    this.samplingRate = 360;
                    break;
                case 'mit-bih-101':
                    signalData = this.generateMITBIHSample(101, 360, 'arrhythmia');
                    this.samplingRate = 360;
                    break;
                case 'nsr-001':
                    signalData = this.generateNSRSample(128);
                    this.samplingRate = 128;
                    break;
                default:
                    throw new Error('Unknown dataset');
            }
            
            document.getElementById('samplingRate').value = this.samplingRate;
            this.loadSignalData(signalData);
            this.showNotification(`Sample dataset "${datasetName}" loaded successfully!`, 'success');
            
        } catch (error) {
            console.error('Error loading sample dataset:', error);
            this.showNotification(`Error loading dataset: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * Generate MIT-BIH sample data
     */
    generateMITBIHSample(recordNumber, samplingRate, type) {
        const duration = 60; // 60 seconds
        const numSamples = duration * samplingRate;
        const signal = new Array(numSamples);
        
        let heartRate = 70; // Default heart rate
        if (type === 'arrhythmia') {
            heartRate = 65; // Slightly slower for arrhythmia example
        }
        
        const rrInterval = 60 / heartRate;
        
        for (let i = 0; i < numSamples; i++) {
            const t = i / samplingRate;
            const beatPhase = (t % rrInterval) / rrInterval;
            
            let amplitude = 0;
            
            // P wave
            if (beatPhase >= 0.1 && beatPhase <= 0.2) {
                amplitude += 0.15 * Math.sin(Math.PI * (beatPhase - 0.1) / 0.1);
            }
            
            // QRS complex
            if (beatPhase >= 0.35 && beatPhase <= 0.45) {
                const qrsPhase = (beatPhase - 0.35) / 0.1;
                if (qrsPhase < 0.3) {
                    amplitude -= 0.2 * Math.sin(Math.PI * qrsPhase / 0.3);
                } else if (qrsPhase < 0.7) {
                    amplitude += 1.0 * Math.sin(Math.PI * (qrsPhase - 0.3) / 0.4);
                } else {
                    amplitude -= 0.3 * Math.sin(Math.PI * (qrsPhase - 0.7) / 0.3);
                }
            }
            
            // T wave
            if (beatPhase >= 0.6 && beatPhase <= 0.8) {
                amplitude += 0.25 * Math.sin(Math.PI * (beatPhase - 0.6) / 0.2);
            }
            
            // Add arrhythmia variations
            if (type === 'arrhythmia' && Math.random() < 0.1) {
                amplitude *= 1.5; // Premature beat
            }
            
            // Add noise
            amplitude += (Math.random() - 0.5) * 0.05;
            
            signal[i] = amplitude;
        }
        
        return signal;
    }
    
    /**
     * Generate NSR sample data
     */
    generateNSRSample(samplingRate) {
        return this.generateMITBIHSample(1, samplingRate, 'normal');
    }
    
    /**
     * Load signal data into the application
     */
    loadSignalData(signalData) {
        // Validate signal
        const validation = ValidationUtils.validateECGSignal(signalData);
        if (!validation.valid) {
            throw new Error(`Invalid signal data: ${validation.errors.join(', ')}`);
        }
        
        // Store signal data
        this.rawSignal = [...signalData];
        this.filteredSignal = [...signalData];
        
        // Update global state
        window.BiosignalLab.ecgData = {
            signal: this.rawSignal,
            samplingRate: this.samplingRate,
            duration: this.rawSignal.length / this.samplingRate,
            timestamp: new Date().toISOString()
        };
        
        // Update UI
        this.updateSignalInfo();
        this.updateCharts();
        this.enableProceedButton();
        
        // Move to preprocessing section
        this.activateProcessingStep('preprocess');
    }
    
    /**
     * Update signal information display
     */
    updateSignalInfo() {
        const duration = this.rawSignal.length / this.samplingRate;
        
        document.getElementById('signalDuration').textContent = FormatUtils.formatDuration(duration);
        document.getElementById('sampleCount').textContent = this.rawSignal.length.toLocaleString();
        document.getElementById('displaySamplingRate').textContent = `${this.samplingRate} Hz`;
    }
    
    /**
     * Initialize charts
     */
    initializeCharts() {
        this.initializeECGChart();
        this.initializeSpectrumChart();
    }
    
    /**
     * Initialize ECG signal chart
     */
    initializeECGChart() {
        const canvas = document.getElementById('ecgChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        this.charts.ecg = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'ECG Signal',
                    data: [],
                    borderColor: '#2563eb',
                    backgroundColor: 'transparent',
                    borderWidth: 1,
                    pointRadius: 0,
                    tension: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time (s)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Amplitude (mV)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    /**
     * Initialize spectrum chart
     */
    initializeSpectrumChart() {
        const canvas = document.getElementById('spectrumChart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        this.charts.spectrum = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Power Spectrum',
                    data: [],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 2,
                    pointRadius: 0,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Frequency (Hz)'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Power (dB)'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    /**
     * Update charts with current signal data
     */
    updateCharts() {
        if (!this.rawSignal) return;
        
        this.updateECGChart();
        this.updateSpectrumChart();
    }
    
    /**
     * Update ECG chart
     */
    updateECGChart() {
        const signal = this.currentView === 'raw' ? this.rawSignal : this.filteredSignal;
        const maxPoints = 2000; // Limit points for performance
        const step = Math.max(1, Math.floor(signal.length / maxPoints));
        
        const timeLabels = [];
        const signalData = [];
        
        for (let i = 0; i < signal.length; i += step) {
            timeLabels.push((i / this.samplingRate).toFixed(2));
            signalData.push(signal[i]);
        }
        
        this.charts.ecg.data.labels = timeLabels;
        this.charts.ecg.data.datasets[0].data = signalData;
        this.charts.ecg.update('none');
    }
    
    /**
     * Update spectrum chart
     */
    updateSpectrumChart() {
        if (!this.filteredSignal) return;
        
        // Calculate power spectral density
        const psdResult = SignalUtils.powerSpectralDensity(this.filteredSignal, this.samplingRate);
        
        // Take only first half (positive frequencies)
        const halfLength = Math.floor(psdResult.frequencies.length / 2);
        const frequencies = psdResult.frequencies.slice(0, halfLength);
        const powerDB = psdResult.psd.slice(0, halfLength).map(p => 10 * Math.log10(p + 1e-10));
        
        this.charts.spectrum.data.labels = frequencies.map(f => f.toFixed(1));
        this.charts.spectrum.data.datasets[0].data = powerDB;
        this.charts.spectrum.update('none');
    }
    
    /**
     * Load existing data if available
     */
    loadExistingData() {
        const existingData = window.BiosignalLab.ecgData;
        if (existingData && existingData.signal) {
            this.rawSignal = [...existingData.signal];
            this.filteredSignal = [...existingData.signal];
            this.samplingRate = existingData.samplingRate || 360;
            
            document.getElementById('samplingRate').value = this.samplingRate;
            this.updateSignalInfo();
            this.updateCharts();
            this.enableProceedButton();
        }
    }
    
    /**
     * Activate processing step
     */
    activateProcessingStep(stepName) {
        document.querySelectorAll('.pipeline-step').forEach(step => {
            step.classList.remove('active');
        });
        
        const targetStep = document.querySelector(`[data-step="${stepName}"]`);
        if (targetStep) {
            targetStep.classList.add('active');
        }
    }
    
    /**
     * Enable proceed button
     */
    enableProceedButton() {
        const proceedBtn = document.getElementById('proceedBtn');
        if (proceedBtn) {
            proceedBtn.disabled = false;
        }
    }
    
    /**
     * Show loading overlay
     */
    showLoading(message = 'Processing...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.querySelector('p').textContent = message;
            overlay.classList.add('active');
        }
    }
    
    /**
     * Hide loading overlay
     */
    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }
    
    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Global functions for HTML onclick handlers
window.generateSyntheticECG = function() {
    const duration = parseInt(document.getElementById('syntheticDuration').value);
    const heartRate = parseInt(document.getElementById('syntheticHeartRate').value);
    const noiseLevel = parseFloat(document.getElementById('noiseLevel').value);
    const arrhythmiaType = document.getElementById('arrhythmiaType').value;
    
    // Generate synthetic ECG with specified parameters
    const signalData = window.signalProcessor.generateMITBIHSample(1, 360, arrhythmiaType);
    window.signalProcessor.loadSignalData(signalData);
};

window.applyPreprocessing = function() {
    console.log('Applying preprocessing...');
    // Implementation for preprocessing
};

window.resetPreprocessing = function() {
    console.log('Resetting preprocessing...');
    // Implementation for reset
};

window.assessQuality = function() {
    console.log('Assessing signal quality...');
    // Implementation for quality assessment
};

window.exportData = function() {
    console.log('Exporting data...');
    // Implementation for data export
};

window.exportReport = function() {
    console.log('Exporting report...');
    // Implementation for report export
};

window.toggleSignalView = function(view) {
    window.signalProcessor.currentView = view;
    window.signalProcessor.updateECGChart();
    
    // Update button states
    document.querySelectorAll('.chart-controls .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-secondary');
    });
    event.target.classList.remove('btn-secondary');
    event.target.classList.add('btn-primary');
};

window.proceedToRPeakDetection = function() {
    if (window.BiosignalLab.ecgData) {
        window.location.href = 'r-peak-detection.html';
    } else {
        window.signalProcessor.showNotification('Please load ECG data first', 'warning');
    }
};

// Initialize signal processor when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.signalProcessor = new SignalProcessor();
});
