/**
 * Navigation System - Biosignal Virtual Lab
 * Handles client-side routing and navigation between modules
 */

class NavigationManager {
    constructor() {
        this.currentRoute = null;
        this.routes = new Map();
        this.history = [];
        this.breadcrumbs = [];
        
        this.initializeRoutes();
        this.bindEvents();
    }
    
    /**
     * Initialize application routes
     */
    initializeRoutes() {
        this.routes.set('home', {
            path: 'index.html',
            title: 'Home',
            description: 'Biosignal Virtual Lab - ECG HRV Analysis Platform',
            module: null
        });
        
        this.routes.set('signal-processing', {
            path: 'signal-processing.html',
            title: 'Signal Processing',
            description: 'ECG signal preprocessing and filtering',
            module: 'signal-processing',
            dependencies: []
        });
        
        this.routes.set('r-peak-detection', {
            path: 'r-peak-detection.html',
            title: 'R-Peak Detection',
            description: 'Pan-<PERSON><PERSON>kins algorithm for R-peak detection',
            module: 'r-peak-detection',
            dependencies: ['signal-processing']
        });
        
        this.routes.set('hrv-analysis', {
            path: 'hrv-analysis.html',
            title: 'HRV Analysis',
            description: 'Heart rate variability analysis',
            module: 'hrv-analysis',
            dependencies: ['signal-processing', 'r-peak-detection']
        });
        
        this.routes.set('data-management', {
            path: 'data-management.html',
            title: 'Data Management',
            description: 'Data import/export and format conversion',
            module: 'data-management',
            dependencies: []
        });
        
        this.routes.set('visualization', {
            path: 'visualization.html',
            title: 'Visualization',
            description: 'Advanced signal visualization and plotting',
            module: 'visualization',
            dependencies: ['signal-processing']
        });
        
        this.routes.set('results', {
            path: 'results.html',
            title: 'Results & Reports',
            description: 'Analysis results and report generation',
            module: 'results',
            dependencies: ['hrv-analysis']
        });
        
        this.routes.set('documentation', {
            path: 'documentation.html',
            title: 'Documentation',
            description: 'Platform documentation and help',
            module: 'documentation',
            dependencies: []
        });
    }
    
    /**
     * Bind navigation events
     */
    bindEvents() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (event) => {
            if (event.state && event.state.route) {
                this.navigateToRoute(event.state.route, false);
            }
        });
        
        // Handle navigation links
        document.addEventListener('click', (event) => {
            const link = event.target.closest('[data-navigate]');
            if (link) {
                event.preventDefault();
                const route = link.getAttribute('data-navigate');
                this.navigateTo(route);
            }
        });
    }
    
    /**
     * Navigate to a specific route
     */
    navigateTo(routeName, addToHistory = true) {
        const route = this.routes.get(routeName);
        if (!route) {
            console.error(`Route '${routeName}' not found`);
            return false;
        }
        
        // Check dependencies
        if (!this.checkDependencies(route)) {
            this.showDependencyWarning(route);
            return false;
        }
        
        // Add to history
        if (addToHistory) {
            this.addToHistory(routeName);
        }
        
        // Update browser history
        if (addToHistory && routeName !== 'home') {
            history.pushState(
                { route: routeName },
                route.title,
                route.path
            );
        }
        
        // Navigate to route
        return this.navigateToRoute(routeName);
    }
    
    /**
     * Navigate to route implementation
     */
    navigateToRoute(routeName, updateHistory = true) {
        const route = this.routes.get(routeName);
        if (!route) return false;
        
        // Show loading
        this.showNavigationLoading();
        
        // Update current route
        this.currentRoute = routeName;
        
        // Update page title
        document.title = `${route.title} - Biosignal Virtual Lab`;
        
        // Update breadcrumbs
        this.updateBreadcrumbs(routeName);
        
        // Update navigation state
        this.updateNavigationState(routeName);
        
        // Simulate navigation delay for smooth transition
        setTimeout(() => {
            if (routeName === 'home') {
                // Already on home page, just scroll to top
                window.scrollTo({ top: 0, behavior: 'smooth' });
                this.hideNavigationLoading();
            } else {
                // Navigate to different page
                window.location.href = route.path;
            }
        }, 300);
        
        return true;
    }
    
    /**
     * Check route dependencies
     */
    checkDependencies(route) {
        if (!route.dependencies || route.dependencies.length === 0) {
            return true;
        }
        
        // Check if required data is available
        const lab = window.BiosignalLab;
        
        for (const dependency of route.dependencies) {
            switch (dependency) {
                case 'signal-processing':
                    if (!lab.ecgData) return false;
                    break;
                case 'r-peak-detection':
                    if (!lab.rPeaks) return false;
                    break;
                case 'hrv-analysis':
                    if (!lab.hrvResults) return false;
                    break;
            }
        }
        
        return true;
    }
    
    /**
     * Show dependency warning
     */
    showDependencyWarning(route) {
        const missingDeps = route.dependencies.filter(dep => {
            const lab = window.BiosignalLab;
            switch (dep) {
                case 'signal-processing': return !lab.ecgData;
                case 'r-peak-detection': return !lab.rPeaks;
                case 'hrv-analysis': return !lab.hrvResults;
                default: return false;
            }
        });
        
        const depNames = missingDeps.map(dep => {
            switch (dep) {
                case 'signal-processing': return 'Signal Processing';
                case 'r-peak-detection': return 'R-Peak Detection';
                case 'hrv-analysis': return 'HRV Analysis';
                default: return dep;
            }
        });
        
        const message = `Please complete the following steps first: ${depNames.join(', ')}`;
        this.showNotification(message, 'warning');
        
        // Suggest navigation to first missing dependency
        if (missingDeps.length > 0) {
            setTimeout(() => {
                if (confirm(`Would you like to go to ${depNames[0]} first?`)) {
                    this.navigateTo(missingDeps[0]);
                }
            }, 1000);
        }
    }
    
    /**
     * Add route to navigation history
     */
    addToHistory(routeName) {
        this.history.push({
            route: routeName,
            timestamp: new Date(),
            data: { ...window.BiosignalLab }
        });
        
        // Limit history size
        if (this.history.length > 50) {
            this.history = this.history.slice(-50);
        }
    }
    
    /**
     * Update breadcrumbs
     */
    updateBreadcrumbs(routeName) {
        const route = this.routes.get(routeName);
        if (!route) return;
        
        this.breadcrumbs = ['home'];
        
        // Add dependencies to breadcrumbs
        if (route.dependencies) {
            this.breadcrumbs.push(...route.dependencies);
        }
        
        // Add current route
        if (routeName !== 'home') {
            this.breadcrumbs.push(routeName);
        }
        
        // Update breadcrumb UI
        this.renderBreadcrumbs();
    }
    
    /**
     * Render breadcrumbs in UI
     */
    renderBreadcrumbs() {
        const breadcrumbContainer = document.querySelector('.breadcrumbs');
        if (!breadcrumbContainer) return;
        
        const breadcrumbHTML = this.breadcrumbs.map((routeName, index) => {
            const route = this.routes.get(routeName);
            const isLast = index === this.breadcrumbs.length - 1;
            const isActive = routeName === this.currentRoute;
            
            return `
                <span class="breadcrumb-item ${isActive ? 'active' : ''}">
                    ${isLast ? route.title : `<a href="#" data-navigate="${routeName}">${route.title}</a>`}
                </span>
                ${!isLast ? '<span class="breadcrumb-separator">›</span>' : ''}
            `;
        }).join('');
        
        breadcrumbContainer.innerHTML = breadcrumbHTML;
    }
    
    /**
     * Update navigation state
     */
    updateNavigationState(routeName) {
        // Update active navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        
        const activeLink = document.querySelector(`[data-navigate="${routeName}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
        
        // Update module status indicators
        this.updateModuleStatus();
    }
    
    /**
     * Update module status indicators
     */
    updateModuleStatus() {
        const lab = window.BiosignalLab;
        
        const statusMap = {
            'signal-processing': lab.ecgData ? 'completed' : 'available',
            'r-peak-detection': lab.rPeaks ? 'completed' : lab.ecgData ? 'available' : 'locked',
            'hrv-analysis': lab.hrvResults ? 'completed' : lab.rPeaks ? 'available' : 'locked',
            'visualization': lab.ecgData ? 'available' : 'locked',
            'results': lab.hrvResults ? 'available' : 'locked'
        };
        
        Object.entries(statusMap).forEach(([module, status]) => {
            const statusElement = document.querySelector(`[data-module="${module}"] .status-badge`);
            if (statusElement) {
                statusElement.className = `status-badge ${status}`;
                statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            }
        });
    }
    
    /**
     * Show navigation loading
     */
    showNavigationLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('active');
        }
    }
    
    /**
     * Hide navigation loading
     */
    hideNavigationLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('active');
        }
    }
    
    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
    
    /**
     * Get current route
     */
    getCurrentRoute() {
        return this.currentRoute;
    }
    
    /**
     * Get route information
     */
    getRoute(routeName) {
        return this.routes.get(routeName);
    }
    
    /**
     * Get navigation history
     */
    getHistory() {
        return [...this.history];
    }
    
    /**
     * Clear navigation history
     */
    clearHistory() {
        this.history = [];
    }
}

// Initialize navigation manager
window.NavigationManager = new NavigationManager();

// Export for global access
window.navigateToModule = (moduleName) => {
    return window.NavigationManager.navigateTo(moduleName);
};
