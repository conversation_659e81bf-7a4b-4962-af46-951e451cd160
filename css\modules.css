/* ===================================
   Module-specific CSS - Biosignal Virtual Lab

   Author: Dr. <PERSON>smail
   Institution: SUST - BME
   Year: 2025
   Contact: <EMAIL>

   Copyright © 2025 Dr. <PERSON> Yagoub Esmail
   =================================== */

/* Page Layout */
.main-content {
    padding-top: 100px;
    min-height: 100vh;
    background: var(--bg-secondary);
}

.page-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
    padding: var(--spacing-2xl) 0;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
}

.page-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.page-title i {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
}

.page-title h1 {
    margin: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-description {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Breadcrumbs */
.breadcrumbs-container {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
    z-index: var(--z-sticky);
}

.breadcrumbs {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    font-size: var(--font-size-sm);
}

.breadcrumb-separator {
    color: var(--text-tertiary);
}

.breadcrumb-current {
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
}

/* Processing Pipeline */
.processing-pipeline {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-3xl);
    padding: var(--spacing-xl);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    flex-wrap: wrap;
}

.pipeline-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
    min-width: 120px;
}

.pipeline-step.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    transform: scale(1.05);
}

.pipeline-step:not(.active):hover {
    background: var(--bg-secondary);
    transform: translateY(-2px);
}

.step-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    background: rgba(255, 255, 255, 0.2);
}

.pipeline-step:not(.active) .step-icon {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
}

.step-content h3 {
    margin: 0;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
}

.step-content p {
    margin: 0;
    font-size: var(--font-size-sm);
    opacity: 0.8;
}

.pipeline-arrow {
    font-size: var(--font-size-xl);
    color: var(--text-tertiary);
    font-weight: bold;
}

/* Processing Interface */
.processing-interface {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-3xl);
}

/* Control Panel */
.control-panel {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.control-section {
    border-bottom: 1px solid var(--border-light);
    transition: all var(--transition-normal);
}

.control-section:last-child {
    border-bottom: none;
}

.control-section h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    margin: 0;
    background: var(--bg-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.control-section h3:hover {
    background: var(--bg-tertiary);
}

.control-section h3 i {
    color: var(--primary-color);
}

.control-section > div {
    padding: var(--spacing-lg);
    display: none;
}

.control-section.active > div {
    display: block;
}

/* Input Tabs */
.input-tabs {
    display: flex;
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.tab-btn {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-btn:hover {
    background: var(--bg-secondary);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--border-medium);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
    margin-bottom: var(--spacing-lg);
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-color);
    background: var(--bg-secondary);
}

.upload-icon {
    font-size: var(--font-size-3xl);
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-md);
}

.upload-area h4 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.upload-area p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

/* File Settings */
.file-settings {
    display: grid;
    gap: var(--spacing-md);
}

/* Sample Datasets */
.sample-datasets {
    display: grid;
    gap: var(--spacing-md);
}

.dataset-card {
    padding: var(--spacing-md);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.dataset-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.dataset-card h4 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.dataset-card p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.dataset-info {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.info-tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* Synthetic Controls */
.synthetic-controls {
    display: grid;
    gap: var(--spacing-md);
}

/* Filter Controls */
.filter-controls {
    display: grid;
    gap: var(--spacing-lg);
}

.filter-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-top: var(--spacing-sm);
}

.range-input label {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

/* Processing Actions */
.processing-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* Quality Metrics */
.quality-metrics {
    display: grid;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.metric-card {
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    text-align: center;
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.metric-value {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.metric-bar {
    height: 4px;
    background: var(--border-light);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--error-color), var(--warning-color), var(--success-color));
    width: 0%;
    transition: width var(--transition-normal);
}

/* Export Options */
.export-options {
    display: grid;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.export-checkboxes {
    display: grid;
    gap: var(--spacing-sm);
}

.export-actions {
    display: flex;
    gap: var(--spacing-md);
}

/* Visualization Panel */
.visualization-panel {
    display: grid;
    gap: var(--spacing-xl);
}

.chart-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-light);
}

.chart-header h3 {
    margin: 0;
    color: var(--text-primary);
}

.chart-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.chart-area {
    padding: var(--spacing-lg);
    position: relative;
}

.chart-area canvas {
    width: 100%;
    height: auto;
    border-radius: var(--radius-md);
}

.chart-info {
    display: flex;
    justify-content: space-around;
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-light);
}

.info-item {
    text-align: center;
}

.info-label {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.info-value {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-xl);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
}

/* Algorithm Overview */
.algorithm-overview {
    margin-bottom: var(--spacing-3xl);
    padding: var(--spacing-2xl);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
}

.algorithm-overview h2 {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    color: var(--text-primary);
}

.algorithm-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.algorithm-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.algorithm-step:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.step-number {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-md);
}

.algorithm-step h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.algorithm-step p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Detection Interface */
.detection-interface {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-3xl);
}

/* Parameter Controls */
.parameter-controls {
    display: grid;
    gap: var(--spacing-lg);
}

.form-range {
    width: 100%;
    height: 6px;
    border-radius: var(--radius-sm);
    background: var(--bg-secondary);
    outline: none;
    -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: var(--radius-full);
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.form-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: var(--radius-full);
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-sm);
}

.range-labels {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.form-help {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    font-style: italic;
}

/* Detection Actions */
.detection-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* Results Summary */
.results-summary {
    display: grid;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.result-card {
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    text-align: center;
}

.result-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.result-value {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.quality-bar {
    height: 6px;
    background: var(--border-light);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.quality-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--error-color), var(--warning-color), var(--success-color));
    width: 0%;
    transition: width var(--transition-normal);
}

/* Validation Controls */
.validation-controls {
    margin-top: var(--spacing-lg);
}

.validation-controls h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.validation-options {
    display: grid;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

/* Manual Correction */
.correction-tools {
    display: grid;
    gap: var(--spacing-lg);
}

.tool-buttons {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.correction-info {
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.correction-info p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.correction-stats {
    display: flex;
    gap: var(--spacing-lg);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.correction-stats span {
    font-weight: var(--font-weight-medium);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .processing-interface,
    .detection-interface {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .control-panel {
        order: 2;
    }

    .visualization-panel {
        order: 1;
    }

    .algorithm-steps {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .processing-pipeline {
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .pipeline-arrow {
        transform: rotate(90deg);
    }

    .filter-range {
        grid-template-columns: 1fr;
    }

    .processing-actions,
    .export-actions,
    .detection-actions {
        flex-direction: column;
    }

    .action-buttons {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .chart-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .chart-info {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .algorithm-steps {
        grid-template-columns: 1fr;
    }

    .tool-buttons {
        justify-content: center;
    }

    .correction-stats {
        justify-content: space-around;
    }
}

/* HRV Analysis Specific Styles */
.hrv-overview {
    margin-bottom: var(--spacing-3xl);
    padding: var(--spacing-2xl);
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
}

.hrv-overview h2 {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    color: var(--text-primary);
}

.domain-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.domain-card {
    padding: var(--spacing-xl);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-light);
    transition: all var(--transition-normal);
}

.domain-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.domain-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-2xl);
}

.domain-card h3 {
    text-align: center;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.domain-card p {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--text-secondary);
}

.domain-features {
    list-style: none;
    padding: 0;
}

.domain-features li {
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    position: relative;
    padding-left: var(--spacing-lg);
}

.domain-features li::before {
    content: "•";
    color: var(--primary-color);
    font-weight: var(--font-weight-bold);
    position: absolute;
    left: 0;
}

/* Analysis Interface */
.analysis-interface {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-3xl);
}

/* Analysis Settings */
.analysis-settings {
    display: grid;
    gap: var(--spacing-lg);
}

.range-inputs {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.range-inputs span {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.filter-options {
    display: grid;
    gap: var(--spacing-sm);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-color);
}

/* Results Grid */
.results-grid {
    display: grid;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.result-item {
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
    transition: all var(--transition-fast);
}

.result-item:hover {
    background: var(--bg-tertiary);
    transform: translateX(4px);
}

.result-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-weight: var(--font-weight-medium);
}

.result-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.result-description {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    line-height: var(--line-height-tight);
}

/* Interpretation Panel */
.interpretation-panel {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.interpretation-panel h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.interpretation-panel h4::before {
    content: "💡";
    font-size: var(--font-size-lg);
}

.interpretation-content {
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
}

/* Frequency Bands Chart */
.frequency-bands {
    margin-top: var(--spacing-lg);
}

.frequency-bands h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    text-align: center;
}

.band-chart {
    display: flex;
    justify-content: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

/* Export Checkboxes */
.export-checkboxes {
    display: grid;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

/* Analysis Actions */
.analysis-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* Responsive Design for HRV */
@media (max-width: 1024px) {
    .analysis-interface {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .domain-cards {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .domain-cards {
        gap: var(--spacing-lg);
    }

    .domain-card {
        padding: var(--spacing-lg);
    }

    .range-inputs {
        flex-direction: column;
        align-items: stretch;
    }

    .analysis-actions,
    .export-actions {
        flex-direction: column;
    }

    .results-grid {
        gap: var(--spacing-sm);
    }

    .result-item {
        padding: var(--spacing-sm);
    }

    .result-value {
        font-size: var(--font-size-xl);
    }
}
