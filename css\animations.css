/* ===================================
   Animations CSS - Biosignal Virtual Lab
   =================================== */

/* Hero Section Styles */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  overflow: hidden;
  padding-top: 80px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
  z-index: 1;
}

.ecg-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 200px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 200"><path d="M0,100 L100,100 L120,50 L140,150 L160,20 L180,100 L1000,100" stroke="%232563eb" stroke-width="2" fill="none" opacity="0.3"/></svg>') repeat-x;
  animation: ecgPulse 3s ease-in-out infinite;
}

@keyframes ecgPulse {
  0%, 100% { opacity: 0.1; transform: translate(-50%, -50%) scaleY(1); }
  50% { opacity: 0.3; transform: translate(-50%, -50%) scaleY(1.2); }
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.hero-text {
  animation: slideInLeft 1s ease-out;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.hero-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
  line-height: var(--line-height-relaxed);
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.hero-visual {
  animation: slideInRight 1s ease-out;
}

.platform-preview {
  position: relative;
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-lg);
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
  transition: transform var(--transition-slow);
}

.platform-preview:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

.preview-screen {
  background: var(--gray-900);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  position: relative;
  overflow: hidden;
}

#heroECGCanvas {
  width: 100%;
  height: auto;
  border-radius: var(--radius-md);
}

/* Section Animations */
.features {
  padding: var(--spacing-3xl) 0;
  background: var(--bg-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
}

.feature-card {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

.platform-access {
  padding: var(--spacing-3xl) 0;
  background: var(--bg-primary);
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.quick-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.about {
  padding: var(--spacing-3xl) 0;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.about-features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.about-feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.about-feature:hover {
  transform: translateX(10px);
  box-shadow: var(--shadow-md);
}

.about-feature i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
  min-width: 40px;
}

.about-feature h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.about-feature p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
}

/* Footer */
.footer {
  background: var(--gray-900);
  color: var(--gray-300);
  padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: white;
  margin-bottom: var(--spacing-lg);
}

.footer-brand i {
  font-size: var(--font-size-2xl);
  color: var(--accent-color);
}

.footer-section h4 {
  color: white;
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-lg);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: var(--gray-400);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: white;
  text-decoration: none;
}

.footer-social {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--gray-800);
  border-radius: var(--radius-md);
  color: var(--gray-400);
  transition: all var(--transition-fast);
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  text-decoration: none;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--gray-800);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.footer-bottom-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-bottom-links a {
  color: var(--gray-400);
  font-size: var(--font-size-sm);
}

/* Keyframe Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-10px); }
  70% { transform: translateY(-5px); }
  90% { transform: translateY(-2px); }
}

/* Scroll Animations */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.scroll-animate.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Hover Effects */
.hover-lift {
  transition: transform var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Responsive Animations */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-2xl);
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }
  
  .about-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
  
  .platform-preview {
    transform: none;
  }
}

@media (max-width: 480px) {
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .about-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .module-grid {
    grid-template-columns: 1fr;
  }
}
