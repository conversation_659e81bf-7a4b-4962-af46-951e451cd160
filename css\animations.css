/* ===================================
   Animations CSS - Biosignal Virtual Lab

   Author: Dr. <PERSON>il
   Institution: SUST - BME
   Year: 2025
   Contact: <EMAIL>

   Copyright © 2025 Dr. <PERSON> Ya<PERSON>ub Esmail
   =================================== */

/* Enhanced Hero Section Styles */
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 25%, #ec4899 50%, #06b6d4 75%, #10b981 100%);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  overflow: hidden;
  padding-top: 80px;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  z-index: 1;
}

.hero-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

/* Particle Animation */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat 8s linear infinite;
}

.particle:nth-child(1) {
  left: 10%;
  animation-delay: 0s;
  animation-duration: 8s;
}

.particle:nth-child(2) {
  left: 20%;
  animation-delay: 1s;
  animation-duration: 10s;
}

.particle:nth-child(3) {
  left: 30%;
  animation-delay: 2s;
  animation-duration: 12s;
}

.particle:nth-child(4) {
  left: 40%;
  animation-delay: 3s;
  animation-duration: 9s;
}

.particle:nth-child(5) {
  left: 60%;
  animation-delay: 4s;
  animation-duration: 11s;
}

.particle:nth-child(6) {
  left: 70%;
  animation-delay: 5s;
  animation-duration: 7s;
}

.particle:nth-child(7) {
  left: 80%;
  animation-delay: 6s;
  animation-duration: 13s;
}

.particle:nth-child(8) {
  left: 90%;
  animation-delay: 7s;
  animation-duration: 9s;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) scale(1);
    opacity: 0;
  }
}

.ecg-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 200px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 200"><path d="M0,100 L100,100 L120,50 L140,150 L160,20 L180,100 L1000,100" stroke="%232563eb" stroke-width="2" fill="none" opacity="0.3"/></svg>') repeat-x;
  animation: ecgPulse 3s ease-in-out infinite;
}

@keyframes ecgPulse {
  0%, 100% { opacity: 0.1; transform: translate(-50%, -50%) scaleY(1); }
  50% { opacity: 0.3; transform: translate(-50%, -50%) scaleY(1.2); }
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.hero-text {
  animation: slideInLeft 1s ease-out;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-lg);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
  0% { filter: brightness(1) drop-shadow(0 0 10px rgba(255, 255, 255, 0.3)); }
  100% { filter: brightness(1.1) drop-shadow(0 0 20px rgba(255, 255, 255, 0.5)); }
}

.hero-description {
  font-size: var(--font-size-xl);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-2xl);
  line-height: var(--line-height-relaxed);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 1s ease-out 0.3s both;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.hero-visual {
  animation: slideInRight 1s ease-out;
}

.platform-preview {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25),
              0 0 0 1px rgba(255, 255, 255, 0.1) inset;
  padding: var(--spacing-xl);
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
  transition: all var(--transition-slow);
}

.platform-preview:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1.02);
  box-shadow: 0 35px 70px -12px rgba(0, 0, 0, 0.3),
              0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

.preview-screen {
  background: var(--gray-900);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  position: relative;
  overflow: hidden;
}

#heroECGCanvas {
  width: 100%;
  height: auto;
  border-radius: var(--radius-md);
}

/* Enhanced Section Animations */
.features {
  padding: var(--spacing-3xl) 0;
  background: var(--bg-gradient-secondary);
  position: relative;
}

.features::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 50%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
}

.feature-card {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

.platform-access {
  padding: var(--spacing-3xl) 0;
  background: var(--bg-primary);
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-2xl);
}

.quick-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.about {
  padding: var(--spacing-3xl) 0;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.about-features {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.about-feature {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.about-feature:hover {
  transform: translateX(10px);
  box-shadow: var(--shadow-md);
}

.about-feature i {
  font-size: var(--font-size-2xl);
  color: var(--primary-color);
  min-width: 40px;
}

.about-feature h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.about-feature p {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
}

/* Footer */
.footer {
  background: var(--gray-900);
  color: var(--gray-300);
  padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: white;
  margin-bottom: var(--spacing-lg);
}

.footer-brand i {
  font-size: var(--font-size-2xl);
  color: var(--accent-color);
}

.author-info {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--accent-color);
}

.author-info p {
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--gray-300);
}

.author-info strong {
  color: var(--accent-color);
  font-weight: var(--font-weight-semibold);
}

.footer-section h4 {
  color: white;
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-size-lg);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: var(--gray-400);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: white;
  text-decoration: none;
}

.footer-social {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--gray-800);
  border-radius: var(--radius-md);
  color: var(--gray-400);
  transition: all var(--transition-fast);
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  text-decoration: none;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--gray-800);
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.footer-bottom-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-bottom-links a {
  color: var(--gray-400);
  font-size: var(--font-size-sm);
}

/* Keyframe Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
  40%, 43% { transform: translateY(-10px); }
  70% { transform: translateY(-5px); }
  90% { transform: translateY(-2px); }
}

/* Modern Animation Effects */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(99, 102, 241, 0.5); }
  50% { box-shadow: 0 0 20px rgba(99, 102, 241, 0.8), 0 0 30px rgba(99, 102, 241, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes slideInFromLeft {
  0% { transform: translateX(-100%); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
  0% { transform: translateX(100%); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0.8); opacity: 0; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes rotateIn {
  0% { transform: rotate(-180deg) scale(0.8); opacity: 0; }
  100% { transform: rotate(0deg) scale(1); opacity: 1; }
}

/* Utility Animation Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

.animate-rotate-in {
  animation: rotateIn 0.8s ease-out;
}

/* Scroll Animations */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.scroll-animate.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Hover Effects */
.hover-lift {
  transition: transform var(--transition-normal);
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-scale {
  transition: transform var(--transition-normal);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Responsive Animations */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-2xl);
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }
  
  .about-stats {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md);
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
  
  .platform-preview {
    transform: none;
  }
}

@media (max-width: 480px) {
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .about-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .module-grid {
    grid-template-columns: 1fr;
  }
}
